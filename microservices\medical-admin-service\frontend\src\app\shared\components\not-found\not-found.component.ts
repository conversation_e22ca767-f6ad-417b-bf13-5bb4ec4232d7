import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-not-found',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="not-found-container">
      <div class="not-found-content">
        <div class="error-code">404</div>
        <h1 class="error-title">Page non trouvée</h1>
        <p class="error-message">
          Désolé, la page que vous recherchez n'existe pas ou a été déplacée.
        </p>
        <div class="error-actions">
          <a routerLink="/dashboard" class="btn btn-primary">
            <i class="fas fa-home me-2"></i>
            Retour au tableau de bord
          </a>
          <button class="btn btn-outline-secondary ms-2" (click)="goBack()">
            <i class="fas fa-arrow-left me-2"></i>
            Retour
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .not-found-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 60vh;
      padding: 2rem;
    }
    
    .not-found-content {
      text-align: center;
      max-width: 500px;
    }
    
    .error-code {
      font-size: 8rem;
      font-weight: 700;
      color: var(--primary-color);
      line-height: 1;
      margin-bottom: 1rem;
    }
    
    .error-title {
      font-size: 2rem;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 1rem;
    }
    
    .error-message {
      font-size: 1.125rem;
      color: var(--text-secondary);
      margin-bottom: 2rem;
      line-height: 1.6;
    }
    
    .error-actions {
      display: flex;
      justify-content: center;
      gap: 1rem;
      flex-wrap: wrap;
    }
    
    @media (max-width: 768px) {
      .error-code {
        font-size: 6rem;
      }
      
      .error-title {
        font-size: 1.5rem;
      }
      
      .error-message {
        font-size: 1rem;
      }
      
      .error-actions {
        flex-direction: column;
        align-items: center;
      }
      
      .error-actions .btn {
        width: 100%;
        max-width: 200px;
      }
    }
  `]
})
export class NotFoundComponent {
  goBack(): void {
    window.history.back();
  }
}
