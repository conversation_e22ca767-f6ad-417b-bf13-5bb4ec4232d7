package com.sprintbot.communication.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * Entité représentant une réaction (emoji) à un message
 */
@Entity
@Table(name = "reactions_message",
       uniqueConstraints = @UniqueConstraint(columnNames = {"message_id", "utilisateur_id", "emoji"}))
@EntityListeners(AuditingEntityListener.class)
public class ReactionMessage {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "L'ID de l'utilisateur est obligatoire")
    @Column(name = "utilisateur_id", nullable = false)
    private Long utilisateurId;

    @NotBlank(message = "L'emoji est obligatoire")
    @Size(max = 10, message = "L'emoji ne peut pas dépasser 10 caractères")
    @Column(name = "emoji", nullable = false, length = 10)
    private String emoji;

    @CreatedDate
    @Column(name = "date_reaction", nullable = false)
    private LocalDateTime dateReaction;

    // Relations
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "message_id", nullable = false)
    private Message message;

    // Constructeurs
    public ReactionMessage() {}

    public ReactionMessage(Message message, Long utilisateurId, String emoji) {
        this.message = message;
        this.utilisateurId = utilisateurId;
        this.emoji = emoji;
    }

    // Getters et Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUtilisateurId() {
        return utilisateurId;
    }

    public void setUtilisateurId(Long utilisateurId) {
        this.utilisateurId = utilisateurId;
    }

    public String getEmoji() {
        return emoji;
    }

    public void setEmoji(String emoji) {
        this.emoji = emoji;
    }

    public LocalDateTime getDateReaction() {
        return dateReaction;
    }

    public void setDateReaction(LocalDateTime dateReaction) {
        this.dateReaction = dateReaction;
    }

    public Message getMessage() {
        return message;
    }

    public void setMessage(Message message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return "ReactionMessage{" +
                "id=" + id +
                ", utilisateurId=" + utilisateurId +
                ", emoji='" + emoji + '\'' +
                ", dateReaction=" + dateReaction +
                '}';
    }
}
