package com.sprintbot.communication.entity;

/**
 * Énumération des statuts de notification
 */
public enum StatutNotification {
    EN_ATTENTE,     // En attente d'envoi
    PROGRAMMEE,     // Programmée pour envoi ultérieur
    ENVOYE,         // Envoyée avec succès
    LU,             // Lue par le destinataire
    ERREUR,         // Erreur lors de l'envoi
    ANNULEE,        // Annulée
    EXPIREE         // Expirée
}
