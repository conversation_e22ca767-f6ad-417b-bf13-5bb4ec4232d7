package com.sprintbot.communication.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Entité représentant un message dans une conversation
 */
@Entity
@Table(name = "messages")
@EntityListeners(AuditingEntityListener.class)
public class Message {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "L'ID de l'expéditeur est obligatoire")
    @Column(name = "expediteur_id", nullable = false)
    private Long expediteurId;

    @NotBlank(message = "Le contenu du message est obligatoire")
    @Size(max = 4000, message = "Le contenu ne peut pas dépasser 4000 caractères")
    @Column(name = "contenu", nullable = false, length = 4000)
    private String contenu;

    @NotNull(message = "Le type de message est obligatoire")
    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, length = 20)
    private TypeMessage type = TypeMessage.TEXTE;

    @NotNull(message = "Le statut est obligatoire")
    @Enumerated(EnumType.STRING)
    @Column(name = "statut", nullable = false, length = 20)
    private StatutMessage statut = StatutMessage.ENVOYE;

    @Column(name = "fichier_url")
    private String fichierUrl;

    @Column(name = "fichier_nom")
    private String fichierNom;

    @Column(name = "fichier_taille")
    private Long fichierTaille;

    @Column(name = "fichier_type")
    private String fichierType;

    @Column(name = "est_modifie")
    private Boolean estModifie = false;

    @Column(name = "date_modification_contenu")
    private LocalDateTime dateModificationContenu;

    @Column(name = "est_epingle")
    private Boolean estEpingle = false;

    @Column(name = "nombre_reactions")
    private Integer nombreReactions = 0;

    @Column(name = "metadata", columnDefinition = "TEXT")
    private String metadata; // JSON pour données supplémentaires

    @CreatedDate
    @Column(name = "date_envoi", nullable = false)
    private LocalDateTime dateEnvoi;

    @LastModifiedDate
    @Column(name = "date_modification")
    private LocalDateTime dateModification;

    // Relations
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "conversation_id", nullable = false)
    private Conversation conversation;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "reponse_a_id")
    private Message reponseA;

    @OneToMany(mappedBy = "reponseA", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Message> reponses = new ArrayList<>();

    @OneToMany(mappedBy = "message", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ReactionMessage> reactions = new ArrayList<>();

    @OneToMany(mappedBy = "message", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<LectureMessage> lectures = new ArrayList<>();

    // Constructeurs
    public Message() {}

    public Message(Long expediteurId, String contenu, Conversation conversation) {
        this.expediteurId = expediteurId;
        this.contenu = contenu;
        this.conversation = conversation;
        this.type = TypeMessage.TEXTE;
        this.statut = StatutMessage.ENVOYE;
    }

    public Message(Long expediteurId, String contenu, TypeMessage type, Conversation conversation) {
        this.expediteurId = expediteurId;
        this.contenu = contenu;
        this.type = type;
        this.conversation = conversation;
        this.statut = StatutMessage.ENVOYE;
    }

    public Message(Conversation conversation, Long expediteurId, String contenu, TypeMessage type) {
        this.conversation = conversation;
        this.expediteurId = expediteurId;
        this.contenu = contenu;
        this.type = type;
        this.statut = StatutMessage.ENVOYE;
    }

    // Méthodes métier
    public void modifier(String nouveauContenu) {
        this.contenu = nouveauContenu;
        this.estModifie = true;
        this.dateModificationContenu = LocalDateTime.now();
    }

    public void epingler() {
        this.estEpingle = true;
    }

    public void desepingler() {
        this.estEpingle = false;
    }

    public void marquerCommeLu(Long utilisateurId) {
        boolean dejaLu = this.lectures.stream()
                .anyMatch(l -> l.getUtilisateurId().equals(utilisateurId));
        
        if (!dejaLu) {
            LectureMessage lecture = new LectureMessage(this, utilisateurId);
            this.lectures.add(lecture);
        }
    }

    public void ajouterReaction(Long utilisateurId, String emoji) {
        // Vérifier si l'utilisateur a déjà réagi avec ce même emoji
        boolean dejaReagi = this.reactions.stream()
                .anyMatch(r -> r.getUtilisateurId().equals(utilisateurId) && 
                              r.getEmoji().equals(emoji));
        
        if (!dejaReagi) {
            ReactionMessage reaction = new ReactionMessage(this, utilisateurId, emoji);
            this.reactions.add(reaction);
            this.nombreReactions = this.reactions.size();
        }
    }

    public void supprimerReaction(Long utilisateurId, String emoji) {
        this.reactions.removeIf(r -> r.getUtilisateurId().equals(utilisateurId) && 
                                    r.getEmoji().equals(emoji));
        this.nombreReactions = this.reactions.size();
    }

    public boolean estReponse() {
        return this.reponseA != null;
    }

    public void setReponseAId(Long reponseAId) {
        // Cette méthode sera utilisée pour définir l'ID du message auquel on répond
        // L'entité reponseA sera chargée par JPA si nécessaire
        if (reponseAId != null) {
            Message messageReponse = new Message();
            messageReponse.setId(reponseAId);
            this.reponseA = messageReponse;
        } else {
            this.reponseA = null;
        }
    }

    public void supprimer() {
        // Marquer le message comme supprimé (soft delete)
        this.contenu = "[Message supprimé]";
        this.estModifie = true;
        this.dateModificationContenu = LocalDateTime.now();
    }

    public boolean aDesReponses() {
        return !this.reponses.isEmpty();
    }

    public boolean estFichier() {
        return this.type == TypeMessage.FICHIER || 
               this.type == TypeMessage.IMAGE || 
               this.type == TypeMessage.VIDEO ||
               this.type == TypeMessage.AUDIO;
    }

    public boolean estSysteme() {
        return this.type == TypeMessage.SYSTEME;
    }

    public int getNombreLectures() {
        return this.lectures.size();
    }

    public boolean estLuPar(Long utilisateurId) {
        return this.lectures.stream()
                .anyMatch(l -> l.getUtilisateurId().equals(utilisateurId));
    }

    // Getters et Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getExpediteurId() {
        return expediteurId;
    }

    public void setExpediteurId(Long expediteurId) {
        this.expediteurId = expediteurId;
    }

    public String getContenu() {
        return contenu;
    }

    public void setContenu(String contenu) {
        this.contenu = contenu;
    }

    public TypeMessage getType() {
        return type;
    }

    public void setType(TypeMessage type) {
        this.type = type;
    }

    public StatutMessage getStatut() {
        return statut;
    }

    public void setStatut(StatutMessage statut) {
        this.statut = statut;
    }

    public String getFichierUrl() {
        return fichierUrl;
    }

    public void setFichierUrl(String fichierUrl) {
        this.fichierUrl = fichierUrl;
    }

    public String getFichierNom() {
        return fichierNom;
    }

    public void setFichierNom(String fichierNom) {
        this.fichierNom = fichierNom;
    }

    public Long getFichierTaille() {
        return fichierTaille;
    }

    public void setFichierTaille(Long fichierTaille) {
        this.fichierTaille = fichierTaille;
    }

    public String getFichierType() {
        return fichierType;
    }

    public void setFichierType(String fichierType) {
        this.fichierType = fichierType;
    }

    public Boolean getEstModifie() {
        return estModifie;
    }

    public void setEstModifie(Boolean estModifie) {
        this.estModifie = estModifie;
    }

    public LocalDateTime getDateModificationContenu() {
        return dateModificationContenu;
    }

    public void setDateModificationContenu(LocalDateTime dateModificationContenu) {
        this.dateModificationContenu = dateModificationContenu;
    }

    public Boolean getEstEpingle() {
        return estEpingle;
    }

    public void setEstEpingle(Boolean estEpingle) {
        this.estEpingle = estEpingle;
    }

    public Integer getNombreReactions() {
        return nombreReactions;
    }

    public void setNombreReactions(Integer nombreReactions) {
        this.nombreReactions = nombreReactions;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public LocalDateTime getDateEnvoi() {
        return dateEnvoi;
    }

    public void setDateEnvoi(LocalDateTime dateEnvoi) {
        this.dateEnvoi = dateEnvoi;
    }

    public LocalDateTime getDateModification() {
        return dateModification;
    }

    public void setDateModification(LocalDateTime dateModification) {
        this.dateModification = dateModification;
    }

    public Conversation getConversation() {
        return conversation;
    }

    public void setConversation(Conversation conversation) {
        this.conversation = conversation;
    }

    public Message getReponseA() {
        return reponseA;
    }

    public void setReponseA(Message reponseA) {
        this.reponseA = reponseA;
    }

    public List<Message> getReponses() {
        return reponses;
    }

    public void setReponses(List<Message> reponses) {
        this.reponses = reponses;
    }

    public List<ReactionMessage> getReactions() {
        return reactions;
    }

    public void setReactions(List<ReactionMessage> reactions) {
        this.reactions = reactions;
    }

    public List<LectureMessage> getLectures() {
        return lectures;
    }

    public void setLectures(List<LectureMessage> lectures) {
        this.lectures = lectures;
    }

    @Override
    public String toString() {
        return "Message{" +
                "id=" + id +
                ", expediteurId=" + expediteurId +
                ", type=" + type +
                ", statut=" + statut +
                ", estModifie=" + estModifie +
                ", nombreReactions=" + nombreReactions +
                ", dateEnvoi=" + dateEnvoi +
                '}';
    }
}
