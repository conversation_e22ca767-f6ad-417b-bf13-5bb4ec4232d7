{"name": "medical-admin-frontend", "version": "1.0.0", "description": "Frontend Angular pour le microservice Medical Admin Service", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build:prod": "ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "serve:dev": "ng serve --configuration development --port 4203", "serve:docker": "ng serve --host 0.0.0.0 --port 4203 --disable-host-check"}, "private": true, "dependencies": {"@angular/animations": "^17.0.0", "@angular/common": "^17.0.0", "@angular/compiler": "^17.0.0", "@angular/core": "^17.0.0", "@angular/forms": "^17.0.0", "@angular/platform-browser": "^17.0.0", "@angular/platform-browser-dynamic": "^17.0.0", "@angular/router": "^17.0.0", "@angular/cdk": "^17.0.0", "@angular/material": "^17.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.0", "bootstrap": "^5.3.0", "@ng-bootstrap/ng-bootstrap": "^16.0.0", "ngx-toastr": "^18.0.0", "chart.js": "^4.4.0", "ng2-charts": "^5.0.0", "date-fns": "^2.30.0", "@fortawesome/fontawesome-free": "^6.5.0"}, "devDependencies": {"@angular-devkit/build-angular": "^17.0.0", "@angular/cli": "^17.0.0", "@angular/compiler-cli": "^17.0.0", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-headless": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.50.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}