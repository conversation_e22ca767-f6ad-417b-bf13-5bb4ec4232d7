# Configuration d'environnement pour Discovery Service (Eureka Server)
# SprintBot - Service de découverte pour l'écosystème microservices
# 
# Copiez ce fichier vers .env et modifiez les valeurs selon votre environnement

# ================================
# Configuration Spring Boot
# ================================

# Profil Spring actif (dev, docker, prod)
SPRING_PROFILES_ACTIVE=docker

# Port du serveur
SERVER_PORT=8761

# ================================
# Configuration Eureka Server
# ================================

# Hostname de l'instance Eureka
EUREKA_INSTANCE_HOSTNAME=discovery-service

# URL du service Eureka (pour la réplication peer-to-peer)
EUREKA_CLIENT_SERVICE_URL=http://discovery-service:8761/eureka/

# Auto-enregistrement (false pour le serveur Eureka)
EUREKA_CLIENT_REGISTER_WITH_EUREKA=false

# Récupération du registry (false pour le serveur Eureka)
EUREKA_CLIENT_FETCH_REGISTRY=false

# Mode de préservation automatique
EUREKA_SERVER_ENABLE_SELF_PRESERVATION=true

# Intervalle d'éviction des services inactifs (ms)
EUREKA_SERVER_EVICTION_INTERVAL=60000

# Seuil de renouvellement pour la préservation automatique
EUREKA_SERVER_RENEWAL_THRESHOLD=0.85

# ================================
# Configuration du Dashboard Eureka
# ================================

# Activation du dashboard
EUREKA_DASHBOARD_ENABLED=true

# Authentification du dashboard
EUREKA_DASHBOARD_USERNAME=admin
EUREKA_DASHBOARD_PASSWORD=admin123

# ================================
# Configuration de sécurité
# ================================

# Utilisateur Spring Security
SPRING_SECURITY_USER_NAME=admin
SPRING_SECURITY_USER_PASSWORD=admin123
SPRING_SECURITY_USER_ROLES=ADMIN

# ================================
# Configuration JVM
# ================================

# Options JVM pour l'optimisation
JAVA_OPTS=-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0

# ================================
# Configuration du monitoring
# ================================

# Endpoints Actuator exposés
MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE=health,info,metrics,env,prometheus

# Niveau de détail pour le health check
MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS=always

# Activation des métriques Prometheus
MANAGEMENT_METRICS_EXPORT_PROMETHEUS_ENABLED=true

# ================================
# Configuration des logs
# ================================

# Niveau de log global
LOGGING_LEVEL_ROOT=INFO

# Niveau de log pour SprintBot
LOGGING_LEVEL_SPRINTBOT=INFO

# Niveau de log pour Eureka
LOGGING_LEVEL_EUREKA=INFO

# Niveau de log pour Discovery
LOGGING_LEVEL_DISCOVERY=INFO

# Niveau de log pour Spring Security
LOGGING_LEVEL_SECURITY=INFO

# Niveau de log pour Spring Web
LOGGING_LEVEL_WEB=INFO

# Fichier de log
LOGGING_FILE_NAME=/app/logs/discovery-service.log

# ================================
# Configuration SSL (Production)
# ================================

# Activation SSL
SSL_ENABLED=false

# Keystore SSL
SSL_KEY_STORE=
SSL_KEY_STORE_PASSWORD=
SSL_KEY_STORE_TYPE=PKCS12

# ================================
# Configuration réseau avancée
# ================================

# Timeout de lecture peer-to-peer (ms)
EUREKA_SERVER_PEER_NODE_READ_TIMEOUT=1000

# Timeout de connexion peer-to-peer (ms)
EUREKA_SERVER_PEER_NODE_CONNECT_TIMEOUT=2000

# Nombre total de connexions peer-to-peer
EUREKA_SERVER_PEER_NODE_TOTAL_CONNECTIONS=1000

# Connexions par host peer-to-peer
EUREKA_SERVER_PEER_NODE_TOTAL_CONNECTIONS_PER_HOST=500

# ================================
# Configuration du cache
# ================================

# Expiration automatique du cache de réponse (secondes)
EUREKA_SERVER_RESPONSE_CACHE_AUTO_EXPIRATION=180

# Intervalle de mise à jour du cache (ms)
EUREKA_SERVER_RESPONSE_CACHE_UPDATE_INTERVAL=30000

# Utilisation du cache en lecture seule
EUREKA_SERVER_USE_READ_ONLY_RESPONSE_CACHE=true

# ================================
# Configuration des threads
# ================================

# Threads max pour la réplication de statut
EUREKA_SERVER_MAX_THREADS_STATUS_REPLICATION=1

# Threads max pour la réplication peer-to-peer
EUREKA_SERVER_MAX_THREADS_PEER_REPLICATION=20

# ================================
# Configuration des instances
# ================================

# Intervalle de renouvellement des baux (secondes)
EUREKA_INSTANCE_LEASE_RENEWAL_INTERVAL=30

# Durée d'expiration des baux (secondes)
EUREKA_INSTANCE_LEASE_EXPIRATION_DURATION=90

# Préférer l'adresse IP au hostname
EUREKA_INSTANCE_PREFER_IP_ADDRESS=true

# ================================
# Configuration Docker
# ================================

# Nom du conteneur
CONTAINER_NAME=sprintbot-discovery-service

# Réseau Docker
DOCKER_NETWORK=sprintbot-network

# ================================
# Configuration de développement
# ================================

# Mode debug (pour le profil dev)
DEBUG_MODE=false

# Port de debug JVM
DEBUG_PORT=5005

# ================================
# Configuration de production
# ================================

# Environnement de déploiement
DEPLOYMENT_ENVIRONMENT=production

# Zone de déploiement
DEPLOYMENT_ZONE=primary

# Version de l'application
APP_VERSION=1.0.0

# ================================
# Configuration de sauvegarde
# ================================

# Répertoire de sauvegarde
BACKUP_DIRECTORY=/app/backups

# Rétention des sauvegardes (jours)
BACKUP_RETENTION_DAYS=30

# ================================
# Configuration de notification
# ================================

# Email pour les alertes
ALERT_EMAIL=<EMAIL>

# Webhook pour les notifications
NOTIFICATION_WEBHOOK=

# ================================
# Métadonnées de l'application
# ================================

# Nom de l'équipe
TEAM_NAME=Infrastructure Team

# Contact de l'équipe
TEAM_CONTACT=<EMAIL>

# Description du service
SERVICE_DESCRIPTION=Service de découverte Eureka pour l'écosystème SprintBot

# ================================
# Configuration de test
# ================================

# Mode test
TEST_MODE=false

# Base de données de test
TEST_DATABASE_URL=

# ================================
# Variables personnalisées
# ================================

# Ajoutez ici vos variables d'environnement personnalisées
# CUSTOM_VARIABLE=value
