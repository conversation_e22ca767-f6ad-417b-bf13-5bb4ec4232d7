#!/bin/bash

# Script de validation complète pour Medical Admin Service
# Usage: ./validate-service.sh [--quick] [--full]

set -e

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
QUICK_MODE=false
FULL_MODE=false

# Traitement des arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --quick)
            QUICK_MODE=true
            shift
            ;;
        --full)
            FULL_MODE=true
            shift
            ;;
        *)
            echo "Usage: $0 [--quick] [--full]"
            echo "  --quick: Validation rapide (connectivité uniquement)"
            echo "  --full:  Validation complète (tous les tests)"
            exit 1
            ;;
    esac
done

# Fonctions utilitaires
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_section() {
    echo -e "${PURPLE}[SECTION]${NC} $1"
}

# Validation de l'environnement
validate_environment() {
    log_section "=== Validation de l'environnement ==="
    
    # Vérifier Docker
    if command -v docker &> /dev/null; then
        log_success "✓ Docker installé: $(docker --version | cut -d' ' -f3)"
    else
        log_error "✗ Docker non installé"
        return 1
    fi
    
    # Vérifier Docker Compose
    if command -v docker-compose &> /dev/null; then
        log_success "✓ Docker Compose installé: $(docker-compose --version | cut -d' ' -f3)"
    else
        log_error "✗ Docker Compose non installé"
        return 1
    fi
    
    # Vérifier que Docker fonctionne
    if docker info &> /dev/null; then
        log_success "✓ Docker daemon en cours d'exécution"
    else
        log_error "✗ Docker daemon non accessible"
        return 1
    fi
    
    # Vérifier les ports disponibles
    local ports=(5435 8083 4203)
    for port in "${ports[@]}"; do
        if ! netstat -tuln 2>/dev/null | grep -q ":$port "; then
            log_success "✓ Port $port disponible"
        else
            log_warning "⚠ Port $port déjà utilisé"
        fi
    done
    
    return 0
}

# Validation de la structure du projet
validate_project_structure() {
    log_section "=== Validation de la structure du projet ==="
    
    local required_files=(
        "docker-compose.yml"
        "backend/Dockerfile"
        "backend/pom.xml"
        "backend/src/main/java/com/sprintbot/medicaladmin/MedicalAdminServiceApplication.java"
        "frontend/Dockerfile"
        "frontend/package.json"
        "frontend/src/app/app.component.ts"
        "README.md"
        "DEPLOYMENT.md"
    )
    
    for file in "${required_files[@]}"; do
        if [ -f "$file" ]; then
            log_success "✓ $file"
        else
            log_error "✗ $file manquant"
            return 1
        fi
    done
    
    # Vérifier les répertoires
    local required_dirs=(
        "backend/src/main/java/com/sprintbot/medicaladmin/entity"
        "backend/src/main/java/com/sprintbot/medicaladmin/repository"
        "backend/src/main/java/com/sprintbot/medicaladmin/service"
        "backend/src/main/java/com/sprintbot/medicaladmin/controller"
        "backend/src/main/resources/db/migration"
        "frontend/src/app/core/models"
        "frontend/src/app/core/services"
        "frontend/src/app/shared/components"
        "frontend/src/app/features"
    )
    
    for dir in "${required_dirs[@]}"; do
        if [ -d "$dir" ]; then
            log_success "✓ $dir/"
        else
            log_error "✗ $dir/ manquant"
            return 1
        fi
    done
    
    return 0
}

# Validation de la configuration
validate_configuration() {
    log_section "=== Validation de la configuration ==="
    
    # Vérifier application.yml
    if [ -f "backend/src/main/resources/application.yml" ]; then
        log_success "✓ Configuration backend trouvée"
        
        # Vérifier les profils
        if grep -q "spring.profiles.active" backend/src/main/resources/application.yml; then
            log_success "✓ Profils Spring configurés"
        else
            log_warning "⚠ Profils Spring non configurés"
        fi
    else
        log_error "✗ Configuration backend manquante"
        return 1
    fi
    
    # Vérifier les environnements Angular
    if [ -f "frontend/src/environments/environment.ts" ]; then
        log_success "✓ Configuration frontend développement"
    else
        log_error "✗ Configuration frontend développement manquante"
        return 1
    fi
    
    if [ -f "frontend/src/environments/environment.prod.ts" ]; then
        log_success "✓ Configuration frontend production"
    else
        log_error "✗ Configuration frontend production manquante"
        return 1
    fi
    
    return 0
}

# Validation des dépendances
validate_dependencies() {
    log_section "=== Validation des dépendances ==="
    
    # Vérifier pom.xml
    if [ -f "backend/pom.xml" ]; then
        log_info "Vérification des dépendances Maven..."
        
        local required_deps=(
            "spring-boot-starter-web"
            "spring-boot-starter-data-jpa"
            "spring-boot-starter-security"
            "spring-boot-starter-validation"
            "postgresql"
            "flyway-core"
        )
        
        for dep in "${required_deps[@]}"; do
            if grep -q "$dep" backend/pom.xml; then
                log_success "✓ $dep"
            else
                log_warning "⚠ $dep non trouvé dans pom.xml"
            fi
        done
    fi
    
    # Vérifier package.json
    if [ -f "frontend/package.json" ]; then
        log_info "Vérification des dépendances npm..."
        
        local required_npm_deps=(
            "@angular/core"
            "@angular/common"
            "@angular/router"
            "@angular/forms"
            "bootstrap"
            "rxjs"
        )
        
        for dep in "${required_npm_deps[@]}"; do
            if grep -q "\"$dep\"" frontend/package.json; then
                log_success "✓ $dep"
            else
                log_warning "⚠ $dep non trouvé dans package.json"
            fi
        done
    fi
    
    return 0
}

# Validation du build
validate_build() {
    log_section "=== Validation du build ==="
    
    # Build backend
    log_info "Test de build du backend..."
    if cd backend && mvn clean compile -q && cd ..; then
        log_success "✓ Build backend réussi"
    else
        log_error "✗ Échec du build backend"
        return 1
    fi
    
    # Build frontend (si Node.js est disponible)
    if command -v npm &> /dev/null; then
        log_info "Test de build du frontend..."
        if cd frontend && npm install --silent && npm run build --silent && cd ..; then
            log_success "✓ Build frontend réussi"
        else
            log_error "✗ Échec du build frontend"
            return 1
        fi
    else
        log_warning "⚠ npm non disponible, build frontend ignoré"
    fi
    
    return 0
}

# Validation Docker
validate_docker() {
    log_section "=== Validation Docker ==="
    
    # Vérifier les Dockerfiles
    log_info "Validation des Dockerfiles..."
    
    if [ -f "backend/Dockerfile" ]; then
        if grep -q "FROM eclipse-temurin" backend/Dockerfile; then
            log_success "✓ Dockerfile backend utilise une image Java appropriée"
        else
            log_warning "⚠ Dockerfile backend n'utilise pas eclipse-temurin"
        fi
    fi
    
    if [ -f "frontend/Dockerfile" ]; then
        if grep -q "FROM node" frontend/Dockerfile && grep -q "FROM nginx" frontend/Dockerfile; then
            log_success "✓ Dockerfile frontend utilise un build multi-stage"
        else
            log_warning "⚠ Dockerfile frontend ne semble pas utiliser un build multi-stage"
        fi
    fi
    
    # Test de build des images Docker
    if [ "$FULL_MODE" = true ]; then
        log_info "Test de build des images Docker..."
        
        if docker-compose build --no-cache medical-admin-backend &> /dev/null; then
            log_success "✓ Build image backend réussi"
        else
            log_error "✗ Échec du build image backend"
            return 1
        fi
        
        if docker-compose build --no-cache medical-admin-frontend &> /dev/null; then
            log_success "✓ Build image frontend réussi"
        else
            log_error "✗ Échec du build image frontend"
            return 1
        fi
    fi
    
    return 0
}

# Validation des services en cours d'exécution
validate_running_services() {
    log_section "=== Validation des services en cours d'exécution ==="
    
    # Vérifier si les services sont démarrés
    local services=(
        "medical-admin-db:5435"
        "medical-admin-backend:8083"
        "medical-admin-frontend:4203"
    )
    
    for service in "${services[@]}"; do
        local name=$(echo $service | cut -d':' -f1)
        local port=$(echo $service | cut -d':' -f2)
        
        if docker-compose ps | grep -q "$name.*Up"; then
            log_success "✓ Service $name en cours d'exécution"
            
            # Test de connectivité
            if [ "$name" = "medical-admin-backend" ]; then
                if curl -f "http://localhost:$port/actuator/health" &> /dev/null; then
                    log_success "✓ Backend accessible sur le port $port"
                else
                    log_warning "⚠ Backend non accessible sur le port $port"
                fi
            elif [ "$name" = "medical-admin-frontend" ]; then
                if curl -f "http://localhost:$port/health" &> /dev/null; then
                    log_success "✓ Frontend accessible sur le port $port"
                else
                    log_warning "⚠ Frontend non accessible sur le port $port"
                fi
            fi
        else
            log_warning "⚠ Service $name non démarré"
        fi
    done
    
    return 0
}

# Validation de la base de données
validate_database() {
    log_section "=== Validation de la base de données ==="
    
    # Vérifier la connectivité à la base
    if docker-compose exec -T medical-admin-db pg_isready -U medical_admin_user -d medical_admin_db &> /dev/null; then
        log_success "✓ Base de données accessible"
        
        # Vérifier le schéma
        if docker-compose exec -T medical-admin-db psql -U medical_admin_user -d medical_admin_db -c "\dn" | grep -q "medical_admin"; then
            log_success "✓ Schéma medical_admin existe"
        else
            log_warning "⚠ Schéma medical_admin non trouvé"
        fi
        
        # Vérifier les tables principales
        local tables=("donnees_sante" "rendez_vous_medical" "demande_administrative")
        for table in "${tables[@]}"; do
            if docker-compose exec -T medical-admin-db psql -U medical_admin_user -d medical_admin_db -c "\dt medical_admin.$table" | grep -q "$table"; then
                log_success "✓ Table $table existe"
            else
                log_warning "⚠ Table $table non trouvée"
            fi
        done
    else
        log_warning "⚠ Base de données non accessible"
    fi
    
    return 0
}

# Exécution des tests d'intégration
run_integration_tests() {
    log_section "=== Tests d'intégration ==="
    
    if [ -f "test-integration.sh" ]; then
        log_info "Exécution des tests d'intégration..."
        if bash test-integration.sh --verbose; then
            log_success "✓ Tests d'intégration réussis"
        else
            log_error "✗ Échec des tests d'intégration"
            return 1
        fi
    else
        log_warning "⚠ Script de tests d'intégration non trouvé"
    fi
    
    return 0
}

# Validation de la sécurité
validate_security() {
    log_section "=== Validation de la sécurité ==="
    
    # Vérifier les configurations de sécurité
    if grep -r "password.*=" backend/src/main/resources/ | grep -v "placeholder"; then
        log_warning "⚠ Mots de passe en dur détectés dans la configuration"
    else
        log_success "✓ Pas de mots de passe en dur dans la configuration"
    fi
    
    # Vérifier les utilisateurs non-root dans les Dockerfiles
    if grep -q "USER.*appuser" backend/Dockerfile && grep -q "USER.*appuser" frontend/Dockerfile; then
        log_success "✓ Utilisateurs non-root configurés dans les Dockerfiles"
    else
        log_warning "⚠ Utilisateurs root utilisés dans les Dockerfiles"
    fi
    
    return 0
}

# Génération du rapport de validation
generate_report() {
    log_section "=== Génération du rapport ==="
    
    local report_file="validation-report-$(date +%Y%m%d-%H%M%S).txt"
    
    {
        echo "=== RAPPORT DE VALIDATION MEDICAL ADMIN SERVICE ==="
        echo "Date: $(date)"
        echo "Mode: $([ "$QUICK_MODE" = true ] && echo "QUICK" || echo "STANDARD")"
        echo ""
        echo "=== RÉSUMÉ ==="
        echo "✓ Validations réussies: $passed_validations"
        echo "⚠ Avertissements: $warnings"
        echo "✗ Erreurs: $errors"
        echo ""
        echo "=== DÉTAILS ==="
        # Ici on pourrait ajouter plus de détails
    } > "$report_file"
    
    log_success "✓ Rapport généré: $report_file"
    
    return 0
}

# Fonction principale
main() {
    log_info "=== Validation Medical Admin Service ==="
    echo ""
    
    local passed_validations=0
    local warnings=0
    local errors=0
    
    # Liste des validations à exécuter
    local validations=(
        "validate_environment"
        "validate_project_structure"
        "validate_configuration"
        "validate_dependencies"
    )
    
    # Validations supplémentaires selon le mode
    if [ "$QUICK_MODE" = false ]; then
        validations+=(
            "validate_build"
            "validate_docker"
            "validate_running_services"
            "validate_database"
            "validate_security"
        )
    fi
    
    if [ "$FULL_MODE" = true ]; then
        validations+=(
            "run_integration_tests"
        )
    fi
    
    # Exécution des validations
    for validation in "${validations[@]}"; do
        echo ""
        if $validation; then
            passed_validations=$((passed_validations + 1))
        else
            errors=$((errors + 1))
        fi
    done
    
    # Résumé final
    echo ""
    log_section "=== RÉSUMÉ DE LA VALIDATION ==="
    log_info "Validations réussies: $passed_validations"
    log_info "Avertissements: $warnings"
    log_info "Erreurs: $errors"
    
    if [ $errors -eq 0 ]; then
        log_success "🎉 Validation réussie! Le service est prêt pour le déploiement."
        exit 0
    else
        log_error "❌ Validation échouée. Veuillez corriger les erreurs avant le déploiement."
        exit 1
    fi
}

# Point d'entrée
main "$@"
