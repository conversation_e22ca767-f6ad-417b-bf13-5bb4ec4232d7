#!/bin/bash

# Script de validation pour Finance Service
# Vérifie la structure, configuration et fonctionnement du microservice

set -e

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
QUICK_MODE=false
FULL_MODE=false
STOP_ON_ERROR=false

# Fonctions de logging
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_section() {
    echo -e "\n${BLUE}$1${NC}"
}

# Validation de l'environnement
validate_environment() {
    log_section "=== Validation de l'environnement ==="
    
    # Vérifier les outils requis
    local tools=("docker" "docker-compose" "mvn" "java")
    
    for tool in "${tools[@]}"; do
        if command -v "$tool" &> /dev/null; then
            log_success "✓ $tool disponible"
        else
            log_warning "⚠ $tool non disponible"
        fi
    done
    
    # Vérifier les versions
    if command -v java &> /dev/null; then
        java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
        log_info "Version Java: $java_version"
    fi
    
    if command -v mvn &> /dev/null; then
        mvn_version=$(mvn -version 2>&1 | head -n 1 | cut -d' ' -f3)
        log_info "Version Maven: $mvn_version"
    fi
    
    return 0
}

# Validation de la structure du projet
validate_project_structure() {
    log_section "=== Validation de la structure du projet ==="
    
    local required_files=(
        "docker-compose.yml"
        "backend/Dockerfile"
        "backend/pom.xml"
        "backend/src/main/java/com/sprintbot/finance/FinanceServiceApplication.java"
        "frontend/Dockerfile"
        "frontend/package.json"
        "frontend/src/app/app.component.ts"
        "README.md"
    )
    
    for file in "${required_files[@]}"; do
        if [ -f "$file" ]; then
            log_success "✓ $file"
        else
            log_error "✗ $file manquant"
            return 1
        fi
    done
    
    # Vérifier les répertoires
    local required_dirs=(
        "backend/src/main/java/com/sprintbot/finance/entity"
        "backend/src/main/java/com/sprintbot/finance/repository"
        "backend/src/main/java/com/sprintbot/finance/service"
        "backend/src/main/java/com/sprintbot/finance/controller"
        "backend/src/main/resources/db/migration"
        "frontend/src/app/core/models"
        "frontend/src/app/core/services"
        "frontend/src/app/shared/components"
        "frontend/src/app/components"
    )
    
    for dir in "${required_dirs[@]}"; do
        if [ -d "$dir" ]; then
            log_success "✓ $dir/"
        else
            log_error "✗ $dir/ manquant"
            return 1
        fi
    done
    
    return 0
}

# Validation de la configuration
validate_configuration() {
    log_section "=== Validation de la configuration ==="
    
    # Vérifier application.yml
    if [ -f "backend/src/main/resources/application.yml" ]; then
        log_success "✓ Configuration backend trouvée"
        
        # Vérifier les profils
        if grep -q "spring.profiles" backend/src/main/resources/application.yml; then
            log_success "✓ Profils Spring configurés"
        else
            log_warning "⚠ Profils Spring non configurés"
        fi
    else
        log_error "✗ Configuration backend manquante"
        return 1
    fi
    
    # Vérifier les environnements Angular
    if [ -f "frontend/src/environments/environment.ts" ]; then
        log_success "✓ Configuration frontend développement"
    else
        log_error "✗ Configuration frontend développement manquante"
        return 1
    fi
    
    if [ -f "frontend/src/environments/environment.prod.ts" ]; then
        log_success "✓ Configuration frontend production"
    else
        log_error "✗ Configuration frontend production manquante"
        return 1
    fi
    
    return 0
}

# Validation des dépendances
validate_dependencies() {
    log_section "=== Validation des dépendances ==="
    
    # Vérifier pom.xml
    if [ -f "backend/pom.xml" ]; then
        log_info "Vérification des dépendances Maven..."
        
        local required_deps=(
            "spring-boot-starter-web"
            "spring-boot-starter-data-jpa"
            "spring-boot-starter-security"
            "spring-boot-starter-validation"
            "postgresql"
            "flyway-core"
        )
        
        for dep in "${required_deps[@]}"; do
            if grep -q "$dep" backend/pom.xml; then
                log_success "✓ $dep"
            else
                log_warning "⚠ $dep non trouvé dans pom.xml"
            fi
        done
    fi
    
    # Vérifier package.json
    if [ -f "frontend/package.json" ]; then
        log_info "Vérification des dépendances npm..."
        
        local required_npm_deps=(
            "@angular/core"
            "@angular/common"
            "@angular/router"
            "@angular/forms"
            "@angular/material"
            "rxjs"
        )
        
        for dep in "${required_npm_deps[@]}"; do
            if grep -q "\"$dep\"" frontend/package.json; then
                log_success "✓ $dep"
            else
                log_warning "⚠ $dep non trouvé dans package.json"
            fi
        done
    fi
    
    return 0
}

# Validation du build
validate_build() {
    log_section "=== Validation du build ==="
    
    # Build backend
    log_info "Test de build du backend..."
    if cd backend && mvn clean compile -q && cd ..; then
        log_success "✓ Build backend réussi"
    else
        log_error "✗ Échec du build backend"
        return 1
    fi
    
    # Build frontend (si Node.js est disponible)
    if command -v npm &> /dev/null; then
        log_info "Test de build du frontend..."
        if cd frontend && npm install --silent && npm run build --silent && cd ..; then
            log_success "✓ Build frontend réussi"
        else
            log_error "✗ Échec du build frontend"
            return 1
        fi
    else
        log_warning "⚠ npm non disponible, build frontend ignoré"
    fi
    
    return 0
}

# Validation Docker
validate_docker() {
    log_section "=== Validation Docker ==="
    
    # Vérifier les Dockerfiles
    log_info "Validation des Dockerfiles..."
    
    if [ -f "backend/Dockerfile" ]; then
        if grep -q "FROM eclipse-temurin" backend/Dockerfile; then
            log_success "✓ Dockerfile backend utilise une image Java appropriée"
        else
            log_warning "⚠ Dockerfile backend n'utilise pas eclipse-temurin"
        fi
    fi
    
    if [ -f "frontend/Dockerfile" ]; then
        if grep -q "FROM node" frontend/Dockerfile && grep -q "FROM nginx" frontend/Dockerfile; then
            log_success "✓ Dockerfile frontend utilise un build multi-stage"
        else
            log_warning "⚠ Dockerfile frontend ne semble pas utiliser un build multi-stage"
        fi
    fi
    
    return 0
}

# Fonction principale
main() {
    log_info "=== Validation Finance Service ==="
    echo ""
    
    local passed_validations=0
    local warnings=0
    local errors=0
    
    # Liste des validations à exécuter
    local validations=(
        "validate_environment"
        "validate_project_structure"
        "validate_configuration"
        "validate_dependencies"
    )
    
    # Validations supplémentaires selon le mode
    if [ "$QUICK_MODE" = false ]; then
        validations+=(
            "validate_build"
            "validate_docker"
        )
    fi
    
    for validation in "${validations[@]}"; do
        echo ""
        if $validation; then
            passed_validations=$((passed_validations + 1))
            log_success "✓ $validation réussie"
        else
            errors=$((errors + 1))
            log_error "✗ $validation échouée"
            if [ "$STOP_ON_ERROR" = true ]; then
                break
            fi
        fi
    done
    
    # Résumé final
    echo ""
    log_section "=== Résumé de la validation ==="
    log_info "Validations réussies: $passed_validations"
    log_info "Avertissements: $warnings"
    log_info "Erreurs: $errors"
    
    if [ $errors -eq 0 ]; then
        log_success "🎉 Finance Service validé avec succès!"
        exit 0
    else
        log_error "❌ Finance Service contient des erreurs"
        exit 1
    fi
}

# Gestion des arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --quick)
            QUICK_MODE=true
            shift
            ;;
        --full)
            FULL_MODE=true
            shift
            ;;
        --stop-on-error)
            STOP_ON_ERROR=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --quick          Mode rapide (sans build)"
            echo "  --full           Mode complet (avec tests)"
            echo "  --stop-on-error  Arrêter à la première erreur"
            echo "  -h, --help       Afficher cette aide"
            exit 0
            ;;
        *)
            log_error "Option inconnue: $1"
            exit 1
            ;;
    esac
done

# Exécution
main
