import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '@environments/environment';
import { ApiResponse, PagedResponse, FilterOptions } from '@core/models/common.model';

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  private readonly baseUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  // Méthodes GET
  get<T>(endpoint: string, params?: any): Observable<T> {
    const httpParams = this.buildHttpParams(params);
    return this.http.get<T>(`${this.baseUrl}${endpoint}`, { params: httpParams });
  }

  getById<T>(endpoint: string, id: number | string): Observable<T> {
    return this.http.get<T>(`${this.baseUrl}${endpoint}/${id}`);
  }

  getPaged<T>(endpoint: string, options?: FilterOptions): Observable<PagedResponse<T>> {
    const httpParams = this.buildHttpParams(options);
    return this.http.get<PagedResponse<T>>(`${this.baseUrl}${endpoint}`, { params: httpParams });
  }

  // Méthodes POST
  post<T>(endpoint: string, data: any): Observable<T> {
    return this.http.post<T>(`${this.baseUrl}${endpoint}`, data);
  }

  postWithResponse<T>(endpoint: string, data: any): Observable<ApiResponse<T>> {
    return this.http.post<ApiResponse<T>>(`${this.baseUrl}${endpoint}`, data);
  }

  // Méthodes PUT
  put<T>(endpoint: string, id: number | string, data: any): Observable<T> {
    return this.http.put<T>(`${this.baseUrl}${endpoint}/${id}`, data);
  }

  putWithResponse<T>(endpoint: string, id: number | string, data: any): Observable<ApiResponse<T>> {
    return this.http.put<ApiResponse<T>>(`${this.baseUrl}${endpoint}/${id}`, data);
  }

  // Méthodes PATCH
  patch<T>(endpoint: string, id: number | string, data: any): Observable<T> {
    return this.http.patch<T>(`${this.baseUrl}${endpoint}/${id}`, data);
  }

  // Méthodes DELETE
  delete<T>(endpoint: string, id: number | string): Observable<T> {
    return this.http.delete<T>(`${this.baseUrl}${endpoint}/${id}`);
  }

  deleteWithResponse<T>(endpoint: string, id: number | string): Observable<ApiResponse<T>> {
    return this.http.delete<ApiResponse<T>>(`${this.baseUrl}${endpoint}/${id}`);
  }

  // Méthodes spécialisées
  search<T>(endpoint: string, searchTerm: string, options?: FilterOptions): Observable<PagedResponse<T>> {
    const params = { ...options, search: searchTerm };
    const httpParams = this.buildHttpParams(params);
    return this.http.get<PagedResponse<T>>(`${this.baseUrl}${endpoint}/search`, { params: httpParams });
  }

  count(endpoint: string, filters?: any): Observable<number> {
    const httpParams = this.buildHttpParams(filters);
    return this.http.get<number>(`${this.baseUrl}${endpoint}/count`, { params: httpParams });
  }

  exists(endpoint: string, id: number | string): Observable<boolean> {
    return this.http.get<boolean>(`${this.baseUrl}${endpoint}/${id}/exists`);
  }

  // Upload de fichiers
  uploadFile(endpoint: string, file: File, additionalData?: any): Observable<any> {
    const formData = new FormData();
    formData.append('file', file);
    
    if (additionalData) {
      Object.keys(additionalData).forEach(key => {
        formData.append(key, additionalData[key]);
      });
    }

    return this.http.post(`${this.baseUrl}${endpoint}/upload`, formData);
  }

  // Download de fichiers
  downloadFile(endpoint: string, filename: string): Observable<Blob> {
    return this.http.get(`${this.baseUrl}${endpoint}/download/${filename}`, {
      responseType: 'blob'
    });
  }

  // Méthodes utilitaires
  private buildHttpParams(params?: any): HttpParams {
    let httpParams = new HttpParams();
    
    if (params) {
      Object.keys(params).forEach(key => {
        const value = params[key];
        if (value !== null && value !== undefined && value !== '') {
          if (Array.isArray(value)) {
            value.forEach(item => {
              httpParams = httpParams.append(key, item.toString());
            });
          } else {
            httpParams = httpParams.set(key, value.toString());
          }
        }
      });
    }
    
    return httpParams;
  }

  // Méthodes pour les actions spécifiques
  executeAction<T>(endpoint: string, id: number | string, action: string, data?: any): Observable<T> {
    const url = `${this.baseUrl}${endpoint}/${id}/${action}`;
    return data ? this.http.put<T>(url, data) : this.http.put<T>(url, {});
  }

  getStats<T>(endpoint: string, filters?: any): Observable<T> {
    const httpParams = this.buildHttpParams(filters);
    return this.http.get<T>(`${this.baseUrl}${endpoint}/stats`, { params: httpParams });
  }

  export(endpoint: string, format: 'csv' | 'excel' | 'pdf', filters?: any): Observable<Blob> {
    const httpParams = this.buildHttpParams({ ...filters, format });
    return this.http.get(`${this.baseUrl}${endpoint}/export`, {
      params: httpParams,
      responseType: 'blob'
    });
  }
}
