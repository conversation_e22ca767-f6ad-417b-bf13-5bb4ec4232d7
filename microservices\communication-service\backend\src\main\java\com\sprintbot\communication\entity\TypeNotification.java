package com.sprintbot.communication.entity;

/**
 * Énumération des types de notification
 */
public enum TypeNotification {
    INFO,                       // Information générale
    ALERTE,                     // Alerte importante
    RAPPEL,                     // Rappel
    SYSTEME,                    // Notification système
    MESSAGE,                    // Nouveau message
    NOUVEAU_MESSAGE,            // Nouveau message dans une conversation
    NOUVELLE_CONVERSATION,      // Nouvelle conversation créée
    AJOUT_CONVERSATION,         // Ajout à une conversation
    SUPPRESSION_CONVERSATION,   // Suppression d'une conversation
    ESCALADE_CHATBOT,           // Escalade d'une conversation chatbot
    INVITATION,                 // Invitation à rejoindre
    EVENEMENT,                  // Événement/Planning
    MEDICAL,                    // Notification médicale
    PERFORMANCE,                // Performance/Entraînement
    FINANCIER,                  // Notification financière
    URGENCE                     // Urgence
}
