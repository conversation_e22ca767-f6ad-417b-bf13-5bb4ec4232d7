package com.sprintbot.communication.service;

import com.sprintbot.communication.entity.*;
import com.sprintbot.communication.repository.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service pour la gestion des messages
 */
@Service
@Transactional
public class MessageService {

    private static final Logger logger = LoggerFactory.getLogger(MessageService.class);

    @Autowired
    private MessageRepository messageRepository;

    @Autowired
    private ConversationRepository conversationRepository;

    @Autowired
    private ParticipantConversationRepository participantRepository;

    @Autowired
    private ReactionMessageRepository reactionRepository;

    @Autowired
    private LectureMessageRepository lectureRepository;

    @Autowired
    private ConversationService conversationService;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private FileStorageService fileStorageService;

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    /**
     * Envoie un message texte
     */
    public Message envoyerMessage(Long conversationId, Long expediteurId, String contenu) {
        return envoyerMessage(conversationId, expediteurId, contenu, TypeMessage.TEXTE, null, null);
    }

    /**
     * Envoie une réponse à un message
     */
    public Message envoyerReponse(Long conversationId, Long expediteurId, String contenu, Long messageParentId) {
        Message message = envoyerMessage(conversationId, expediteurId, contenu, TypeMessage.TEXTE, null, null);
        if (messageParentId != null) {
            message.setReponseAId(messageParentId);
            message = messageRepository.save(message);
        }
        return message;
    }

    /**
     * Envoie un message avec fichier
     */
    public Message envoyerMessageAvecFichier(Long conversationId, Long expediteurId, 
                                           String contenu, MultipartFile fichier) {
        // Sauvegarder le fichier
        String fichierUrl = fileStorageService.sauvegarderFichier(fichier);
        String fichierNom = fichier.getOriginalFilename();
        Long fichierTaille = fichier.getSize();

        TypeMessage type = determinerTypeMessage(fichier);
        
        return envoyerMessage(conversationId, expediteurId, contenu, type, 
                            fichierUrl, fichierNom, fichierTaille);
    }

    /**
     * Envoie un message (méthode principale)
     */
    private Message envoyerMessage(Long conversationId, Long expediteurId, String contenu, 
                                 TypeMessage type, String fichierUrl, String fichierNom, 
                                 Long fichierTaille) {
        // Vérifier que l'utilisateur est participant
        if (!participantRepository.existsByConversationIdAndUtilisateurId(conversationId, expediteurId)) {
            throw new RuntimeException("Utilisateur non autorisé à envoyer des messages dans cette conversation");
        }

        Optional<Conversation> conversationOpt = conversationRepository.findById(conversationId);
        if (conversationOpt.isEmpty()) {
            throw new RuntimeException("Conversation non trouvée: " + conversationId);
        }

        Conversation conversation = conversationOpt.get();
        
        // Créer le message
        Message message = new Message(conversation, expediteurId, contenu, type);
        if (fichierUrl != null) {
            message.setFichierUrl(fichierUrl);
            message.setFichierNom(fichierNom);
            message.setFichierTaille(fichierTaille);
        }

        message = messageRepository.save(message);

        // Mettre à jour la conversation
        conversation.ajouterMessage(message);
        conversationService.mettreAJourActivite(conversationId);

        // Envoyer en temps réel via WebSocket
        envoyerMessageTempsReel(message);

        // Envoyer notifications aux participants
        notifierNouveauMessage(message);

        logger.info("Message envoyé dans la conversation {} par l'utilisateur {}: ID {}", 
                   conversationId, expediteurId, message.getId());

        return message;
    }

    /**
     * Surcharge pour message simple
     */
    public Message envoyerMessage(Long conversationId, Long expediteurId, String contenu,
                                 TypeMessage type, String fichierUrl, String fichierNom) {
        return envoyerMessage(conversationId, expediteurId, contenu, type, fichierUrl, fichierNom, null);
    }

    /**
     * Répond à un message
     */
    public Message repondreAMessage(Long messageId, Long expediteurId, String contenu) {
        Optional<Message> messageOriginalOpt = messageRepository.findById(messageId);
        if (messageOriginalOpt.isEmpty()) {
            throw new RuntimeException("Message original non trouvé: " + messageId);
        }

        Message messageOriginal = messageOriginalOpt.get();
        Long conversationId = messageOriginal.getConversation().getId();

        Message reponse = envoyerMessage(conversationId, expediteurId, contenu);
        reponse.setReponseAId(messageId);
        reponse = messageRepository.save(reponse);

        logger.info("Réponse au message {} envoyée par l'utilisateur {}: ID {}", 
                   messageId, expediteurId, reponse.getId());

        return reponse;
    }

    /**
     * Modifie un message
     */
    public Message modifierMessage(Long messageId, Long utilisateurId, String nouveauContenu) {
        Optional<Message> messageOpt = messageRepository.findById(messageId);
        if (messageOpt.isEmpty()) {
            throw new RuntimeException("Message non trouvé: " + messageId);
        }

        Message message = messageOpt.get();

        // Vérifier que l'utilisateur est l'expéditeur
        if (!message.getExpediteurId().equals(utilisateurId)) {
            throw new RuntimeException("Seul l'expéditeur peut modifier ce message");
        }

        // Vérifier le délai de modification (ex: 15 minutes)
        if (message.getDateEnvoi().isBefore(LocalDateTime.now().minusMinutes(15))) {
            throw new RuntimeException("Délai de modification dépassé");
        }

        message.modifier(nouveauContenu);
        message = messageRepository.save(message);

        // Notifier la modification en temps réel
        envoyerModificationTempsReel(message);

        logger.info("Message {} modifié par l'utilisateur {}", messageId, utilisateurId);
        return message;
    }

    /**
     * Supprime un message
     */
    public void supprimerMessage(Long messageId, Long utilisateurId) {
        Optional<Message> messageOpt = messageRepository.findById(messageId);
        if (messageOpt.isEmpty()) {
            throw new RuntimeException("Message non trouvé: " + messageId);
        }

        Message message = messageOpt.get();

        // Vérifier les permissions
        if (!message.getExpediteurId().equals(utilisateurId) && 
            !peutModererConversation(message.getConversation().getId(), utilisateurId)) {
            throw new RuntimeException("Permission refusée pour supprimer ce message");
        }

        message.supprimer();
        messageRepository.save(message);

        // Notifier la suppression en temps réel
        envoyerSuppressionTempsReel(message);

        logger.info("Message {} supprimé par l'utilisateur {}", messageId, utilisateurId);
    }

    /**
     * Ajoute une réaction à un message
     */
    public ReactionMessage ajouterReaction(Long messageId, Long utilisateurId, String emoji) {
        Optional<Message> messageOpt = messageRepository.findById(messageId);
        if (messageOpt.isEmpty()) {
            throw new RuntimeException("Message non trouvé: " + messageId);
        }

        Message message = messageOpt.get();

        // Vérifier si la réaction existe déjà
        Optional<ReactionMessage> reactionExistante = reactionRepository
                .findByMessageIdAndUtilisateurIdAndEmoji(messageId, utilisateurId, emoji);

        if (reactionExistante.isPresent()) {
            // Supprimer la réaction existante
            reactionRepository.delete(reactionExistante.get());
            message.supprimerReaction(utilisateurId, emoji);
            messageRepository.save(message);
            
            // Notifier la suppression de réaction
            envoyerReactionTempsReel(message, emoji, utilisateurId, false);
            
            logger.info("Réaction {} supprimée du message {} par l'utilisateur {}", 
                       emoji, messageId, utilisateurId);
            return null;
        }

        // Créer nouvelle réaction
        ReactionMessage reaction = new ReactionMessage(message, utilisateurId, emoji);
        reaction = reactionRepository.save(reaction);

        message.ajouterReaction(utilisateurId, emoji);
        messageRepository.save(message);

        // Notifier la nouvelle réaction
        envoyerReactionTempsReel(message, emoji, utilisateurId, true);

        logger.info("Réaction {} ajoutée au message {} par l'utilisateur {}", 
                   emoji, messageId, utilisateurId);
        return reaction;
    }

    /**
     * Marque un message comme lu
     */
    public void marquerCommeLu(Long messageId, Long utilisateurId) {
        Optional<Message> messageOpt = messageRepository.findById(messageId);
        if (messageOpt.isEmpty()) {
            return;
        }

        Message message = messageOpt.get();

        // Ne pas marquer ses propres messages comme lus
        if (message.getExpediteurId().equals(utilisateurId)) {
            return;
        }

        // Vérifier si déjà lu
        if (lectureRepository.existsByMessageIdAndUtilisateurId(messageId, utilisateurId)) {
            return;
        }

        LectureMessage lecture = new LectureMessage(message, utilisateurId);
        lectureRepository.save(lecture);

        // Notifier la lecture en temps réel
        envoyerLectureTempsReel(message, utilisateurId);

        logger.debug("Message {} marqué comme lu par l'utilisateur {}", messageId, utilisateurId);
    }

    /**
     * Obtient les messages d'une conversation
     */
    @Transactional(readOnly = true)
    public Page<Message> obtenirMessagesConversation(Long conversationId, Pageable pageable) {
        return messageRepository.findByConversationIdOrderByDateEnvoiDesc(conversationId, pageable);
    }

    /**
     * Recherche des messages
     */
    @Transactional(readOnly = true)
    public List<Message> rechercherMessages(Long conversationId, String recherche) {
        return messageRepository.rechercherMessagesParContenu(conversationId, recherche);
    }

    /**
     * Épingle/désépingle un message
     */
    public void toggleEpingleMessage(Long messageId, Long utilisateurId) {
        Optional<Message> messageOpt = messageRepository.findById(messageId);
        if (messageOpt.isEmpty()) {
            throw new RuntimeException("Message non trouvé: " + messageId);
        }

        Message message = messageOpt.get();

        // Vérifier les permissions de modération
        if (!peutModererConversation(message.getConversation().getId(), utilisateurId)) {
            throw new RuntimeException("Permission refusée pour épingler ce message");
        }

        if (message.getEstEpingle()) {
            message.desepingler();
        } else {
            message.epingler();
        }
        messageRepository.save(message);

        // Notifier l'épinglage en temps réel
        envoyerEpinglageTempsReel(message);

        logger.info("Message {} épinglé/désépinglé par l'utilisateur {}", messageId, utilisateurId);
    }

    /**
     * Envoie un message en temps réel via WebSocket
     */
    private void envoyerMessageTempsReel(Message message) {
        String destination = "/topic/conversation/" + message.getConversation().getId();
        messagingTemplate.convertAndSend(destination, message);
    }

    /**
     * Envoie une modification en temps réel
     */
    private void envoyerModificationTempsReel(Message message) {
        String destination = "/topic/conversation/" + message.getConversation().getId() + "/modification";
        messagingTemplate.convertAndSend(destination, message);
    }

    /**
     * Envoie une suppression en temps réel
     */
    private void envoyerSuppressionTempsReel(Message message) {
        String destination = "/topic/conversation/" + message.getConversation().getId() + "/suppression";
        messagingTemplate.convertAndSend(destination, message.getId());
    }

    /**
     * Envoie une réaction en temps réel
     */
    private void envoyerReactionTempsReel(Message message, String emoji, Long utilisateurId, boolean ajoutee) {
        String destination = "/topic/conversation/" + message.getConversation().getId() + "/reaction";
        java.util.Map<String, Object> reactionData = new java.util.HashMap<>();
        reactionData.put("messageId", message.getId());
        reactionData.put("emojiReaction", emoji);
        reactionData.put("utilisateurReaction", utilisateurId);
        reactionData.put("reactionAjoutee", ajoutee);
        messagingTemplate.convertAndSend(destination, reactionData);
    }

    /**
     * Envoie une lecture en temps réel
     */
    private void envoyerLectureTempsReel(Message message, Long utilisateurId) {
        String destination = "/topic/conversation/" + message.getConversation().getId() + "/lecture";
        java.util.Map<String, Object> lectureData = new java.util.HashMap<>();
        lectureData.put("messageId", message.getId());
        lectureData.put("utilisateurLecture", utilisateurId);
        messagingTemplate.convertAndSend(destination, lectureData);
    }

    /**
     * Supprime une réaction d'un message
     */
    public void supprimerReaction(Long messageId, Long utilisateurId, String emoji) {
        Message message = messageRepository.findById(messageId)
                .orElseThrow(() -> new RuntimeException("Message non trouvé"));

        Optional<ReactionMessage> reaction = reactionRepository
                .findByMessageIdAndUtilisateurIdAndEmoji(messageId, utilisateurId, emoji);

        if (reaction.isPresent()) {
            reactionRepository.delete(reaction.get());
            message.supprimerReaction(utilisateurId, emoji);
            messageRepository.save(message);

            // Notifier la suppression
            envoyerReactionTempsReel(message, emoji, utilisateurId, false);
        }
    }

    /**
     * Obtient les messages non lus d'un utilisateur
     */
    public java.util.List<Message> obtenirMessagesNonLus(Long utilisateurId) {
        // Utiliser la méthode existante pour obtenir les messages récents
        return messageRepository.findMessagesRecents(utilisateurId, org.springframework.data.domain.PageRequest.of(0, 50));
    }

    /**
     * Compte les messages non lus d'un utilisateur dans toutes ses conversations
     */
    public long compterMessagesNonLus(Long utilisateurId) {
        // Pour l'instant, retourner 0 - cette méthode nécessiterait une requête plus complexe
        return 0L;
    }

    /**
     * Obtient les réactions d'un message
     */
    public java.util.List<ReactionMessage> obtenirReactionsMessage(Long messageId) {
        return reactionRepository.findByMessageIdOrderByDateReactionAsc(messageId);
    }

    /**
     * Obtient les messages épinglés d'une conversation
     */
    public java.util.List<Message> obtenirMessagesEpingles(Long conversationId) {
        return messageRepository.findByConversationIdAndEstEpingleTrueOrderByDateEnvoiDesc(conversationId);
    }

    /**
     * Recherche des messages avec pagination
     */
    public org.springframework.data.domain.Page<Message> rechercherMessages(Long conversationId, String recherche, org.springframework.data.domain.Pageable pageable) {
        // Utiliser la méthode de recherche existante et convertir en Page
        java.util.List<Message> messages = messageRepository.rechercherMessagesParContenu(conversationId, recherche);
        return new org.springframework.data.domain.PageImpl<>(messages, pageable, messages.size());
    }

    /**
     * Envoie un épinglage en temps réel
     */
    private void envoyerEpinglageTempsReel(Message message) {
        String destination = "/topic/conversation/" + message.getConversation().getId() + "/epinglage";
        messagingTemplate.convertAndSend(destination, message);
    }

    /**
     * Notifie les participants d'un nouveau message
     */
    private void notifierNouveauMessage(Message message) {
        Long conversationId = message.getConversation().getId();
        List<ParticipantConversation> participants = participantRepository
                .findByConversationIdAndNotificationsActiveesTrueOrderByDateAjoutAsc(conversationId);

        for (ParticipantConversation participant : participants) {
            if (!participant.getUtilisateurId().equals(message.getExpediteurId())) {
                notificationService.notifierNouveauMessage(message, participant.getUtilisateurId());
            }
        }
    }

    /**
     * Détermine le type de message selon le fichier
     */
    private TypeMessage determinerTypeMessage(MultipartFile fichier) {
        String contentType = fichier.getContentType();
        if (contentType == null) return TypeMessage.FICHIER;

        if (contentType.startsWith("image/")) return TypeMessage.IMAGE;
        if (contentType.startsWith("video/")) return TypeMessage.VIDEO;
        if (contentType.startsWith("audio/")) return TypeMessage.AUDIO;
        
        return TypeMessage.FICHIER;
    }

    /**
     * Vérifie si un utilisateur peut modérer une conversation
     */
    private boolean peutModererConversation(Long conversationId, Long utilisateurId) {
        Optional<ParticipantConversation> participantOpt = 
                participantRepository.findByConversationIdAndUtilisateurId(conversationId, utilisateurId);
        
        return participantOpt.isPresent() && participantOpt.get().peutModererConversation();
    }
}
