# Dockerfile pour le backend du service de communication
# Multi-stage build pour optimiser la taille de l'image

# Stage 1: Build
FROM eclipse-temurin:21-jdk-alpine AS builder

# Métadonnées
LABEL maintainer="SprintBot Team"
LABEL description="Backend du service de communication SprintBot"
LABEL version="1.0.0"

# Variables d'environnement pour le build
ENV MAVEN_VERSION=3.9.5
ENV MAVEN_HOME=/opt/maven
ENV PATH=$MAVEN_HOME/bin:$PATH

# Installation de Maven
RUN apk add --no-cache curl tar bash \
    && mkdir -p /opt \
    && curl -fsSL https://archive.apache.org/dist/maven/maven-3/$MAVEN_VERSION/binaries/apache-maven-$MAVEN_VERSION-bin.tar.gz \
    | tar -xzC /opt \
    && mv /opt/apache-maven-$MAVEN_VERSION /opt/maven \
    && rm -rf /var/cache/apk/*

# Répertoire de travail
WORKDIR /app

# Copier les fichiers de configuration Maven
COPY pom.xml .

# Télécharger les dépendances (cache layer)
RUN mvn dependency:go-offline -B

# Copier le code source
COPY src/ src/

# Build de l'application
RUN mvn clean package -DskipTests -B

# Stage 2: Runtime
FROM eclipse-temurin:21-jre-alpine AS runtime

# Métadonnées
LABEL maintainer="SprintBot Team"
LABEL description="Backend du service de communication SprintBot - Runtime"
LABEL version="1.0.0"

# Installation des outils nécessaires
RUN apk add --no-cache \
    curl \
    dumb-init \
    && rm -rf /var/cache/apk/*

# Création d'un utilisateur non-root pour la sécurité
RUN addgroup -g 1001 -S sprintbot && \
    adduser -u 1001 -S sprintbot -G sprintbot

# Répertoire de travail
WORKDIR /app

# Copier l'artefact depuis le stage de build
COPY --from=builder /app/target/communication-service-*.jar app.jar

# Copier les scripts et configurations
COPY docker/ docker/

# Permissions
RUN chown -R sprintbot:sprintbot /app && \
    chmod +x docker/entrypoint.sh

# Variables d'environnement par défaut
ENV SPRING_PROFILES_ACTIVE=docker
ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+UseContainerSupport"
ENV SERVER_PORT=8084

# Exposition du port
EXPOSE 8084

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8084/actuator/health || exit 1

# Utilisateur non-root
USER sprintbot

# Point d'entrée avec dumb-init pour la gestion des signaux
ENTRYPOINT ["dumb-init", "--"]
CMD ["docker/entrypoint.sh"]
