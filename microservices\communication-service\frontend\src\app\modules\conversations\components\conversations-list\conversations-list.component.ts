import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Subscription } from 'rxjs';

import { ConversationService, Conversation } from '../../../../services/conversation.service';
import { WebSocketService } from '../../../../services/websocket.service';
import { CreateConversationDialogComponent } from '../create-conversation-dialog/create-conversation-dialog.component';

@Component({
  selector: 'app-conversations-list',
  templateUrl: './conversations-list.component.html',
  styleUrls: ['./conversations-list.component.scss']
})
export class ConversationsListComponent implements OnInit, OnDestroy {
  conversations: Conversation[] = [];
  loading = true;
  error: string | null = null;
  
  // Filtres et recherche
  searchTerm = '';
  selectedType = 'TOUS';
  conversationTypes = [
    { value: 'TOUS', label: 'Toutes' },
    { value: 'PRIVE', label: 'Privées' },
    { value: 'GROUPE', label: 'Groupes' },
    { value: 'EQUIPE', label: 'Équipes' },
    { value: 'CANAL', label: 'Canaux' }
  ];
  
  private subscriptions: Subscription[] = [];
  private currentUserId = 1; // À remplacer par l'utilisateur connecté

  constructor(
    private conversationService: ConversationService,
    private webSocketService: WebSocketService,
    private router: Router,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadConversations();
    this.setupWebSocketSubscriptions();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private loadConversations(): void {
    this.loading = true;
    this.error = null;
    
    const sub = this.conversationService.obtenirConversationsUtilisateur(this.currentUserId).subscribe({
      next: (response) => {
        this.conversations = response.content || [];
        this.loading = false;
      },
      error: (error) => {
        this.error = 'Erreur lors du chargement des conversations';
        this.loading = false;
        console.error('Erreur chargement conversations:', error);
      }
    });
    
    this.subscriptions.push(sub);
  }

  private setupWebSocketSubscriptions(): void {
    // Écouter les nouveaux messages
    const messagesSub = this.webSocketService.getMessages().subscribe(message => {
      if (message.type === 'MESSAGE') {
        this.handleNewMessage(message.data);
      }
    });
    
    this.subscriptions.push(messagesSub);
  }

  private handleNewMessage(messageData: any): void {
    // Mettre à jour la conversation avec le nouveau message
    const conversation = this.conversations.find(c => c.id === messageData.conversationId);
    if (conversation) {
      conversation.dernierMessage = messageData.contenu;
      conversation.dernierMessageDate = messageData.dateCreation;
      conversation.nombreMessagesNonLus = (conversation.nombreMessagesNonLus || 0) + 1;
      
      // Déplacer la conversation en haut de la liste
      this.conversations = [
        conversation,
        ...this.conversations.filter(c => c.id !== conversation.id)
      ];
    }
  }

  // Actions
  openConversation(conversation: Conversation): void {
    this.router.navigate(['/conversations', conversation.id]);
  }

  createConversation(): void {
    const dialogRef = this.dialog.open(CreateConversationDialogComponent, {
      width: '500px',
      data: { userId: this.currentUserId }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadConversations();
        this.snackBar.open('Conversation créée avec succès', 'Fermer', {
          duration: 3000
        });
      }
    });
  }

  archiveConversation(conversation: Conversation, event: Event): void {
    event.stopPropagation();
    
    const sub = this.conversationService.archiverConversation(conversation.id).subscribe({
      next: () => {
        this.conversations = this.conversations.filter(c => c.id !== conversation.id);
        this.snackBar.open('Conversation archivée', 'Annuler', {
          duration: 3000
        }).onAction().subscribe(() => {
          this.restaurerConversation(conversation.id);
        });
      },
      error: (error) => {
        this.snackBar.open('Erreur lors de l\'archivage', 'Fermer', {
          duration: 3000
        });
        console.error('Erreur archivage:', error);
      }
    });
    
    this.subscriptions.push(sub);
  }

  private restaurerConversation(conversationId: number): void {
    const sub = this.conversationService.restaurerConversation(conversationId).subscribe({
      next: () => {
        this.loadConversations();
        this.snackBar.open('Conversation restaurée', 'Fermer', {
          duration: 3000
        });
      },
      error: (error) => {
        this.snackBar.open('Erreur lors de la restauration', 'Fermer', {
          duration: 3000
        });
        console.error('Erreur restauration:', error);
      }
    });
    
    this.subscriptions.push(sub);
  }

  quitterConversation(conversation: Conversation, event: Event): void {
    event.stopPropagation();
    
    if (confirm('Êtes-vous sûr de vouloir quitter cette conversation ?')) {
      const sub = this.conversationService.quitterConversation(conversation.id, this.currentUserId).subscribe({
        next: () => {
          this.conversations = this.conversations.filter(c => c.id !== conversation.id);
          this.snackBar.open('Vous avez quitté la conversation', 'Fermer', {
            duration: 3000
          });
        },
        error: (error) => {
          this.snackBar.open('Erreur lors de la sortie', 'Fermer', {
            duration: 3000
          });
          console.error('Erreur quitter conversation:', error);
        }
      });
      
      this.subscriptions.push(sub);
    }
  }

  // Filtres et recherche
  get filteredConversations(): Conversation[] {
    let filtered = this.conversations;
    
    // Filtre par type
    if (this.selectedType !== 'TOUS') {
      filtered = filtered.filter(c => c.type === this.selectedType);
    }
    
    // Filtre par recherche
    if (this.searchTerm.trim()) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(c => 
        c.nom.toLowerCase().includes(term) ||
        (c.description && c.description.toLowerCase().includes(term)) ||
        (c.dernierMessage && c.dernierMessage.toLowerCase().includes(term))
      );
    }
    
    return filtered;
  }

  onSearchChange(): void {
    // La recherche est réactive grâce au getter filteredConversations
  }

  onTypeChange(): void {
    // Le filtrage est réactif grâce au getter filteredConversations
  }

  // Utilitaires
  getConversationIcon(type: string): string {
    switch (type) {
      case 'PRIVE': return 'person';
      case 'GROUPE': return 'group';
      case 'EQUIPE': return 'sports_volleyball';
      case 'CANAL': return 'tag';
      default: return 'chat';
    }
  }

  getConversationTypeLabel(type: string): string {
    const typeObj = this.conversationTypes.find(t => t.value === type);
    return typeObj ? typeObj.label : type;
  }

  formatLastMessageDate(date: string): string {
    const messageDate = new Date(date);
    const now = new Date();
    const diffMs = now.getTime() - messageDate.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'À l\'instant';
    if (diffMins < 60) return `${diffMins}min`;
    if (diffHours < 24) return `${diffHours}h`;
    if (diffDays < 7) return `${diffDays}j`;
    
    return messageDate.toLocaleDateString('fr-FR');
  }

  trackByConversation(index: number, conversation: Conversation): number {
    return conversation.id;
  }
}
