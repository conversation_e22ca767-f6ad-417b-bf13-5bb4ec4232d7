# Discovery Service - Fichiers à ignorer
# SprintBot - Service de découverte Eureka

# ================================
# Java & Maven
# ================================

# Compiled class files
*.class

# Log files
*.log

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# Virtual machine crash logs
hs_err_pid*
replay_pid*

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# ================================
# Spring Boot
# ================================

# Spring Boot build info
build-info.properties

# Spring Boot DevTools
.spring-boot-devtools.restarttrigger

# ================================
# IDEs
# ================================

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

# Eclipse
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# VS Code
.vscode/
*.code-workspace

# ================================
# Docker
# ================================

# Docker volumes
docker-volumes/

# Docker build context
.dockerignore

# ================================
# Logs et données
# ================================

# Application logs
logs/
*.log
*.log.*

# Eureka data
eureka-data/

# Temporary files
tmp/
temp/
*.tmp
*.temp

# ================================
# Configuration sensible
# ================================

# Environment files
.env
.env.local
.env.*.local

# Configuration locale
application-local.yml
application-local.properties

# Secrets
secrets/
*.key
*.pem
*.p12
*.jks

# ================================
# Système d'exploitation
# ================================

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ================================
# Tests et couverture
# ================================

# JaCoCo
jacoco.exec
jacoco-it.exec

# Test reports
test-results/
test-reports/

# Coverage reports
coverage/

# ================================
# Build et déploiement
# ================================

# Build artifacts
build/
dist/

# Deployment
deploy/

# ================================
# Monitoring et métriques
# ================================

# Prometheus data
prometheus-data/

# Grafana data
grafana-data/

# ================================
# Backup et archives
# ================================

# Backup files
*.bak
*.backup
*.old

# Archives
*.7z
*.dmg
*.gz
*.iso
*.rar
*.tar
*.zip

# ================================
# Spécifique au Discovery Service
# ================================

# Eureka registry cache
eureka-registry-cache/

# Service discovery data
discovery-data/

# Health check logs
health-check.log

# Registration logs
registration.log

# ================================
# Documentation générée
# ================================

# JavaDoc
javadoc/

# API documentation
api-docs/

# ================================
# Outils de développement
# ================================

# JRebel
rebel.xml

# Spring Loaded
.springloaded

# JProfiler
*.jprofiler

# YourKit
*.yjp

# ================================
# Cache et temporaires
# ================================

# Gradle cache
.gradle/

# Node modules (si utilisé pour des outils)
node_modules/

# NPM cache
.npm

# Yarn cache
.yarn/

# ================================
# Fichiers de lock
# ================================

# Maven wrapper
!.mvn/wrapper/maven-wrapper.properties

# Gradle wrapper
!gradle/wrapper/gradle-wrapper.properties
