package com.sprintbot.communication.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Entité représentant une conversation (privée ou de groupe)
 */
@Entity
@Table(name = "conversations")
@EntityListeners(AuditingEntityListener.class)
public class Conversation {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "Le nom de la conversation est obligatoire")
    @Size(max = 200, message = "Le nom ne peut pas dépasser 200 caractères")
    @Column(name = "nom", nullable = false, length = 200)
    private String nom;

    @Size(max = 500, message = "La description ne peut pas dépasser 500 caractères")
    @Column(name = "description", length = 500)
    private String description;

    @NotNull(message = "Le type de conversation est obligatoire")
    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, length = 20)
    private TypeConversation type;

    @NotNull(message = "L'ID du créateur est obligatoire")
    @Column(name = "createur_id", nullable = false)
    private Long createurId;

    @Column(name = "avatar_url")
    private String avatarUrl;

    @NotNull(message = "Le statut est obligatoire")
    @Enumerated(EnumType.STRING)
    @Column(name = "statut", nullable = false, length = 20)
    private StatutConversation statut = StatutConversation.ACTIF;

    @Column(name = "derniere_activite")
    private LocalDateTime derniereActivite;

    @Column(name = "nombre_participants")
    private Integer nombreParticipants = 0;

    @Column(name = "nombre_messages")
    private Integer nombreMessages = 0;

    @Column(name = "est_epinglee")
    private Boolean estEpinglee = false;

    @Column(name = "notifications_activees")
    private Boolean notificationsActivees = true;

    @CreatedDate
    @Column(name = "date_creation", nullable = false)
    private LocalDateTime dateCreation;

    @LastModifiedDate
    @Column(name = "date_modification")
    private LocalDateTime dateModification;

    // Relations
    @OneToMany(mappedBy = "conversation", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Message> messages = new ArrayList<>();

    @OneToMany(mappedBy = "conversation", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ParticipantConversation> participants = new ArrayList<>();

    // Constructeurs
    public Conversation() {}

    public Conversation(String nom, TypeConversation type, Long createurId) {
        this.nom = nom;
        this.type = type;
        this.createurId = createurId;
        this.statut = StatutConversation.ACTIF;
        this.derniereActivite = LocalDateTime.now();
    }

    // Méthodes métier
    public void ajouterParticipant(Long utilisateurId, RoleParticipant role) {
        ParticipantConversation participant = new ParticipantConversation(this, utilisateurId, role);
        this.participants.add(participant);
        this.nombreParticipants = this.participants.size();
        this.derniereActivite = LocalDateTime.now();
    }

    public void supprimerParticipant(Long utilisateurId) {
        this.participants.removeIf(p -> p.getUtilisateurId().equals(utilisateurId));
        this.nombreParticipants = this.participants.size();
        this.derniereActivite = LocalDateTime.now();
    }

    public void ajouterMessage(Message message) {
        this.messages.add(message);
        this.nombreMessages = this.messages.size();
        this.derniereActivite = LocalDateTime.now();
    }

    public void archiver() {
        this.statut = StatutConversation.ARCHIVE;
        this.derniereActivite = LocalDateTime.now();
    }

    public void restaurer() {
        this.statut = StatutConversation.ACTIF;
        this.derniereActivite = LocalDateTime.now();
    }

    public boolean estPrivee() {
        return this.type == TypeConversation.PRIVE;
    }

    public boolean estGroupe() {
        return this.type == TypeConversation.GROUPE;
    }

    public boolean estEquipe() {
        return this.type == TypeConversation.EQUIPE;
    }

    public boolean peutEtreModifieeParUtilisateur(Long utilisateurId) {
        return this.createurId.equals(utilisateurId) || 
               this.participants.stream()
                   .anyMatch(p -> p.getUtilisateurId().equals(utilisateurId) && 
                                 p.getRole() == RoleParticipant.ADMIN);
    }

    // Getters et Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNom() {
        return nom;
    }

    public void setNom(String nom) {
        this.nom = nom;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public TypeConversation getType() {
        return type;
    }

    public void setType(TypeConversation type) {
        this.type = type;
    }

    public Long getCreateurId() {
        return createurId;
    }

    public void setCreateurId(Long createurId) {
        this.createurId = createurId;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public StatutConversation getStatut() {
        return statut;
    }

    public void setStatut(StatutConversation statut) {
        this.statut = statut;
    }

    public LocalDateTime getDerniereActivite() {
        return derniereActivite;
    }

    public void setDerniereActivite(LocalDateTime derniereActivite) {
        this.derniereActivite = derniereActivite;
    }

    public Integer getNombreParticipants() {
        return nombreParticipants;
    }

    public void setNombreParticipants(Integer nombreParticipants) {
        this.nombreParticipants = nombreParticipants;
    }

    public Integer getNombreMessages() {
        return nombreMessages;
    }

    public void setNombreMessages(Integer nombreMessages) {
        this.nombreMessages = nombreMessages;
    }

    public Boolean getEstEpinglee() {
        return estEpinglee;
    }

    public void setEstEpinglee(Boolean estEpinglee) {
        this.estEpinglee = estEpinglee;
    }

    public Boolean getNotificationsActivees() {
        return notificationsActivees;
    }

    public void setNotificationsActivees(Boolean notificationsActivees) {
        this.notificationsActivees = notificationsActivees;
    }

    public LocalDateTime getDateCreation() {
        return dateCreation;
    }

    public void setDateCreation(LocalDateTime dateCreation) {
        this.dateCreation = dateCreation;
    }

    public LocalDateTime getDateModification() {
        return dateModification;
    }

    public void setDateModification(LocalDateTime dateModification) {
        this.dateModification = dateModification;
    }

    public List<Message> getMessages() {
        return messages;
    }

    public void setMessages(List<Message> messages) {
        this.messages = messages;
    }

    public List<ParticipantConversation> getParticipants() {
        return participants;
    }

    public void setParticipants(List<ParticipantConversation> participants) {
        this.participants = participants;
    }

    @Override
    public String toString() {
        return "Conversation{" +
                "id=" + id +
                ", nom='" + nom + '\'' +
                ", type=" + type +
                ", statut=" + statut +
                ", nombreParticipants=" + nombreParticipants +
                ", nombreMessages=" + nombreMessages +
                '}';
    }
}


