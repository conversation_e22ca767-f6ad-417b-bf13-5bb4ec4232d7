package com.sprintbot.medicaladmin.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Entité représentant une demande administrative interne
 * Gère le workflow de validation des demandes administratives
 */
@Entity
@Table(name = "demandes_administratives")
@EntityListeners(AuditingEntityListener.class)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DemandeAdministrative {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "L'ID du demandeur est obligatoire")
    @Column(name = "demandeur_id", nullable = false)
    private Long demandeurId;

    @NotBlank(message = "Le type de demandeur est obligatoire")
    @Size(max = 50, message = "Le type de demandeur ne peut pas dépasser 50 caractères")
    @Column(name = "type_demandeur", nullable = false, length = 50)
    private String typeDemandeur; // JOUEUR, COACH, STAFF_MEDICAL, RESPONSABLE_FINANCIER

    @Column(name = "approbateur_id")
    private Long approvateurId; // ID de l'administrateur qui traite

    @NotBlank(message = "Le type de demande est obligatoire")
    @Size(max = 100, message = "Le type de demande ne peut pas dépasser 100 caractères")
    @Column(name = "type_demande", nullable = false, length = 100)
    private String typeDemande; // CONGE, MATERIEL, FORMATION, REMBOURSEMENT, ACCES, AUTRE

    @NotBlank(message = "Le titre est obligatoire")
    @Size(max = 200, message = "Le titre ne peut pas dépasser 200 caractères")
    @Column(name = "titre", nullable = false, length = 200)
    private String titre;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "justification", columnDefinition = "TEXT")
    private String justification;

    @Size(max = 50, message = "La priorité ne peut pas dépasser 50 caractères")
    @Column(name = "priorite", length = 50)
    private String priorite = "NORMALE"; // FAIBLE, NORMALE, ELEVEE, URGENTE

    @Size(max = 50, message = "Le statut ne peut pas dépasser 50 caractères")
    @Column(name = "statut", length = 50)
    private String statut = "EN_ATTENTE"; // EN_ATTENTE, EN_TRAITEMENT, VALIDEE, REJETEE, SUSPENDUE

    @NotNull(message = "La date de soumission est obligatoire")
    @Column(name = "date_soumission", nullable = false)
    private LocalDate dateSoumission;

    @Column(name = "date_traitement")
    private LocalDate dateTraitement;

    @Column(name = "date_echeance")
    private LocalDate dateEcheance;

    @Column(name = "commentaire_approbateur", columnDefinition = "TEXT")
    private String commentaireApprobateur;

    @Column(name = "documents_joints", columnDefinition = "TEXT")
    private String documentsJoints; // URLs ou chemins des fichiers

    @Column(name = "cout_estime")
    private Double coutEstime;

    @Column(name = "cout_reel")
    private Double coutReel;

    @Size(max = 100, message = "La catégorie budgétaire ne peut pas dépasser 100 caractères")
    @Column(name = "categorie_budgetaire", length = 100)
    private String categorieBudgetaire;

    @Column(name = "impact_planning")
    private Boolean impactPlanning = false;

    @Column(name = "necessite_validation_coach")
    private Boolean necessiteValidationCoach = false;

    @Column(name = "necessite_validation_medical")
    private Boolean necessiteValidationMedical = false;

    @Column(name = "necessite_validation_financier")
    private Boolean necessiteValidationFinancier = false;

    @Column(name = "validation_coach")
    private Boolean validationCoach;

    @Column(name = "validation_medical")
    private Boolean validationMedical;

    @Column(name = "validation_financier")
    private Boolean validationFinancier;

    @CreatedDate
    @Column(name = "date_creation", nullable = false, updatable = false)
    private LocalDateTime dateCreation;

    @LastModifiedDate
    @Column(name = "date_modification")
    private LocalDateTime dateModification;

    // Initialisation par défaut avec @PrePersist
    @PrePersist
    protected void onCreate() {
        if (this.dateSoumission == null) {
            this.dateSoumission = LocalDate.now();
        }
        if (this.statut == null) {
            this.statut = "EN_ATTENTE";
        }
        if (this.priorite == null) {
            this.priorite = "NORMALE";
        }
        if (this.impactPlanning == null) {
            this.impactPlanning = false;
        }
        if (this.necessiteValidationCoach == null) {
            this.necessiteValidationCoach = false;
        }
        if (this.necessiteValidationMedical == null) {
            this.necessiteValidationMedical = false;
        }
        if (this.necessiteValidationFinancier == null) {
            this.necessiteValidationFinancier = false;
        }
    }

    // Méthodes métier
    public void soumettre() {
        this.dateSoumission = LocalDate.now();
        this.statut = "EN_ATTENTE";
    }

    public void commencerTraitement(Long approvateurId) {
        this.statut = "EN_TRAITEMENT";
        this.approvateurId = approvateurId;
        this.dateTraitement = LocalDate.now();
    }

    public void valider(String commentaire) {
        this.statut = "VALIDEE";
        this.commentaireApprobateur = commentaire;
        this.dateTraitement = LocalDate.now();
    }

    public void rejeter(String commentaire) {
        this.statut = "REJETEE";
        this.commentaireApprobateur = commentaire;
        this.dateTraitement = LocalDate.now();
    }

    public void suspendre(String commentaire) {
        this.statut = "SUSPENDUE";
        this.commentaireApprobateur = commentaire;
    }

    public boolean estEnAttente() {
        return "EN_ATTENTE".equals(this.statut);
    }

    public boolean estValidee() {
        return "VALIDEE".equals(this.statut);
    }

    public boolean estRejetee() {
        return "REJETEE".equals(this.statut);
    }

    public boolean estEnTraitement() {
        return "EN_TRAITEMENT".equals(this.statut);
    }

    public boolean estUrgente() {
        return "URGENTE".equals(this.priorite);
    }

    public boolean estEchue() {
        return this.dateEcheance != null && this.dateEcheance.isBefore(LocalDate.now());
    }

    public long getNombreJoursDepuisSoumission() {
        return this.dateSoumission.until(LocalDate.now()).getDays();
    }

    public long getNombreJoursAvantEcheance() {
        if (this.dateEcheance == null) {
            return Long.MAX_VALUE;
        }
        return LocalDate.now().until(this.dateEcheance).getDays();
    }

    public boolean necessiteValidationsMultiples() {
        return this.necessiteValidationCoach || this.necessiteValidationMedical || 
               this.necessiteValidationFinancier;
    }

    public boolean toutesValidationsObtenues() {
        if (!necessiteValidationsMultiples()) {
            return true;
        }
        
        boolean coachOk = !this.necessiteValidationCoach || Boolean.TRUE.equals(this.validationCoach);
        boolean medicalOk = !this.necessiteValidationMedical || Boolean.TRUE.equals(this.validationMedical);
        boolean financierOk = !this.necessiteValidationFinancier || Boolean.TRUE.equals(this.validationFinancier);
        
        return coachOk && medicalOk && financierOk;
    }

    public void definirValidationsNecessaires(String typeDemande) {
        switch (typeDemande.toUpperCase()) {
            case "CONGE":
                this.necessiteValidationCoach = true;
                this.impactPlanning = true;
                break;
            case "MATERIEL":
                this.necessiteValidationFinancier = true;
                break;
            case "FORMATION":
                this.necessiteValidationCoach = true;
                this.necessiteValidationFinancier = true;
                break;
            case "REMBOURSEMENT":
                this.necessiteValidationFinancier = true;
                break;
            case "ACCES":
                // Pas de validation spéciale nécessaire
                break;
            default:
                // Pour les autres types, évaluation au cas par cas
                break;
        }
    }



    @Override
    public String toString() {
        return "DemandeAdministrative{" +
                "id=" + id +
                ", demandeurId=" + demandeurId +
                ", typeDemande='" + typeDemande + '\'' +
                ", titre='" + titre + '\'' +
                ", statut='" + statut + '\'' +
                ", dateSoumission=" + dateSoumission +
                '}';
    }
}
