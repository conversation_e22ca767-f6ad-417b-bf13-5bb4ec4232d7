{"name": "planning-performance-frontend", "version": "1.0.0", "description": "Frontend Angular pour le microservice Planning & Performance", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "serve": "ng serve --host 0.0.0.0 --port 4202", "build:prod": "ng build --configuration production", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "^17.0.0", "@angular/common": "^17.0.0", "@angular/compiler": "^17.0.0", "@angular/core": "^17.0.0", "@angular/forms": "^17.0.0", "@angular/platform-browser": "^17.0.0", "@angular/platform-browser-dynamic": "^17.0.0", "@angular/router": "^17.0.0", "@angular/cdk": "^17.0.0", "@angular/material": "^17.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.0", "bootstrap": "^5.3.0", "@ng-bootstrap/ng-bootstrap": "^16.0.0", "chart.js": "^4.4.0", "ng2-charts": "^5.0.0", "date-fns": "^2.30.0", "ngx-toastr": "^18.0.0"}, "devDependencies": {"@angular-devkit/build-angular": "^17.0.0", "@angular/cli": "^17.0.0", "@angular/compiler-cli": "^17.0.0", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-headless": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.2.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}