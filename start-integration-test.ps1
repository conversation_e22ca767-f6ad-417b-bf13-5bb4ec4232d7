# Script PowerShell pour tester l'intégration des microservices
# Club Olympique de Kelibia - Plateforme Volleyball

Write-Host "=== Test d'Intégration des Microservices ===" -ForegroundColor Green
Write-Host "Club Olympique de Kelibia - Plateforme Volleyball" -ForegroundColor Cyan
Write-Host ""

# Vérification des prérequis
Write-Host "1. Vérification des prérequis..." -ForegroundColor Yellow

# Vérifier Java
try {
    $javaVersion = java -version 2>&1 | Select-String "version"
    Write-Host "✅ Java installé : $javaVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Java non trouvé. Veuillez installer Java 17+" -ForegroundColor Red
    exit 1
}

# Vérifier Maven
try {
    $mavenVersion = mvn -version 2>&1 | Select-String "Apache Maven"
    Write-Host "✅ Maven installé : $mavenVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Maven non trouvé. Veuillez installer Maven" -ForegroundColor Red
    exit 1
}

# Vérifier Node.js
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js installé : $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js non trouvé. Veuillez installer Node.js" -ForegroundColor Red
    exit 1
}

# Vérifier Angular CLI
try {
    $ngVersion = ng version --skip-git 2>&1 | Select-String "Angular CLI"
    Write-Host "✅ Angular CLI installé : $ngVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Angular CLI non trouvé. Installation..." -ForegroundColor Yellow
    npm install -g @angular/cli
}

Write-Host ""
Write-Host "2. Configuration des services..." -ForegroundColor Yellow

# Créer les répertoires de logs
$logDir = "logs"
if (!(Test-Path $logDir)) {
    New-Item -ItemType Directory -Path $logDir
    Write-Host "✅ Répertoire logs créé" -ForegroundColor Green
}

Write-Host ""
Write-Host "3. Démarrage des microservices..." -ForegroundColor Yellow
Write-Host "   (Les services vont démarrer en arrière-plan)" -ForegroundColor Cyan

# Fonction pour démarrer un service Spring Boot
function Start-SpringBootService {
    param(
        [string]$ServiceName,
        [string]$ServicePath,
        [int]$Port
    )
    
    Write-Host "   Démarrage de $ServiceName sur le port $Port..." -ForegroundColor Cyan
    
    $logFile = "$logDir\$ServiceName.log"
    
    # Vérifier si le port est libre
    $portCheck = netstat -an | Select-String ":$Port "
    if ($portCheck) {
        Write-Host "   ⚠️  Port $Port déjà utilisé pour $ServiceName" -ForegroundColor Yellow
        return
    }
    
    # Démarrer le service
    Start-Process -FilePath "mvn" -ArgumentList "spring-boot:run" -WorkingDirectory $ServicePath -WindowStyle Hidden -RedirectStandardOutput $logFile -RedirectStandardError "$logFile.error"
    
    Write-Host "   ✅ $ServiceName démarré (logs: $logFile)" -ForegroundColor Green
}

# Démarrer Discovery Service (Eureka)
if (Test-Path "microservices\discovery-service\backend") {
    Start-SpringBootService -ServiceName "Discovery-Service" -ServicePath "microservices\discovery-service\backend" -Port 8761
    Start-Sleep -Seconds 10
} else {
    Write-Host "   ⚠️  Discovery Service non trouvé" -ForegroundColor Yellow
}

# Démarrer Gateway Service
if (Test-Path "microservices\gateway-service\backend") {
    Start-SpringBootService -ServiceName "Gateway-Service" -ServicePath "microservices\gateway-service\backend" -Port 8080
    Start-Sleep -Seconds 5
} else {
    Write-Host "   ⚠️  Gateway Service non trouvé" -ForegroundColor Yellow
}

# Démarrer Auth User Service
if (Test-Path "microservices\auth-user-service\backend") {
    Start-SpringBootService -ServiceName "Auth-User-Service" -ServicePath "microservices\auth-user-service\backend" -Port 8081
    Start-Sleep -Seconds 5
} else {
    Write-Host "   ⚠️  Auth User Service non trouvé" -ForegroundColor Yellow
}

# Démarrer Planning Performance Service
if (Test-Path "microservices\planning-performance-service\backend") {
    Start-SpringBootService -ServiceName "Planning-Performance-Service" -ServicePath "microservices\planning-performance-service\backend" -Port 8082
    Start-Sleep -Seconds 5
} else {
    Write-Host "   ⚠️  Planning Performance Service non trouvé" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "4. Démarrage du Frontend Angular..." -ForegroundColor Yellow

# Démarrer le frontend Angular
if (Test-Path "microservices\auth-user-service\frontend") {
    Write-Host "   Installation des dépendances npm..." -ForegroundColor Cyan
    Set-Location "microservices\auth-user-service\frontend"
    
    # Installer les dépendances si nécessaire
    if (!(Test-Path "node_modules")) {
        npm install
    }
    
    Write-Host "   Démarrage du serveur Angular sur le port 4201..." -ForegroundColor Cyan
    $frontendLogFile = "..\..\..logs\frontend.log"
    Start-Process -FilePath "ng" -ArgumentList "serve", "--port", "4201", "--host", "0.0.0.0" -WindowStyle Hidden -RedirectStandardOutput $frontendLogFile -RedirectStandardError "$frontendLogFile.error"
    
    Set-Location "..\..\..\"
    Write-Host "   ✅ Frontend Angular démarré (logs: $frontendLogFile)" -ForegroundColor Green
} else {
    Write-Host "   ❌ Frontend Angular non trouvé" -ForegroundColor Red
}

Write-Host ""
Write-Host "5. Vérification des services..." -ForegroundColor Yellow
Write-Host "   Attente du démarrage complet (30 secondes)..." -ForegroundColor Cyan
Start-Sleep -Seconds 30

# Fonction pour vérifier un service
function Test-Service {
    param(
        [string]$ServiceName,
        [string]$Url
    )
    
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "   ✅ $ServiceName : OK" -ForegroundColor Green
        } else {
            Write-Host "   ⚠️  $ServiceName : Status $($response.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "   ❌ $ServiceName : Non accessible" -ForegroundColor Red
    }
}

# Vérifier les services
Test-Service -ServiceName "Discovery Service" -Url "http://localhost:8761"
Test-Service -ServiceName "Gateway Service" -Url "http://localhost:8080/actuator/health"
Test-Service -ServiceName "Auth User Service" -Url "http://localhost:8081/actuator/health"
Test-Service -ServiceName "Planning Performance Service" -Url "http://localhost:8082/actuator/health"
Test-Service -ServiceName "Frontend Angular" -Url "http://localhost:4201"

Write-Host ""
Write-Host "=== Intégration des Microservices Démarrée ===" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 URLs d'accès :" -ForegroundColor Cyan
Write-Host "   • Frontend Principal    : http://localhost:4201" -ForegroundColor White
Write-Host "   • Gateway API          : http://localhost:8080" -ForegroundColor White
Write-Host "   • Discovery Service    : http://localhost:8761" -ForegroundColor White
Write-Host "   • Auth User Service    : http://localhost:8081" -ForegroundColor White
Write-Host "   • Planning Performance : http://localhost:8082" -ForegroundColor White
Write-Host ""
Write-Host "📋 Test d'intégration :" -ForegroundColor Cyan
Write-Host "   1. Accédez à http://localhost:4201" -ForegroundColor White
Write-Host "   2. Connectez-vous avec le compte admin" -ForegroundColor White
Write-Host "   3. Cliquez sur 'Planning' dans la navigation" -ForegroundColor White
Write-Host "   4. Testez le bouton 'Charger les entraînements'" -ForegroundColor White
Write-Host ""
Write-Host "Logs disponibles dans le repertoire 'logs/'" -ForegroundColor Cyan
Write-Host ""
Write-Host "Pour arrêter les services, fermez cette fenêtre ou utilisez Ctrl+C" -ForegroundColor Yellow

# Garder le script ouvert
Read-Host "Appuyez sur Entrée pour arrêter tous les services"
