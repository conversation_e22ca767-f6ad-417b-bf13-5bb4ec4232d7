# Dockerfile pour Medical Admin Service Frontend
# Étape 1: Build de l'application Angular
FROM node:20-alpine AS builder

# Définir le répertoire de travail
WORKDIR /app

# Copier les fichiers de configuration npm
COPY package*.json ./

# Installer les dépendances
RUN npm ci --only=production

# Copier le code source
COPY . .

# Construire l'application pour la production
RUN npm run build:prod

# Étape 2: Serveur web Nginx
FROM nginx:alpine

# Copier la configuration Nginx personnalisée
COPY nginx.conf /etc/nginx/nginx.conf

# Copier les fichiers buildés depuis l'étape précédente
COPY --from=builder /app/dist/medical-admin-frontend /usr/share/nginx/html

# Créer un utilisateur non-root
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# Ajuster les permissions
RUN chown -R appuser:appgroup /usr/share/nginx/html && \
    chown -R appuser:appgroup /var/cache/nginx && \
    chown -R appuser:appgroup /var/log/nginx && \
    chown -R appuser:appgroup /etc/nginx/conf.d

# Créer les répertoires nécessaires avec les bonnes permissions
RUN touch /var/run/nginx.pid && \
    chown -R appuser:appgroup /var/run/nginx.pid

# Utiliser l'utilisateur non-root
USER appuser

# Exposer le port
EXPOSE 8080

# Démarrer Nginx
CMD ["nginx", "-g", "daemon off;"]
