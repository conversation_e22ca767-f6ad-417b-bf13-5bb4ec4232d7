package com.sprintbot.medicaladmin.controller;

import com.sprintbot.medicaladmin.entity.DemandeAdministrative;
import com.sprintbot.medicaladmin.service.DemandeAdministrativeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Contrôleur REST pour la gestion des demandes administratives
 * Expose les APIs pour le workflow de validation des demandes
 */
@RestController
@RequestMapping("/api/demandes-administratives")
@Tag(name = "Demandes Administratives", description = "APIs pour la gestion des demandes administratives")
@CrossOrigin(origins = "*")
public class DemandeAdministrativeController {

    private static final Logger logger = LoggerFactory.getLogger(DemandeAdministrativeController.class);

    @Autowired
    private DemandeAdministrativeService demandeService;

    // CRUD Operations
    @PostMapping
    @Operation(summary = "Créer une nouvelle demande", description = "Soumet une nouvelle demande administrative")
    public ResponseEntity<DemandeAdministrative> creerDemande(@Valid @RequestBody DemandeAdministrative demande) {
        logger.info("Création d'une nouvelle demande par le demandeur ID: {}", demande.getDemandeurId());
        
        try {
            DemandeAdministrative created = demandeService.creerDemande(demande);
            return ResponseEntity.status(HttpStatus.CREATED).body(created);
        } catch (Exception e) {
            logger.error("Erreur lors de la création de la demande: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "Obtenir une demande par ID", description = "Récupère les détails d'une demande spécifique")
    public ResponseEntity<DemandeAdministrative> obtenirDemande(
            @Parameter(description = "ID de la demande") @PathVariable Long id) {
        
        Optional<DemandeAdministrative> demande = demandeService.obtenirDemande(id);
        return demande.map(ResponseEntity::ok)
                     .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping
    @Operation(summary = "Lister toutes les demandes", description = "Récupère la liste paginée de toutes les demandes")
    public ResponseEntity<Page<DemandeAdministrative>> obtenirToutesDemandes(
            @Parameter(description = "Numéro de page (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Taille de la page") @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<DemandeAdministrative> demandes = demandeService.obtenirDemandesPaginees(pageable);
        return ResponseEntity.ok(demandes);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Mettre à jour une demande", description = "Met à jour une demande existante")
    public ResponseEntity<DemandeAdministrative> mettreAJourDemande(
            @Parameter(description = "ID de la demande") @PathVariable Long id,
            @Valid @RequestBody DemandeAdministrative demande) {
        
        try {
            demande.setId(id);
            DemandeAdministrative updated = demandeService.mettreAJourDemande(demande);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            logger.error("Erreur lors de la mise à jour de la demande ID {}: {}", id, e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Supprimer une demande", description = "Supprime une demande")
    public ResponseEntity<Void> supprimerDemande(
            @Parameter(description = "ID de la demande") @PathVariable Long id) {
        
        try {
            demandeService.supprimerDemande(id);
            return ResponseEntity.noContent().build();
        } catch (RuntimeException e) {
            logger.error("Erreur lors de la suppression de la demande ID {}: {}", id, e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    // Gestion du workflow
    @PostMapping("/soumettre")
    @Operation(summary = "Soumettre une nouvelle demande", description = "Soumet une nouvelle demande avec workflow automatique")
    public ResponseEntity<DemandeAdministrative> soumettreNouveauDemande(@Valid @RequestBody DemandeAdministrative demande) {
        logger.info("Soumission d'une nouvelle demande par le demandeur ID: {}", demande.getDemandeurId());
        
        try {
            DemandeAdministrative submitted = demandeService.soumettreNouveauDemande(demande);
            return ResponseEntity.status(HttpStatus.CREATED).body(submitted);
        } catch (Exception e) {
            logger.error("Erreur lors de la soumission de la demande: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    @PutMapping("/{id}/commencer-traitement")
    @Operation(summary = "Commencer le traitement", description = "Démarre le traitement d'une demande par un approbateur")
    public ResponseEntity<DemandeAdministrative> commencerTraitement(
            @Parameter(description = "ID de la demande") @PathVariable Long id,
            @RequestBody Map<String, Long> approbateur) {
        
        try {
            Long approvateurId = approbateur.get("approvateurId");
            DemandeAdministrative updated = demandeService.commencerTraitement(id, approvateurId);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            logger.error("Erreur lors du début de traitement de la demande ID {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/{id}/valider")
    @Operation(summary = "Valider une demande", description = "Valide une demande avec commentaire")
    public ResponseEntity<DemandeAdministrative> validerDemande(
            @Parameter(description = "ID de la demande") @PathVariable Long id,
            @RequestBody Map<String, String> validation) {
        
        try {
            String commentaire = validation.get("commentaire");
            DemandeAdministrative updated = demandeService.validerDemande(id, commentaire);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            logger.error("Erreur lors de la validation de la demande ID {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/{id}/rejeter")
    @Operation(summary = "Rejeter une demande", description = "Rejette une demande avec commentaire")
    public ResponseEntity<DemandeAdministrative> rejeterDemande(
            @Parameter(description = "ID de la demande") @PathVariable Long id,
            @RequestBody Map<String, String> rejet) {
        
        try {
            String commentaire = rejet.get("commentaire");
            DemandeAdministrative updated = demandeService.rejeterDemande(id, commentaire);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            logger.error("Erreur lors du rejet de la demande ID {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/{id}/suspendre")
    @Operation(summary = "Suspendre une demande", description = "Suspend une demande avec commentaire")
    public ResponseEntity<DemandeAdministrative> suspendreDemande(
            @Parameter(description = "ID de la demande") @PathVariable Long id,
            @RequestBody Map<String, String> suspension) {
        
        try {
            String commentaire = suspension.get("commentaire");
            DemandeAdministrative updated = demandeService.suspendreDemande(id, commentaire);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            logger.error("Erreur lors de la suspension de la demande ID {}: {}", id, e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    // Gestion des validations multiples
    @PutMapping("/{id}/validation-coach")
    @Operation(summary = "Donner la validation coach", description = "Donne ou refuse la validation coach")
    public ResponseEntity<DemandeAdministrative> donnerValidationCoach(
            @Parameter(description = "ID de la demande") @PathVariable Long id,
            @RequestBody Map<String, Boolean> validation) {
        
        try {
            Boolean approuve = validation.get("validation");
            DemandeAdministrative updated = demandeService.donnerValidationCoach(id, approuve);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            logger.error("Erreur lors de la validation coach de la demande ID {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/{id}/validation-medical")
    @Operation(summary = "Donner la validation médicale", description = "Donne ou refuse la validation médicale")
    public ResponseEntity<DemandeAdministrative> donnerValidationMedical(
            @Parameter(description = "ID de la demande") @PathVariable Long id,
            @RequestBody Map<String, Boolean> validation) {
        
        try {
            Boolean approuve = validation.get("validation");
            DemandeAdministrative updated = demandeService.donnerValidationMedical(id, approuve);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            logger.error("Erreur lors de la validation médicale de la demande ID {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/{id}/validation-financier")
    @Operation(summary = "Donner la validation financière", description = "Donne ou refuse la validation financière")
    public ResponseEntity<DemandeAdministrative> donnerValidationFinancier(
            @Parameter(description = "ID de la demande") @PathVariable Long id,
            @RequestBody Map<String, Boolean> validation) {
        
        try {
            Boolean approuve = validation.get("validation");
            DemandeAdministrative updated = demandeService.donnerValidationFinancier(id, approuve);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            logger.error("Erreur lors de la validation financière de la demande ID {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    // Recherches par demandeur
    @GetMapping("/demandeur/{demandeurId}")
    @Operation(summary = "Obtenir les demandes d'un demandeur", description = "Récupère l'historique des demandes d'un demandeur")
    public ResponseEntity<List<DemandeAdministrative>> obtenirDemandesParDemandeur(
            @Parameter(description = "ID du demandeur") @PathVariable Long demandeurId) {
        
        List<DemandeAdministrative> demandes = demandeService.obtenirDemandesParDemandeur(demandeurId);
        return ResponseEntity.ok(demandes);
    }

    // Recherches par approbateur
    @GetMapping("/approbateur/{approvateurId}")
    @Operation(summary = "Obtenir les demandes d'un approbateur", description = "Récupère les demandes gérées par un approbateur")
    public ResponseEntity<List<DemandeAdministrative>> obtenirDemandesParApprobateur(
            @Parameter(description = "ID de l'approbateur") @PathVariable Long approvateurId) {
        
        List<DemandeAdministrative> demandes = demandeService.obtenirDemandesParApprobateur(approvateurId);
        return ResponseEntity.ok(demandes);
    }

    // Recherches par statut
    @GetMapping("/en-attente")
    @Operation(summary = "Obtenir les demandes en attente", description = "Récupère toutes les demandes en attente de traitement")
    public ResponseEntity<List<DemandeAdministrative>> obtenirDemandesEnAttente() {
        List<DemandeAdministrative> demandes = demandeService.obtenirDemandesEnAttente();
        return ResponseEntity.ok(demandes);
    }

    @GetMapping("/en-cours")
    @Operation(summary = "Obtenir les demandes en cours", description = "Récupère toutes les demandes en cours de traitement")
    public ResponseEntity<List<DemandeAdministrative>> obtenirDemandesEnCours() {
        List<DemandeAdministrative> demandes = demandeService.obtenirDemandesEnCours();
        return ResponseEntity.ok(demandes);
    }

    @GetMapping("/urgentes")
    @Operation(summary = "Obtenir les demandes urgentes", description = "Récupère toutes les demandes avec priorité élevée ou urgente")
    public ResponseEntity<List<DemandeAdministrative>> obtenirDemandesUrgentes() {
        List<DemandeAdministrative> demandes = demandeService.obtenirDemandesUrgentes();
        return ResponseEntity.ok(demandes);
    }

    @GetMapping("/urgentes-en-cours")
    @Operation(summary = "Obtenir les demandes urgentes en cours", description = "Récupère les demandes urgentes en cours de traitement")
    public ResponseEntity<List<DemandeAdministrative>> obtenirDemandesUrgentesEnCours() {
        List<DemandeAdministrative> demandes = demandeService.obtenirDemandesUrgentesEnCours();
        return ResponseEntity.ok(demandes);
    }

    // Recherches pour validations
    @GetMapping("/validation-coach/en-attente")
    @Operation(summary = "Demandes en attente de validation coach", description = "Récupère les demandes en attente de validation coach")
    public ResponseEntity<List<DemandeAdministrative>> obtenirDemandesEnAttenteValidationCoach() {
        List<DemandeAdministrative> demandes = demandeService.obtenirDemandesEnAttenteValidationCoach();
        return ResponseEntity.ok(demandes);
    }

    @GetMapping("/validation-medical/en-attente")
    @Operation(summary = "Demandes en attente de validation médicale", description = "Récupère les demandes en attente de validation médicale")
    public ResponseEntity<List<DemandeAdministrative>> obtenirDemandesEnAttenteValidationMedical() {
        List<DemandeAdministrative> demandes = demandeService.obtenirDemandesEnAttenteValidationMedical();
        return ResponseEntity.ok(demandes);
    }

    @GetMapping("/validation-financier/en-attente")
    @Operation(summary = "Demandes en attente de validation financière", description = "Récupère les demandes en attente de validation financière")
    public ResponseEntity<List<DemandeAdministrative>> obtenirDemandesEnAttenteValidationFinancier() {
        List<DemandeAdministrative> demandes = demandeService.obtenirDemandesEnAttenteValidationFinancier();
        return ResponseEntity.ok(demandes);
    }

    @GetMapping("/pretes-validation-finale")
    @Operation(summary = "Demandes prêtes pour validation finale", description = "Récupère les demandes ayant toutes les validations nécessaires")
    public ResponseEntity<List<DemandeAdministrative>> obtenirDemandesPretesValidationFinale() {
        List<DemandeAdministrative> demandes = demandeService.obtenirDemandesPretesValidationFinale();
        return ResponseEntity.ok(demandes);
    }

    // Alertes et échéances
    @GetMapping("/alertes/echues")
    @Operation(summary = "Obtenir les demandes échues", description = "Récupère les demandes avec échéance dépassée")
    public ResponseEntity<List<DemandeAdministrative>> obtenirDemandesEchues() {
        List<DemandeAdministrative> demandes = demandeService.obtenirDemandesEchues();
        return ResponseEntity.ok(demandes);
    }

    @GetMapping("/alertes/echeance-proche")
    @Operation(summary = "Obtenir les demandes à échéance proche", description = "Récupère les demandes avec échéance dans les 7 prochains jours")
    public ResponseEntity<List<DemandeAdministrative>> obtenirDemandesEcheanceProche() {
        List<DemandeAdministrative> demandes = demandeService.obtenirDemandesEcheanceProche();
        return ResponseEntity.ok(demandes);
    }

    @GetMapping("/alertes/anciennes-en-attente")
    @Operation(summary = "Obtenir les anciennes demandes en attente", description = "Récupère les demandes en attente depuis plus de 7 jours")
    public ResponseEntity<List<DemandeAdministrative>> obtenirDemandesAnciennesEnAttente() {
        List<DemandeAdministrative> demandes = demandeService.obtenirDemandesAnciennesEnAttente();
        return ResponseEntity.ok(demandes);
    }

    @GetMapping("/alertes/anciennes-en-traitement")
    @Operation(summary = "Obtenir les anciennes demandes en traitement", description = "Récupère les demandes en traitement depuis plus de 14 jours")
    public ResponseEntity<List<DemandeAdministrative>> obtenirDemandesAnciennesEnTraitement() {
        List<DemandeAdministrative> demandes = demandeService.obtenirDemandesAnciennesEnTraitement();
        return ResponseEntity.ok(demandes);
    }

    // Recherches par type et période
    @GetMapping("/type/{typeDemande}")
    @Operation(summary = "Obtenir les demandes par type", description = "Récupère les demandes par type")
    public ResponseEntity<List<DemandeAdministrative>> obtenirDemandesParType(
            @Parameter(description = "Type de demande") @PathVariable String typeDemande) {
        
        List<DemandeAdministrative> demandes = demandeService.obtenirDemandesParType(typeDemande);
        return ResponseEntity.ok(demandes);
    }

    @GetMapping("/periode")
    @Operation(summary = "Obtenir les demandes par période", description = "Récupère les demandes sur une période donnée")
    public ResponseEntity<List<DemandeAdministrative>> obtenirDemandesParPeriode(
            @Parameter(description = "Date de début") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateDebut,
            @Parameter(description = "Date de fin") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateFin) {
        
        List<DemandeAdministrative> demandes = demandeService.obtenirDemandesParPeriode(dateDebut, dateFin);
        return ResponseEntity.ok(demandes);
    }

    // Statistiques
    @GetMapping("/statistiques/demandeur/{demandeurId}/count")
    @Operation(summary = "Compter les demandes par demandeur", description = "Compte le nombre de demandes d'un demandeur")
    public ResponseEntity<Long> compterDemandesParDemandeur(
            @Parameter(description = "ID du demandeur") @PathVariable Long demandeurId) {
        
        long count = demandeService.compterDemandesParDemandeur(demandeurId);
        return ResponseEntity.ok(count);
    }

    @GetMapping("/statistiques/en-attente")
    @Operation(summary = "Compter les demandes en attente", description = "Compte le nombre de demandes en attente")
    public ResponseEntity<Long> compterDemandesEnAttente() {
        long count = demandeService.compterDemandesEnAttente();
        return ResponseEntity.ok(count);
    }

    @GetMapping("/statistiques/en-traitement")
    @Operation(summary = "Compter les demandes en traitement", description = "Compte le nombre de demandes en traitement")
    public ResponseEntity<Long> compterDemandesEnTraitement() {
        long count = demandeService.compterDemandesEnTraitement();
        return ResponseEntity.ok(count);
    }

    @GetMapping("/statistiques/aujourd-hui")
    @Operation(summary = "Compter les demandes d'aujourd'hui", description = "Compte le nombre de demandes soumises aujourd'hui")
    public ResponseEntity<Long> compterDemandesAujourdhui() {
        long count = demandeService.compterDemandesAujourdhui();
        return ResponseEntity.ok(count);
    }

    // Gestion des coûts
    @GetMapping("/cout-superieur/{montant}")
    @Operation(summary = "Obtenir les demandes avec coût supérieur", description = "Récupère les demandes avec un coût estimé supérieur au montant")
    public ResponseEntity<List<DemandeAdministrative>> obtenirDemandesAvecCoutSuperieur(
            @Parameter(description = "Montant seuil") @PathVariable Double montant) {
        
        List<DemandeAdministrative> demandes = demandeService.obtenirDemandesAvecCoutSuperieur(montant);
        return ResponseEntity.ok(demandes);
    }

    @GetMapping("/statistiques/cout-estime-validees")
    @Operation(summary = "Calculer le coût estimé des demandes validées", description = "Calcule le coût total estimé des demandes validées sur une période")
    public ResponseEntity<Double> calculerCoutEstimeValidees(
            @Parameter(description = "Date de début") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateDebut,
            @Parameter(description = "Date de fin") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateFin) {
        
        Double cout = demandeService.calculerCoutEstimeValidees(dateDebut, dateFin);
        return ResponseEntity.ok(cout != null ? cout : 0.0);
    }

    @GetMapping("/statistiques/cout-reel")
    @Operation(summary = "Calculer le coût réel", description = "Calcule le coût réel total sur une période")
    public ResponseEntity<Double> calculerCoutReel(
            @Parameter(description = "Date de début") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateDebut,
            @Parameter(description = "Date de fin") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateFin) {
        
        Double cout = demandeService.calculerCoutReel(dateDebut, dateFin);
        return ResponseEntity.ok(cout != null ? cout : 0.0);
    }

    // Recherche avec filtres
    @GetMapping("/recherche")
    @Operation(summary = "Rechercher avec filtres", description = "Recherche les demandes avec des filtres multiples")
    public ResponseEntity<Page<DemandeAdministrative>> rechercherAvecFiltres(
            @RequestParam(required = false) Long demandeurId,
            @RequestParam(required = false) String typeDemandeur,
            @RequestParam(required = false) Long approvateurId,
            @RequestParam(required = false) String typeDemande,
            @RequestParam(required = false) String statut,
            @RequestParam(required = false) String priorite,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateDebut,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateFin,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<DemandeAdministrative> demandes = demandeService.rechercherAvecFiltres(
                demandeurId, typeDemandeur, approvateurId, typeDemande, statut, priorite, dateDebut, dateFin, pageable);
        return ResponseEntity.ok(demandes);
    }

    @GetMapping("/recherche/texte")
    @Operation(summary = "Recherche textuelle", description = "Recherche les demandes par texte libre")
    public ResponseEntity<List<DemandeAdministrative>> rechercherParTexte(
            @Parameter(description = "Terme de recherche") @RequestParam String searchTerm) {
        
        List<DemandeAdministrative> demandes = demandeService.rechercherParTexte(searchTerm);
        return ResponseEntity.ok(demandes);
    }
}
