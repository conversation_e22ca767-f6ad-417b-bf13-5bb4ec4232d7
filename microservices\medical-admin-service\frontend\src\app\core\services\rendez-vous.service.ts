import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { 
  RendezVousMedical, 
  RendezVousFilters, 
  RendezVousStats,
  TypeRendezVous,
  StatutRendezVous,
  PrioriteRendezVous,
  CreneauDisponible
} from '@core/models/rendez-vous.model';
import { PagedResponse, FilterOptions } from '@core/models/common.model';

@Injectable({
  providedIn: 'root'
})
export class RendezVousService {
  private readonly endpoint = '/api/rendez-vous';

  constructor(private apiService: ApiService) {}

  // CRUD Operations
  getAllRendezVous(options?: FilterOptions): Observable<PagedResponse<RendezVousMedical>> {
    return this.apiService.getPaged<RendezVousMedical>(this.endpoint, options);
  }

  getRendezVousById(id: number): Observable<RendezVousMedical> {
    return this.apiService.getById<RendezVousMedical>(this.endpoint, id);
  }

  creerRendezVous(rendezVous: RendezVousMedical): Observable<RendezVousMedical> {
    return this.apiService.post<RendezVousMedical>(this.endpoint, rendezVous);
  }

  modifierRendezVous(id: number, rendezVous: RendezVousMedical): Observable<RendezVousMedical> {
    return this.apiService.put<RendezVousMedical>(this.endpoint, id, rendezVous);
  }

  supprimerRendezVous(id: number): Observable<void> {
    return this.apiService.delete<void>(this.endpoint, id);
  }

  // Recherche et filtrage
  rechercherRendezVous(filters: RendezVousFilters, options?: FilterOptions): Observable<PagedResponse<RendezVousMedical>> {
    const params = { ...filters, ...options };
    return this.apiService.get<PagedResponse<RendezVousMedical>>(`${this.endpoint}/recherche`, params);
  }

  getRendezVousParJoueur(joueurId: number, options?: FilterOptions): Observable<PagedResponse<RendezVousMedical>> {
    return this.apiService.getPaged<RendezVousMedical>(`${this.endpoint}/joueur/${joueurId}`, options);
  }

  getRendezVousParStaffMedical(staffId: number, options?: FilterOptions): Observable<PagedResponse<RendezVousMedical>> {
    return this.apiService.getPaged<RendezVousMedical>(`${this.endpoint}/staff-medical/${staffId}`, options);
  }

  getRendezVousParDate(date: string): Observable<RendezVousMedical[]> {
    return this.apiService.get<RendezVousMedical[]>(`${this.endpoint}/date/${date}`);
  }

  getRendezVousParPeriode(dateDebut: string, dateFin: string, options?: FilterOptions): Observable<PagedResponse<RendezVousMedical>> {
    const params = { dateDebut, dateFin, ...options };
    return this.apiService.get<PagedResponse<RendezVousMedical>>(`${this.endpoint}/periode`, params);
  }

  getRendezVousParStatut(statut: StatutRendezVous, options?: FilterOptions): Observable<PagedResponse<RendezVousMedical>> {
    return this.apiService.getPaged<RendezVousMedical>(`${this.endpoint}/statut/${statut}`, options);
  }

  getRendezVousParPriorite(priorite: PrioriteRendezVous, options?: FilterOptions): Observable<PagedResponse<RendezVousMedical>> {
    return this.apiService.getPaged<RendezVousMedical>(`${this.endpoint}/priorite/${priorite}`, options);
  }

  // Actions spécifiques sur les rendez-vous
  confirmerRendezVous(id: number): Observable<RendezVousMedical> {
    return this.apiService.executeAction<RendezVousMedical>(this.endpoint, id, 'confirmer');
  }

  annulerRendezVous(id: number, motif?: string): Observable<RendezVousMedical> {
    const data = motif ? { motif } : {};
    return this.apiService.executeAction<RendezVousMedical>(this.endpoint, id, 'annuler', data);
  }

  reporterRendezVous(id: number, nouvelleDate: string, nouvelleHeure: string): Observable<RendezVousMedical> {
    const data = { nouvelleDate, nouvelleHeure };
    return this.apiService.executeAction<RendezVousMedical>(this.endpoint, id, 'reporter', data);
  }

  commencerRendezVous(id: number): Observable<RendezVousMedical> {
    return this.apiService.executeAction<RendezVousMedical>(this.endpoint, id, 'commencer');
  }

  terminerRendezVous(id: number, compteRendu?: string, prescriptions?: string): Observable<RendezVousMedical> {
    const data = { compteRendu, prescriptions };
    return this.apiService.executeAction<RendezVousMedical>(this.endpoint, id, 'terminer', data);
  }

  envoyerRappel(id: number): Observable<RendezVousMedical> {
    return this.apiService.executeAction<RendezVousMedical>(this.endpoint, id, 'envoyer-rappel');
  }

  changerPriorite(id: number, nouvellePriorite: PrioriteRendezVous): Observable<RendezVousMedical> {
    return this.apiService.executeAction<RendezVousMedical>(this.endpoint, id, 'priorite', { priorite: nouvellePriorite });
  }

  // Gestion des créneaux et disponibilités
  getCreneauxDisponibles(staffId: number, date: string): Observable<CreneauDisponible[]> {
    return this.apiService.get<CreneauxDisponible[]>(`${this.endpoint}/creneaux-disponibles`, { staffId, date });
  }

  verifierDisponibilite(staffId: number, date: string, heureDebut: string, heureFin: string): Observable<boolean> {
    const params = { staffId, date, heureDebut, heureFin };
    return this.apiService.get<boolean>(`${this.endpoint}/verifier-disponibilite`, params);
  }

  getConflitsCreneaux(rendezVous: RendezVousMedical): Observable<RendezVousMedical[]> {
    return this.apiService.post<RendezVousMedical[]>(`${this.endpoint}/conflits-creneaux`, rendezVous);
  }

  // Planning et calendrier
  getPlanningJournalier(date: string, staffId?: number): Observable<RendezVousMedical[]> {
    const params = staffId ? { date, staffId } : { date };
    return this.apiService.get<RendezVousMedical[]>(`${this.endpoint}/planning-journalier`, params);
  }

  getPlanningHebdomadaire(dateDebut: string, staffId?: number): Observable<RendezVousMedical[]> {
    const params = staffId ? { dateDebut, staffId } : { dateDebut };
    return this.apiService.get<RendezVousMedical[]>(`${this.endpoint}/planning-hebdomadaire`, params);
  }

  getPlanningMensuel(annee: number, mois: number, staffId?: number): Observable<RendezVousMedical[]> {
    const params = staffId ? { annee, mois, staffId } : { annee, mois };
    return this.apiService.get<RendezVousMedical[]>(`${this.endpoint}/planning-mensuel`, params);
  }

  // Statistiques et rapports
  getStatistiques(filters?: RendezVousFilters): Observable<RendezVousStats> {
    return this.apiService.getStats<RendezVousStats>(this.endpoint, filters);
  }

  getStatistiquesParStaffMedical(staffId: number): Observable<RendezVousStats> {
    return this.apiService.get<RendezVousStats>(`${this.endpoint}/staff-medical/${staffId}/stats`);
  }

  getRendezVousAujourdhui(): Observable<RendezVousMedical[]> {
    return this.apiService.get<RendezVousMedical[]>(`${this.endpoint}/aujourd-hui`);
  }

  getRendezVousEnRetard(): Observable<RendezVousMedical[]> {
    return this.apiService.get<RendezVousMedical[]>(`${this.endpoint}/en-retard`);
  }

  getRendezVousUrgents(): Observable<RendezVousMedical[]> {
    return this.apiService.get<RendezVousMedical[]>(`${this.endpoint}/urgents`);
  }

  // Export et import
  exporterPlanning(format: 'csv' | 'excel' | 'pdf', filters?: RendezVousFilters): Observable<Blob> {
    return this.apiService.export(this.endpoint, format, filters);
  }

  importerRendezVous(file: File): Observable<any> {
    return this.apiService.uploadFile(this.endpoint, file);
  }
}
