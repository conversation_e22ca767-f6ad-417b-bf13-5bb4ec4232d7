// Configuration pour l'environnement de production
export const environment = {
  production: true,
  apiUrl: 'https://api.sprintbot.com/communication',
  wsUrl: 'wss://api.sprintbot.com/communication/ws',
  
  // Configuration WebSocket
  websocket: {
    reconnectInterval: 10000,
    maxReconnectAttempts: 5,
    heartbeatInterval: 60000
  },
  
  // Configuration des notifications
  notifications: {
    enablePush: true,
    enableEmail: true,
    enableSms: true,
    defaultDuration: 4000
  },
  
  // Configuration du chatbot
  chatbot: {
    enabled: true,
    maxMessageLength: 1000,
    typingIndicatorDelay: 800,
    suggestionCount: 3
  },
  
  // Configuration des fichiers
  fileUpload: {
    maxFileSize: 25 * 1024 * 1024, // 25MB
    allowedTypes: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'video/mp4',
      'video/webm',
      'audio/mp3',
      'audio/wav',
      'audio/m4a',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain'
    ]
  },
  
  // Configuration de la présence utilisateur
  presence: {
    heartbeatInterval: 60000,
    activityThreshold: 120000,
    offlineThreshold: 600000
  },
  
  // Configuration de l'interface
  ui: {
    theme: 'light',
    language: 'fr',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    messagesPerPage: 50,
    conversationsPerPage: 20,
    notificationsPerPage: 25
  },
  
  // Configuration du cache
  cache: {
    conversationsTtl: 600000, // 10 minutes
    messagesTtl: 1200000, // 20 minutes
    usersTtl: 1800000 // 30 minutes
  },
  
  // Configuration de sécurité
  security: {
    jwtTokenKey: 'sprintbot_token',
    refreshTokenKey: 'sprintbot_refresh_token',
    sessionTimeout: 7200000 // 2 heures
  },
  
  // Configuration des logs
  logging: {
    level: 'error',
    enableConsole: false,
    enableRemote: true
  },
  
  // Configuration des fonctionnalités
  features: {
    enableVoiceMessages: true,
    enableVideoMessages: true,
    enableFileSharing: true,
    enableMessageReactions: true,
    enableMessageReplies: true,
    enableMessageForwarding: true,
    enableMessageSearch: true,
    enableConversationArchive: true,
    enableUserBlocking: true,
    enableMessageEncryption: true
  }
};
