package com.sprintbot.communication.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * Entité représentant un message dans une conversation chatbot
 */
@Entity
@Table(name = "chatbot_messages")
@EntityListeners(AuditingEntityListener.class)
public class ChatbotMessage {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "Le contenu du message est obligatoire")
    @Size(max = 4000, message = "Le contenu ne peut pas dépasser 4000 caractères")
    @Column(name = "contenu", nullable = false, length = 4000)
    private String contenu;

    @NotNull(message = "Le type de message est obligatoire")
    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, length = 20)
    private TypeMessageChatbot type;

    @Column(name = "intention_detectee")
    private String intentionDetectee;

    @Column(name = "confiance_score")
    private Double confianceScore; // Score de confiance de l'IA (0.0 - 1.0)

    @Column(name = "temps_reponse") // en millisecondes
    private Long tempsReponse;

    @Column(name = "modele_utilise")
    private String modeleUtilise; // ex: "gpt-3.5-turbo"

    @Column(name = "tokens_utilises")
    private Integer tokensUtilises;

    @Column(name = "cout_estimation")
    private Double coutEstimation;

    @Column(name = "metadata", columnDefinition = "TEXT")
    private String metadata; // JSON pour données supplémentaires

    @Column(name = "est_utile")
    private Boolean estUtile; // Feedback utilisateur

    @Column(name = "feedback_utilisateur")
    private String feedbackUtilisateur;

    @CreatedDate
    @Column(name = "date_creation", nullable = false)
    private LocalDateTime dateCreation;

    // Relations
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "conversation_id", nullable = false)
    private ChatbotConversation conversation;

    // Constructeurs
    public ChatbotMessage() {}

    public ChatbotMessage(String contenu, TypeMessageChatbot type, ChatbotConversation conversation) {
        this.contenu = contenu;
        this.type = type;
        this.conversation = conversation;
    }

    public ChatbotMessage(ChatbotConversation conversation, TypeMessageChatbot type, String contenu) {
        this.conversation = conversation;
        this.type = type;
        this.contenu = contenu;
    }

    // Méthodes métier
    public void marquerCommeUtile() {
        this.estUtile = true;
    }

    public void marquerCommeInutile() {
        this.estUtile = false;
    }

    public void ajouterFeedback(String feedback) {
        this.feedbackUtilisateur = feedback;
    }

    public boolean estDeUtilisateur() {
        return this.type == TypeMessageChatbot.UTILISATEUR;
    }

    public boolean estDuBot() {
        return this.type == TypeMessageChatbot.BOT;
    }

    public boolean estSysteme() {
        return this.type == TypeMessageChatbot.SYSTEME;
    }

    public boolean aUneBonneConfiance() {
        return this.confianceScore != null && this.confianceScore >= 0.7;
    }

    public boolean aEteEvalue() {
        return this.estUtile != null;
    }

    // Getters et Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getContenu() {
        return contenu;
    }

    public void setContenu(String contenu) {
        this.contenu = contenu;
    }

    public TypeMessageChatbot getType() {
        return type;
    }

    public void setType(TypeMessageChatbot type) {
        this.type = type;
    }

    public String getIntentionDetectee() {
        return intentionDetectee;
    }

    public void setIntentionDetectee(String intentionDetectee) {
        this.intentionDetectee = intentionDetectee;
    }

    public Double getConfianceScore() {
        return confianceScore;
    }

    public void setConfianceScore(Double confianceScore) {
        this.confianceScore = confianceScore;
    }

    public Long getTempsReponse() {
        return tempsReponse;
    }

    public void setTempsReponse(Long tempsReponse) {
        this.tempsReponse = tempsReponse;
    }

    public String getModeleUtilise() {
        return modeleUtilise;
    }

    public void setModeleUtilise(String modeleUtilise) {
        this.modeleUtilise = modeleUtilise;
    }

    public Integer getTokensUtilises() {
        return tokensUtilises;
    }

    public void setTokensUtilises(Integer tokensUtilises) {
        this.tokensUtilises = tokensUtilises;
    }

    public Double getCoutEstimation() {
        return coutEstimation;
    }

    public void setCoutEstimation(Double coutEstimation) {
        this.coutEstimation = coutEstimation;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Boolean getEstUtile() {
        return estUtile;
    }

    public void setEstUtile(Boolean estUtile) {
        this.estUtile = estUtile;
    }

    public String getFeedbackUtilisateur() {
        return feedbackUtilisateur;
    }

    public void setFeedbackUtilisateur(String feedbackUtilisateur) {
        this.feedbackUtilisateur = feedbackUtilisateur;
    }

    public LocalDateTime getDateCreation() {
        return dateCreation;
    }

    public void setDateCreation(LocalDateTime dateCreation) {
        this.dateCreation = dateCreation;
    }

    public ChatbotConversation getConversation() {
        return conversation;
    }

    public void setConversation(ChatbotConversation conversation) {
        this.conversation = conversation;
    }

    @Override
    public String toString() {
        return "ChatbotMessage{" +
                "id=" + id +
                ", type=" + type +
                ", intentionDetectee='" + intentionDetectee + '\'' +
                ", confianceScore=" + confianceScore +
                ", dateCreation=" + dateCreation +
                '}';
    }
}
