{"name": "communication-service-frontend", "version": "1.0.0", "description": "Frontend Angular pour le service de communication SprintBot", "scripts": {"ng": "ng", "start": "ng serve --port 4204", "start:dev": "ng serve --port 4204 --configuration development", "start:docker": "ng serve --host 0.0.0.0 --port 4204 --disable-host-check", "build": "ng build", "build:prod": "ng build --configuration production", "build:docker": "ng build --configuration docker", "watch": "ng build --watch --configuration development", "test": "ng test", "test:ci": "ng test --watch=false --browsers=ChromeHeadless", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "^17.0.0", "@angular/common": "^17.0.0", "@angular/compiler": "^17.0.0", "@angular/core": "^17.0.0", "@angular/forms": "^17.0.0", "@angular/platform-browser": "^17.0.0", "@angular/platform-browser-dynamic": "^17.0.0", "@angular/router": "^17.0.0", "@angular/material": "^17.0.0", "@angular/cdk": "^17.0.0", "@angular/service-worker": "^17.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.0", "sockjs-client": "^1.6.1", "@stomp/stompjs": "^7.0.0", "emoji-picker-element": "^1.18.0", "@ctrl/ngx-emoji-mart": "^9.0.0", "ngx-file-drop": "^16.0.0", "ngx-infinite-scroll": "^17.0.0", "moment": "^2.29.4", "lodash": "^4.17.21"}, "devDependencies": {"@angular-devkit/build-angular": "^17.0.0", "@angular/cli": "^17.0.0", "@angular/compiler-cli": "^17.0.0", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "@types/sockjs-client": "^1.5.1", "@types/lodash": "^4.14.200", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.2.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}