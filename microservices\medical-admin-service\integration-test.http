# Tests d'intégration pour Medical Admin Service
# Utiliser avec l'extension REST Client de VS Code

### Variables
@baseUrl = http://localhost:8083
@contentType = application/json

### 1. Test de santé du service
GET {{baseUrl}}/actuator/health

### 2. Test des données de santé

# Créer une nouvelle donnée de santé
POST {{baseUrl}}/api/donnees-sante
Content-Type: {{contentType}}

{
  "joueurId": 1,
  "staffMedicalId": 1,
  "typeExamen": "BILAN_GENERAL",
  "dateExamen": "2024-07-29",
  "resultats": "Bilan général satisfaisant",
  "statut": "ACTIF",
  "gravite": "FAIBLE",
  "necessiteSuivi": false,
  "visibleParJoueur": true
}

### Récupérer toutes les données de santé
GET {{baseUrl}}/api/donnees-sante

### Récupérer une donnée de santé par ID
GET {{baseUrl}}/api/donnees-sante/1

### Rechercher des données de santé
GET {{baseUrl}}/api/donnees-sante/recherche?typeExamen=BILAN_GENERAL&statut=ACTIF

### Récupérer les statistiques des données de santé
GET {{baseUrl}}/api/donnees-sante/stats

### 3. Test des rendez-vous médicaux

# Créer un nouveau rendez-vous
POST {{baseUrl}}/api/rendez-vous
Content-Type: {{contentType}}

{
  "joueurId": 1,
  "staffMedicalId": 1,
  "typeRendezVous": "CONSULTATION",
  "dateRendezVous": "2024-07-30",
  "heureDebut": "09:00",
  "heureFin": "09:30",
  "lieu": "Cabinet médical",
  "description": "Consultation de routine",
  "statut": "PLANIFIE",
  "priorite": "NORMALE",
  "rappelEnvoye": false
}

### Récupérer tous les rendez-vous
GET {{baseUrl}}/api/rendez-vous

### Récupérer un rendez-vous par ID
GET {{baseUrl}}/api/rendez-vous/1

### Récupérer les rendez-vous d'aujourd'hui
GET {{baseUrl}}/api/rendez-vous/aujourd-hui

### Confirmer un rendez-vous
PUT {{baseUrl}}/api/rendez-vous/1/confirmer

### Récupérer le planning journalier
GET {{baseUrl}}/api/rendez-vous/planning-journalier?date=2024-07-30

### Vérifier la disponibilité d'un créneau
GET {{baseUrl}}/api/rendez-vous/verifier-disponibilite?staffId=1&date=2024-07-30&heureDebut=10:00&heureFin=10:30

### 4. Test des demandes administratives

# Créer une nouvelle demande administrative
POST {{baseUrl}}/api/demandes-administratives
Content-Type: {{contentType}}

{
  "demandeurId": 1,
  "typeDemandeur": "JOUEUR",
  "typeDemande": "CONGE",
  "titre": "Demande de congé médical",
  "description": "Demande de congé pour récupération suite à blessure",
  "dateSoumission": "2024-07-29",
  "priorite": "NORMALE",
  "coutEstime": 0.00,
  "necessiteValidationCoach": true,
  "necessiteValidationMedical": true,
  "necessiteValidationFinancier": false
}

### Récupérer toutes les demandes
GET {{baseUrl}}/api/demandes-administratives

### Récupérer une demande par ID
GET {{baseUrl}}/api/demandes-administratives/1

### Récupérer les demandes en attente
GET {{baseUrl}}/api/demandes-administratives/en-attente

### Donner une validation coach
PUT {{baseUrl}}/api/demandes-administratives/1/validation-coach
Content-Type: {{contentType}}

{
  "validation": true,
  "commentaire": "Validation accordée par le coach"
}

### Donner une validation médicale
PUT {{baseUrl}}/api/demandes-administratives/1/validation-medical
Content-Type: {{contentType}}

{
  "validation": true,
  "commentaire": "Validation médicale accordée"
}

### Approuver une demande
PUT {{baseUrl}}/api/demandes-administratives/1/approuver
Content-Type: {{contentType}}

{
  "commentaire": "Demande approuvée"
}

### Récupérer le workflow d'une demande
GET {{baseUrl}}/api/demandes-administratives/1/workflow

### Récupérer les statistiques des demandes
GET {{baseUrl}}/api/demandes-administratives/stats

### 5. Tests de filtrage et pagination

# Pagination des données de santé
GET {{baseUrl}}/api/donnees-sante?page=0&size=10&sort=dateExamen&direction=DESC

# Filtrage par joueur
GET {{baseUrl}}/api/donnees-sante/joueur/1?page=0&size=5

# Filtrage par période
GET {{baseUrl}}/api/donnees-sante/periode?dateDebut=2024-07-01&dateFin=2024-07-31

# Recherche avec filtres multiples
GET {{baseUrl}}/api/rendez-vous/recherche?statut=PLANIFIE&priorite=ELEVEE&dateDebut=2024-07-29

### 6. Tests d'actions spécifiques

# Changer le statut d'une donnée de santé
PUT {{baseUrl}}/api/donnees-sante/1/statut
Content-Type: {{contentType}}

{
  "statut": "EN_TRAITEMENT"
}

# Marquer comme guéri
PUT {{baseUrl}}/api/donnees-sante/1/guerir
Content-Type: {{contentType}}

{
  "dateGuerison": "2024-07-29"
}

# Annuler un rendez-vous
PUT {{baseUrl}}/api/rendez-vous/1/annuler
Content-Type: {{contentType}}

{
  "motif": "Indisponibilité du patient"
}

# Reporter un rendez-vous
PUT {{baseUrl}}/api/rendez-vous/1/reporter
Content-Type: {{contentType}}

{
  "nouvelleDate": "2024-08-01",
  "nouvelleHeure": "14:00"
}

### 7. Tests d'export

# Export des données de santé en CSV
GET {{baseUrl}}/api/donnees-sante/export?format=csv

# Export du planning en Excel
GET {{baseUrl}}/api/rendez-vous/export?format=excel&dateDebut=2024-07-01&dateFin=2024-07-31

# Export des demandes en PDF
GET {{baseUrl}}/api/demandes-administratives/export?format=pdf&statut=VALIDEE

### 8. Tests de validation

# Test de validation des données
POST {{baseUrl}}/api/donnees-sante/valider
Content-Type: {{contentType}}

{
  "joueurId": 1,
  "typeExamen": "BILAN_GENERAL",
  "dateExamen": "2024-07-29"
}

# Test de vérification des conflits de créneaux
POST {{baseUrl}}/api/rendez-vous/conflits-creneaux
Content-Type: {{contentType}}

{
  "staffMedicalId": 1,
  "dateRendezVous": "2024-07-30",
  "heureDebut": "09:00",
  "heureFin": "09:30"
}

### 9. Tests d'erreur

# Test avec ID inexistant
GET {{baseUrl}}/api/donnees-sante/999

# Test avec données invalides
POST {{baseUrl}}/api/donnees-sante
Content-Type: {{contentType}}

{
  "typeExamen": "INVALID_TYPE",
  "dateExamen": "invalid-date"
}

### 10. Tests de performance

# Test avec pagination importante
GET {{baseUrl}}/api/donnees-sante?page=0&size=100

# Test de recherche complexe
GET {{baseUrl}}/api/demandes-administratives/recherche?typeDemande=MATERIEL&statut=EN_ATTENTE&priorite=ELEVEE&dateDebut=2024-01-01&dateFin=2024-12-31
