import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { 
  DemandeAdministrative, 
  DemandeAdministrativeFilters, 
  DemandeAdministrativeStats,
  TypeDemande,
  StatutDemande,
  PrioriteDemande,
  ValidationWorkflow
} from '@core/models/demande-administrative.model';
import { PagedResponse, FilterOptions } from '@core/models/common.model';

@Injectable({
  providedIn: 'root'
})
export class DemandesAdministrativesService {
  private readonly endpoint = '/api/demandes-administratives';

  constructor(private apiService: ApiService) {}

  // CRUD Operations
  getAllDemandes(options?: FilterOptions): Observable<PagedResponse<DemandeAdministrative>> {
    return this.apiService.getPaged<DemandeAdministrative>(this.endpoint, options);
  }

  getDemandeById(id: number): Observable<DemandeAdministrative> {
    return this.apiService.getById<DemandeAdministrative>(this.endpoint, id);
  }

  creerDemande(demande: DemandeAdministrative): Observable<DemandeAdministrative> {
    return this.apiService.post<DemandeAdministrative>(this.endpoint, demande);
  }

  modifierDemande(id: number, demande: DemandeAdministrative): Observable<DemandeAdministrative> {
    return this.apiService.put<DemandeAdministrative>(this.endpoint, id, demande);
  }

  supprimerDemande(id: number): Observable<void> {
    return this.apiService.delete<void>(this.endpoint, id);
  }

  // Recherche et filtrage
  rechercherDemandes(filters: DemandeAdministrativeFilters, options?: FilterOptions): Observable<PagedResponse<DemandeAdministrative>> {
    const params = { ...filters, ...options };
    return this.apiService.get<PagedResponse<DemandeAdministrative>>(`${this.endpoint}/recherche`, params);
  }

  getDemandesParDemandeur(demandeurId: number, options?: FilterOptions): Observable<PagedResponse<DemandeAdministrative>> {
    return this.apiService.getPaged<DemandeAdministrative>(`${this.endpoint}/demandeur/${demandeurId}`, options);
  }

  getDemandesParType(type: TypeDemande, options?: FilterOptions): Observable<PagedResponse<DemandeAdministrative>> {
    return this.apiService.getPaged<DemandeAdministrative>(`${this.endpoint}/type/${type}`, options);
  }

  getDemandesParStatut(statut: StatutDemande, options?: FilterOptions): Observable<PagedResponse<DemandeAdministrative>> {
    return this.apiService.getPaged<DemandeAdministrative>(`${this.endpoint}/statut/${statut}`, options);
  }

  getDemandesParPriorite(priorite: PrioriteDemande, options?: FilterOptions): Observable<PagedResponse<DemandeAdministrative>> {
    return this.apiService.getPaged<DemandeAdministrative>(`${this.endpoint}/priorite/${priorite}`, options);
  }

  getDemandesParPeriode(dateDebut: string, dateFin: string, options?: FilterOptions): Observable<PagedResponse<DemandeAdministrative>> {
    const params = { dateDebut, dateFin, ...options };
    return this.apiService.get<PagedResponse<DemandeAdministrative>>(`${this.endpoint}/periode`, params);
  }

  // Workflow de validation
  donnerValidationCoach(id: number, validation: boolean, commentaire?: string): Observable<DemandeAdministrative> {
    const data = { validation, commentaire };
    return this.apiService.executeAction<DemandeAdministrative>(this.endpoint, id, 'validation-coach', data);
  }

  donnerValidationMedical(id: number, validation: boolean, commentaire?: string): Observable<DemandeAdministrative> {
    const data = { validation, commentaire };
    return this.apiService.executeAction<DemandeAdministrative>(this.endpoint, id, 'validation-medical', data);
  }

  donnerValidationFinancier(id: number, validation: boolean, commentaire?: string): Observable<DemandeAdministrative> {
    const data = { validation, commentaire };
    return this.apiService.executeAction<DemandeAdministrative>(this.endpoint, id, 'validation-financier', data);
  }

  approuverDemande(id: number, commentaire?: string): Observable<DemandeAdministrative> {
    const data = commentaire ? { commentaire } : {};
    return this.apiService.executeAction<DemandeAdministrative>(this.endpoint, id, 'approuver', data);
  }

  rejeterDemande(id: number, motif: string): Observable<DemandeAdministrative> {
    return this.apiService.executeAction<DemandeAdministrative>(this.endpoint, id, 'rejeter', { motif });
  }

  suspendreDemande(id: number, motif: string): Observable<DemandeAdministrative> {
    return this.apiService.executeAction<DemandeAdministrative>(this.endpoint, id, 'suspendre', { motif });
  }

  reprendreDemande(id: number): Observable<DemandeAdministrative> {
    return this.apiService.executeAction<DemandeAdministrative>(this.endpoint, id, 'reprendre');
  }

  // Gestion des coûts
  mettreAJourCoutEstime(id: number, coutEstime: number): Observable<DemandeAdministrative> {
    return this.apiService.executeAction<DemandeAdministrative>(this.endpoint, id, 'cout-estime', { coutEstime });
  }

  mettreAJourCoutReel(id: number, coutReel: number): Observable<DemandeAdministrative> {
    return this.apiService.executeAction<DemandeAdministrative>(this.endpoint, id, 'cout-reel', { coutReel });
  }

  // Gestion des priorités et échéances
  changerPriorite(id: number, nouvellePriorite: PrioriteDemande): Observable<DemandeAdministrative> {
    return this.apiService.executeAction<DemandeAdministrative>(this.endpoint, id, 'priorite', { priorite: nouvellePriorite });
  }

  definirEcheance(id: number, dateEcheance: string): Observable<DemandeAdministrative> {
    return this.apiService.executeAction<DemandeAdministrative>(this.endpoint, id, 'echeance', { dateEcheance });
  }

  prolongerEcheance(id: number, nouvelleEcheance: string, justification: string): Observable<DemandeAdministrative> {
    const data = { nouvelleEcheance, justification };
    return this.apiService.executeAction<DemandeAdministrative>(this.endpoint, id, 'prolonger-echeance', data);
  }

  // Workflow et historique
  getWorkflowDemande(id: number): Observable<ValidationWorkflow[]> {
    return this.apiService.get<ValidationWorkflow[]>(`${this.endpoint}/${id}/workflow`);
  }

  getHistoriqueDemande(id: number): Observable<any[]> {
    return this.apiService.get<any[]>(`${this.endpoint}/${id}/historique`);
  }

  // Demandes spécifiques par statut
  getDemandesEnAttente(): Observable<DemandeAdministrative[]> {
    return this.apiService.get<DemandeAdministrative[]>(`${this.endpoint}/en-attente`);
  }

  getDemandesEnTraitement(): Observable<DemandeAdministrative[]> {
    return this.apiService.get<DemandeAdministrative[]>(`${this.endpoint}/en-traitement`);
  }

  getDemandesEchues(): Observable<DemandeAdministrative[]> {
    return this.apiService.get<DemandeAdministrative[]>(`${this.endpoint}/echues`);
  }

  getDemandesUrgentes(): Observable<DemandeAdministrative[]> {
    return this.apiService.get<DemandeAdministrative[]>(`${this.endpoint}/urgentes`);
  }

  getDemandesEnAttenteValidation(validateur: 'coach' | 'medical' | 'financier'): Observable<DemandeAdministrative[]> {
    return this.apiService.get<DemandeAdministrative[]>(`${this.endpoint}/en-attente-validation/${validateur}`);
  }

  // Statistiques et rapports
  getStatistiques(filters?: DemandeAdministrativeFilters): Observable<DemandeAdministrativeStats> {
    return this.apiService.getStats<DemandeAdministrativeStats>(this.endpoint, filters);
  }

  getStatistiquesParDemandeur(demandeurId: number): Observable<DemandeAdministrativeStats> {
    return this.apiService.get<DemandeAdministrativeStats>(`${this.endpoint}/demandeur/${demandeurId}/stats`);
  }

  getStatistiquesParType(type: TypeDemande): Observable<DemandeAdministrativeStats> {
    return this.apiService.get<DemandeAdministrativeStats>(`${this.endpoint}/type/${type}/stats`);
  }

  getRapportCouts(dateDebut: string, dateFin: string): Observable<any> {
    return this.apiService.get<any>(`${this.endpoint}/rapport-couts`, { dateDebut, dateFin });
  }

  getRapportDelais(dateDebut: string, dateFin: string): Observable<any> {
    return this.apiService.get<any>(`${this.endpoint}/rapport-delais`, { dateDebut, dateFin });
  }

  // Export et import
  exporterDemandes(format: 'csv' | 'excel' | 'pdf', filters?: DemandeAdministrativeFilters): Observable<Blob> {
    return this.apiService.export(this.endpoint, format, filters);
  }

  importerDemandes(file: File): Observable<any> {
    return this.apiService.uploadFile(this.endpoint, file);
  }

  // Gestion des documents
  ajouterDocument(id: number, file: File, description?: string): Observable<DemandeAdministrative> {
    const additionalData = description ? { description } : {};
    return this.apiService.uploadFile(`${this.endpoint}/${id}/documents`, file, additionalData);
  }

  supprimerDocument(id: number, documentId: string): Observable<DemandeAdministrative> {
    return this.apiService.delete<DemandeAdministrative>(`${this.endpoint}/${id}/documents/${documentId}`);
  }

  telechargerDocument(id: number, documentId: string): Observable<Blob> {
    return this.apiService.downloadFile(`${this.endpoint}/${id}/documents`, documentId);
  }

  // Validation et vérification
  verifierValidationsNecessaires(demande: DemandeAdministrative): Observable<{ validations: string[] }> {
    return this.apiService.post<{ validations: string[] }>(`${this.endpoint}/verifier-validations`, demande);
  }

  calculerDelaiTraitement(demande: DemandeAdministrative): Observable<{ delaiEstime: number }> {
    return this.apiService.post<{ delaiEstime: number }>(`${this.endpoint}/calculer-delai`, demande);
  }
}
