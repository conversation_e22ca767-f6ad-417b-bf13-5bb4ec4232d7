<div class="budget-form-container">
  <!-- Header -->
  <div class="header-section">
    <div class="title-section">
      <h2>
        <mat-icon>{{ isEditMode ? 'edit' : 'add' }}</mat-icon>
        {{ isEditMode ? 'Modifier le Budget' : 'Nouveau Budget' }}
      </h2>
      <p class="subtitle">{{ isEditMode ? 'Modifiez les informations du budget' : 'Créez un nouveau budget pour votre équipe' }}</p>
    </div>
    
    <div class="actions-section">
      <button mat-stroked-button (click)="goBack()">
        <mat-icon>arrow_back</mat-icon>
        Retour
      </button>
    </div>
  </div>

  <!-- Formulaire -->
  <mat-card class="form-card">
    <mat-card-content>
      <form [formGroup]="budgetForm" (ngSubmit)="onSubmit()" class="budget-form">
        
        <!-- Informations générales -->
        <div class="form-section">
          <h3>
            <mat-icon>info</mat-icon>
            Informations générales
          </h3>
          
          <div class="form-grid">
            <!-- Nom du budget -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Nom du budget *</mat-label>
              <input matInput formControlName="nom" placeholder="Ex: Budget Équipements 2024">
              <mat-error *ngIf="budgetForm.get('nom')?.hasError('required')">
                Le nom est obligatoire
              </mat-error>
              <mat-error *ngIf="budgetForm.get('nom')?.hasError('minlength')">
                Le nom doit contenir au moins 3 caractères
              </mat-error>
            </mat-form-field>

            <!-- Description -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Description</mat-label>
              <textarea matInput formControlName="description" 
                        rows="3" 
                        placeholder="Description détaillée du budget..."></textarea>
            </mat-form-field>
          </div>
        </div>

        <!-- Configuration financière -->
        <div class="form-section">
          <h3>
            <mat-icon>euro</mat-icon>
            Configuration financière
          </h3>
          
          <div class="form-grid">
            <!-- Montant total -->
            <mat-form-field appearance="outline">
              <mat-label>Montant total *</mat-label>
              <input matInput type="number" formControlName="montantTotal" 
                     placeholder="0.00" min="0" step="0.01">
              <span matSuffix>€</span>
              <mat-error *ngIf="budgetForm.get('montantTotal')?.hasError('required')">
                Le montant est obligatoire
              </mat-error>
              <mat-error *ngIf="budgetForm.get('montantTotal')?.hasError('min')">
                Le montant doit être positif
              </mat-error>
            </mat-form-field>

            <!-- Seuil d'alerte -->
            <mat-form-field appearance="outline">
              <mat-label>Seuil d'alerte</mat-label>
              <input matInput type="number" formControlName="seuilAlerte" 
                     placeholder="80" min="0" max="100" step="1">
              <span matSuffix>%</span>
              <mat-hint>Pourcentage d'utilisation déclenchant une alerte</mat-hint>
              <mat-error *ngIf="budgetForm.get('seuilAlerte')?.hasError('min')">
                Le seuil doit être entre 0 et 100
              </mat-error>
              <mat-error *ngIf="budgetForm.get('seuilAlerte')?.hasError('max')">
                Le seuil doit être entre 0 et 100
              </mat-error>
            </mat-form-field>
          </div>
        </div>

        <!-- Période et dates -->
        <div class="form-section">
          <h3>
            <mat-icon>date_range</mat-icon>
            Période et dates
          </h3>
          
          <div class="form-grid">
            <!-- Période -->
            <mat-form-field appearance="outline">
              <mat-label>Période *</mat-label>
              <mat-select formControlName="periode">
                <mat-option value="MENSUEL">Mensuel</mat-option>
                <mat-option value="TRIMESTRIEL">Trimestriel</mat-option>
                <mat-option value="SEMESTRIEL">Semestriel</mat-option>
                <mat-option value="ANNUEL">Annuel</mat-option>
              </mat-select>
              <mat-error *ngIf="budgetForm.get('periode')?.hasError('required')">
                La période est obligatoire
              </mat-error>
            </mat-form-field>

            <!-- Date de début -->
            <mat-form-field appearance="outline">
              <mat-label>Date de début *</mat-label>
              <input matInput [matDatepicker]="pickerDebut" formControlName="dateDebut">
              <mat-datepicker-toggle matSuffix [for]="pickerDebut"></mat-datepicker-toggle>
              <mat-datepicker #pickerDebut></mat-datepicker>
              <mat-error *ngIf="budgetForm.get('dateDebut')?.hasError('required')">
                La date de début est obligatoire
              </mat-error>
            </mat-form-field>

            <!-- Date de fin -->
            <mat-form-field appearance="outline">
              <mat-label>Date de fin *</mat-label>
              <input matInput [matDatepicker]="pickerFin" formControlName="dateFin">
              <mat-datepicker-toggle matSuffix [for]="pickerFin"></mat-datepicker-toggle>
              <mat-datepicker #pickerFin></mat-datepicker>
              <mat-error *ngIf="budgetForm.get('dateFin')?.hasError('required')">
                La date de fin est obligatoire
              </mat-error>
              <mat-error *ngIf="budgetForm.get('dateFin')?.hasError('dateRange')">
                La date de fin doit être après la date de début
              </mat-error>
            </mat-form-field>
          </div>
        </div>

        <!-- Gestion et statut -->
        <div class="form-section">
          <h3>
            <mat-icon>settings</mat-icon>
            Gestion et statut
          </h3>
          
          <div class="form-grid">
            <!-- Responsable -->
            <mat-form-field appearance="outline">
              <mat-label>Responsable</mat-label>
              <mat-select formControlName="responsableId">
                <mat-option *ngFor="let responsable of responsables" [value]="responsable.id">
                  {{ responsable.nom }} {{ responsable.prenom }}
                </mat-option>
              </mat-select>
            </mat-form-field>

            <!-- Statut (seulement en mode édition) -->
            <mat-form-field appearance="outline" *ngIf="isEditMode">
              <mat-label>Statut</mat-label>
              <mat-select formControlName="statut">
                <mat-option value="ACTIF">Actif</mat-option>
                <mat-option value="SUSPENDU">Suspendu</mat-option>
                <mat-option value="CLOTURE">Clôturé</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>

        <!-- Catégories de budget -->
        <div class="form-section">
          <h3>
            <mat-icon>category</mat-icon>
            Catégories de budget
          </h3>
          
          <div class="categories-section">
            <div formArrayName="categories" class="categories-list">
              <div *ngFor="let category of categoriesFormArray.controls; let i = index" 
                   [formGroupName]="i" 
                   class="category-item">
                
                <mat-form-field appearance="outline" class="category-name">
                  <mat-label>Nom de la catégorie</mat-label>
                  <input matInput formControlName="nom" placeholder="Ex: Équipements">
                </mat-form-field>
                
                <mat-form-field appearance="outline" class="category-amount">
                  <mat-label>Montant alloué</mat-label>
                  <input matInput type="number" formControlName="montantAlloue" 
                         placeholder="0.00" min="0" step="0.01">
                  <span matSuffix>€</span>
                </mat-form-field>
                
                <button type="button" mat-icon-button color="warn" 
                        (click)="removeCategory(i)"
                        matTooltip="Supprimer la catégorie">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </div>
            
            <button type="button" mat-stroked-button (click)="addCategory()" class="add-category-btn">
              <mat-icon>add</mat-icon>
              Ajouter une catégorie
            </button>
          </div>
        </div>

        <!-- Résumé financier -->
        <div class="form-section" *ngIf="getTotalCategoriesAmount() > 0">
          <h3>
            <mat-icon>assessment</mat-icon>
            Résumé financier
          </h3>
          
          <div class="financial-summary">
            <div class="summary-item">
              <span class="label">Montant total du budget :</span>
              <span class="value">{{ budgetForm.get('montantTotal')?.value | currencyFormat }}</span>
            </div>
            
            <div class="summary-item">
              <span class="label">Total des catégories :</span>
              <span class="value">{{ getTotalCategoriesAmount() | currencyFormat }}</span>
            </div>
            
            <div class="summary-item" [class.warning]="getRemainingAmount() < 0">
              <span class="label">Montant restant :</span>
              <span class="value">{{ getRemainingAmount() | currencyFormat }}</span>
            </div>
            
            <mat-progress-bar 
              [value]="getCategoriesPercentage()" 
              [color]="getCategoriesPercentage() > 100 ? 'warn' : 'primary'"
              class="allocation-progress">
            </mat-progress-bar>
            
            <div class="progress-label">
              {{ getCategoriesPercentage() | number:'1.1-1' }}% du budget alloué
            </div>
          </div>
        </div>

        <!-- Actions du formulaire -->
        <div class="form-actions">
          <button type="button" mat-stroked-button (click)="goBack()">
            <mat-icon>cancel</mat-icon>
            Annuler
          </button>
          
          <button type="submit" 
                  mat-raised-button 
                  color="primary" 
                  [disabled]="budgetForm.invalid || loading">
            <mat-icon *ngIf="!loading">{{ isEditMode ? 'save' : 'add' }}</mat-icon>
            <mat-spinner *ngIf="loading" diameter="20"></mat-spinner>
            {{ isEditMode ? 'Modifier' : 'Créer' }} le budget
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div>
