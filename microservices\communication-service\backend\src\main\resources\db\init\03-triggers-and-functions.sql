-- Script de création des triggers et fonctions pour le service de communication
-- Auteur: SprintBot Team
-- Version: 1.0.0

-- Utiliser le schéma communication
SET search_path TO communication, public;

-- Fonction pour mettre à jour automatiquement date_modification
CREATE OR REPLACE FUNCTION communication.update_date_modification()
RETURNS TRIGGER AS $$
BEGIN
    NEW.date_modification = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour conversation
CREATE TRIGGER trigger_conversation_update_date_modification
    BEFORE UPDATE ON conversation
    FOR EACH ROW
    EXECUTE FUNCTION communication.update_date_modification();

-- Trigger pour participant_conversation
CREATE TRIGGER trigger_participant_conversation_update_date_modification
    BEFORE UPDATE ON participant_conversation
    FOR EACH ROW
    EXECUTE FUNCTION communication.update_date_modification();

-- Trigger pour message
CREATE TRIGGER trigger_message_update_date_modification
    BEFORE UPDATE ON message
    FOR EACH ROW
    EXECUTE FUNCTION communication.update_date_modification();

-- Trigger pour notification_preference
CREATE TRIGGER trigger_notification_preference_update_date_modification
    BEFORE UPDATE ON notification_preference
    FOR EACH ROW
    EXECUTE FUNCTION communication.update_date_modification();

-- Fonction pour mettre à jour la date de modification de la conversation lors d'un nouveau message
CREATE OR REPLACE FUNCTION communication.update_conversation_on_message()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE conversation 
    SET date_modification = CURRENT_TIMESTAMP 
    WHERE id = NEW.conversation_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour mettre à jour la conversation lors d'un nouveau message
CREATE TRIGGER trigger_update_conversation_on_new_message
    AFTER INSERT ON message
    FOR EACH ROW
    EXECUTE FUNCTION communication.update_conversation_on_message();

-- Fonction pour valider les données de notification
CREATE OR REPLACE FUNCTION communication.validate_notification()
RETURNS TRIGGER AS $$
BEGIN
    -- Valider l'email si le canal est EMAIL
    IF NEW.canal = 'EMAIL' AND NEW.donnees IS NOT NULL THEN
        IF NOT communication.is_valid_email(NEW.donnees->>'email') THEN
            RAISE EXCEPTION 'Email invalide: %', NEW.donnees->>'email';
        END IF;
    END IF;
    
    -- Valider le téléphone si le canal est SMS
    IF NEW.canal = 'SMS' AND NEW.donnees IS NOT NULL THEN
        IF NOT communication.is_valid_phone(NEW.donnees->>'phone') THEN
            RAISE EXCEPTION 'Numéro de téléphone invalide: %', NEW.donnees->>'phone';
        END IF;
    END IF;
    
    -- Valider la date programmée
    IF NEW.date_programmee IS NOT NULL AND NEW.date_programmee <= CURRENT_TIMESTAMP THEN
        RAISE EXCEPTION 'La date programmée doit être dans le futur';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour valider les notifications
CREATE TRIGGER trigger_validate_notification
    BEFORE INSERT OR UPDATE ON notification
    FOR EACH ROW
    EXECUTE FUNCTION communication.validate_notification();

-- Fonction pour nettoyer automatiquement les messages supprimés
CREATE OR REPLACE FUNCTION communication.auto_cleanup_deleted_messages()
RETURNS TRIGGER AS $$
BEGIN
    -- Si le message est marqué comme supprimé, programmer sa suppression définitive
    IF NEW.est_supprime = true AND OLD.est_supprime = false THEN
        -- Ici on pourrait ajouter une logique pour programmer la suppression
        -- Pour l'instant, on log juste l'action
        INSERT INTO communication.notification (
            destinataire_id,
            type,
            canal,
            titre,
            contenu,
            priorite
        ) VALUES (
            NEW.expediteur_id,
            'SYSTEME',
            'INTERNE',
            'Message supprimé',
            'Votre message a été marqué pour suppression et sera définitivement supprimé dans 30 jours.',
            'BASSE'
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour le nettoyage automatique des messages
CREATE TRIGGER trigger_auto_cleanup_deleted_messages
    AFTER UPDATE ON message
    FOR EACH ROW
    EXECUTE FUNCTION communication.auto_cleanup_deleted_messages();

-- Fonction pour mettre à jour automatiquement la présence utilisateur
CREATE OR REPLACE FUNCTION communication.update_user_presence()
RETURNS TRIGGER AS $$
BEGIN
    NEW.derniere_activite = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour mettre à jour la présence
CREATE TRIGGER trigger_update_user_presence
    BEFORE UPDATE ON user_presence
    FOR EACH ROW
    EXECUTE FUNCTION communication.update_user_presence();

-- Fonction pour créer automatiquement les préférences de notification pour un nouvel utilisateur
CREATE OR REPLACE FUNCTION communication.create_default_notification_preferences(p_utilisateur_id BIGINT)
RETURNS VOID AS $$
DECLARE
    notification_types TEXT[] := ARRAY['NOUVEAU_MESSAGE', 'NOUVELLE_CONVERSATION', 'AJOUT_CONVERSATION', 'ESCALADE_CHATBOT'];
    canaux TEXT[] := ARRAY['PUSH', 'EMAIL', 'INTERNE'];
    notification_type TEXT;
    canal TEXT;
BEGIN
    FOREACH notification_type IN ARRAY notification_types
    LOOP
        FOREACH canal IN ARRAY canaux
        LOOP
            INSERT INTO communication.notification_preference (
                utilisateur_id,
                type_notification,
                canal,
                active
            ) VALUES (
                p_utilisateur_id,
                notification_type,
                canal,
                CASE 
                    WHEN canal = 'SMS' THEN FALSE  -- SMS désactivé par défaut
                    ELSE TRUE
                END
            ) ON CONFLICT (utilisateur_id, type_notification, canal) DO NOTHING;
        END LOOP;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour obtenir les statistiques d'une conversation
CREATE OR REPLACE FUNCTION communication.get_conversation_stats(p_conversation_id BIGINT)
RETURNS TABLE (
    total_messages BIGINT,
    total_participants BIGINT,
    messages_aujourd_hui BIGINT,
    dernier_message_date TIMESTAMP,
    utilisateur_plus_actif BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*) FROM communication.message WHERE conversation_id = p_conversation_id AND est_supprime = false),
        (SELECT COUNT(*) FROM communication.participant_conversation WHERE conversation_id = p_conversation_id),
        (SELECT COUNT(*) FROM communication.message WHERE conversation_id = p_conversation_id AND date_creation >= CURRENT_DATE),
        (SELECT MAX(date_creation) FROM communication.message WHERE conversation_id = p_conversation_id),
        (SELECT expediteur_id FROM communication.message WHERE conversation_id = p_conversation_id GROUP BY expediteur_id ORDER BY COUNT(*) DESC LIMIT 1);
END;
$$ LANGUAGE plpgsql;

-- Fonction pour obtenir les messages non lus d'un utilisateur
CREATE OR REPLACE FUNCTION communication.get_unread_messages_count(p_utilisateur_id BIGINT)
RETURNS BIGINT AS $$
DECLARE
    unread_count BIGINT;
BEGIN
    SELECT COUNT(DISTINCT m.id) INTO unread_count
    FROM communication.message m
    INNER JOIN communication.participant_conversation pc ON m.conversation_id = pc.conversation_id
    LEFT JOIN communication.lecture_message lm ON m.id = lm.message_id AND lm.utilisateur_id = p_utilisateur_id
    WHERE pc.utilisateur_id = p_utilisateur_id
    AND m.expediteur_id != p_utilisateur_id
    AND m.est_supprime = false
    AND lm.id IS NULL
    AND (pc.date_derniere_lecture IS NULL OR m.date_creation > pc.date_derniere_lecture);
    
    RETURN COALESCE(unread_count, 0);
END;
$$ LANGUAGE plpgsql;

-- Fonction pour rechercher dans les messages
CREATE OR REPLACE FUNCTION communication.search_messages(
    p_utilisateur_id BIGINT,
    p_query TEXT,
    p_conversation_id BIGINT DEFAULT NULL,
    p_limit INTEGER DEFAULT 50,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    message_id BIGINT,
    conversation_id BIGINT,
    conversation_nom VARCHAR(255),
    expediteur_id BIGINT,
    contenu TEXT,
    date_creation TIMESTAMP,
    rank REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.id,
        m.conversation_id,
        c.nom,
        m.expediteur_id,
        m.contenu,
        m.date_creation,
        ts_rank(to_tsvector('french_unaccent', m.contenu), plainto_tsquery('french_unaccent', p_query)) as rank
    FROM communication.message m
    INNER JOIN communication.conversation c ON m.conversation_id = c.id
    INNER JOIN communication.participant_conversation pc ON c.id = pc.conversation_id
    WHERE pc.utilisateur_id = p_utilisateur_id
    AND m.est_supprime = false
    AND to_tsvector('french_unaccent', m.contenu) @@ plainto_tsquery('french_unaccent', p_query)
    AND (p_conversation_id IS NULL OR m.conversation_id = p_conversation_id)
    ORDER BY rank DESC, m.date_creation DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- Affichage du statut
SELECT 'Triggers et fonctions créés avec succès' AS status;
SELECT 'Fonctions utilitaires disponibles pour les statistiques et recherches' AS functions_status;
