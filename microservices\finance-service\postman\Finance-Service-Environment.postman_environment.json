{"id": "finance-service-env", "name": "Finance Service Environment", "values": [{"key": "baseUrl", "value": "http://localhost:8085", "description": "URL de base du service Finance", "enabled": true}, {"key": "authToken", "value": "", "description": "Token JWT pour l'authentification", "enabled": true}, {"key": "budgetId", "value": "", "description": "ID du budget pour les tests", "enabled": true}, {"key": "transactionId", "value": "", "description": "ID de la transaction pour les tests", "enabled": true}, {"key": "sponsorId", "value": "", "description": "ID du sponsor pour les tests", "enabled": true}, {"key": "salaire<PERSON>d", "value": "", "description": "ID du salaire pour les tests", "enabled": true}, {"key": "categorieId", "value": "", "description": "ID de la catégorie pour les tests", "enabled": true}, {"key": "testUserId", "value": "1", "description": "ID utilisateur pour les tests", "enabled": true}, {"key": "testEmployeId", "value": "101", "description": "ID employé pour les tests de salaire", "enabled": true}, {"key": "currentYear", "value": "2024", "description": "Année courante pour les tests", "enabled": true}, {"key": "testDateDebut", "value": "2024-01-01", "description": "Date de début pour les tests", "enabled": true}, {"key": "testDateFin", "value": "2024-12-31", "description": "Date de fin pour les tests", "enabled": true}], "_postman_variable_scope": "environment"}