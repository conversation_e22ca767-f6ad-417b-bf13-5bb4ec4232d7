import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { 
  DonneesSante, 
  DonneesSanteFilters, 
  DonneesSanteStats,
  TypeExamen,
  StatutDonneesSante,
  GraviteDonneesSante
} from '@core/models/donnees-sante.model';
import { PagedResponse, FilterOptions } from '@core/models/common.model';

@Injectable({
  providedIn: 'root'
})
export class DonneesSanteService {
  private readonly endpoint = '/api/donnees-sante';

  constructor(private apiService: ApiService) {}

  // CRUD Operations
  getAllDonneesSante(options?: FilterOptions): Observable<PagedResponse<DonneesSante>> {
    return this.apiService.getPaged<DonneesSante>(this.endpoint, options);
  }

  getDonneesSanteById(id: number): Observable<DonneesSante> {
    return this.apiService.getById<DonneesSante>(this.endpoint, id);
  }

  creerDonneesSante(donneesSante: DonneesSante): Observable<DonneesSante> {
    return this.apiService.post<DonneesSante>(this.endpoint, donneesSante);
  }

  modifierDonneesSante(id: number, donneesSante: DonneesSante): Observable<DonneesSante> {
    return this.apiService.put<DonneesSante>(this.endpoint, id, donneesSante);
  }

  supprimerDonneesSante(id: number): Observable<void> {
    return this.apiService.delete<void>(this.endpoint, id);
  }

  // Recherche et filtrage
  rechercherDonneesSante(filters: DonneesSanteFilters, options?: FilterOptions): Observable<PagedResponse<DonneesSante>> {
    const params = { ...filters, ...options };
    return this.apiService.get<PagedResponse<DonneesSante>>(`${this.endpoint}/recherche`, params);
  }

  getDonneesSanteParJoueur(joueurId: number, options?: FilterOptions): Observable<PagedResponse<DonneesSante>> {
    return this.apiService.getPaged<DonneesSante>(`${this.endpoint}/joueur/${joueurId}`, options);
  }

  getDonneesSanteParStaffMedical(staffId: number, options?: FilterOptions): Observable<PagedResponse<DonneesSante>> {
    return this.apiService.getPaged<DonneesSante>(`${this.endpoint}/staff-medical/${staffId}`, options);
  }

  getDonneesSanteParType(type: TypeExamen, options?: FilterOptions): Observable<PagedResponse<DonneesSante>> {
    return this.apiService.getPaged<DonneesSante>(`${this.endpoint}/type/${type}`, options);
  }

  getDonneesSanteParStatut(statut: StatutDonneesSante, options?: FilterOptions): Observable<PagedResponse<DonneesSante>> {
    return this.apiService.getPaged<DonneesSante>(`${this.endpoint}/statut/${statut}`, options);
  }

  getDonneesSanteParGravite(gravite: GraviteDonneesSante, options?: FilterOptions): Observable<PagedResponse<DonneesSante>> {
    return this.apiService.getPaged<DonneesSante>(`${this.endpoint}/gravite/${gravite}`, options);
  }

  getDonneesSanteParPeriode(dateDebut: string, dateFin: string, options?: FilterOptions): Observable<PagedResponse<DonneesSante>> {
    const params = { dateDebut, dateFin, ...options };
    return this.apiService.get<PagedResponse<DonneesSante>>(`${this.endpoint}/periode`, params);
  }

  // Actions spécifiques
  changerStatut(id: number, nouveauStatut: StatutDonneesSante): Observable<DonneesSante> {
    return this.apiService.executeAction<DonneesSante>(this.endpoint, id, 'statut', { statut: nouveauStatut });
  }

  marquerGueri(id: number, dateGuerison?: string): Observable<DonneesSante> {
    const data = dateGuerison ? { dateGuerison } : {};
    return this.apiService.executeAction<DonneesSante>(this.endpoint, id, 'guerir', data);
  }

  planifierSuivi(id: number, dateSuivi: string, notes?: string): Observable<DonneesSante> {
    const data = { dateSuivi, notes };
    return this.apiService.executeAction<DonneesSante>(this.endpoint, id, 'planifier-suivi', data);
  }

  ajouterTraitement(id: number, traitement: string): Observable<DonneesSante> {
    return this.apiService.executeAction<DonneesSante>(this.endpoint, id, 'ajouter-traitement', { traitement });
  }

  ajouterMedicament(id: number, medicament: string): Observable<DonneesSante> {
    return this.apiService.executeAction<DonneesSante>(this.endpoint, id, 'ajouter-medicament', { medicament });
  }

  // Statistiques et rapports
  getStatistiques(filters?: DonneesSanteFilters): Observable<DonneesSanteStats> {
    return this.apiService.getStats<DonneesSanteStats>(this.endpoint, filters);
  }

  getStatistiquesParJoueur(joueurId: number): Observable<DonneesSanteStats> {
    return this.apiService.get<DonneesSanteStats>(`${this.endpoint}/joueur/${joueurId}/stats`);
  }

  getStatistiquesParStaffMedical(staffId: number): Observable<DonneesSanteStats> {
    return this.apiService.get<DonneesSanteStats>(`${this.endpoint}/staff-medical/${staffId}/stats`);
  }

  getAlertesSante(): Observable<DonneesSante[]> {
    return this.apiService.get<DonneesSante[]>(`${this.endpoint}/alertes`);
  }

  getSuivisEnRetard(): Observable<DonneesSante[]> {
    return this.apiService.get<DonneesSante[]>(`${this.endpoint}/suivis-retard`);
  }

  getBlessuresActives(): Observable<DonneesSante[]> {
    return this.apiService.get<DonneesSante[]>(`${this.endpoint}/blessures-actives`);
  }

  // Export et import
  exporterDonneesSante(format: 'csv' | 'excel' | 'pdf', filters?: DonneesSanteFilters): Observable<Blob> {
    return this.apiService.export(this.endpoint, format, filters);
  }

  importerDonneesSante(file: File): Observable<any> {
    return this.apiService.uploadFile(this.endpoint, file);
  }

  // Validation et vérification
  verifierConflits(donneesSante: DonneesSante): Observable<boolean> {
    return this.apiService.post<boolean>(`${this.endpoint}/verifier-conflits`, donneesSante);
  }

  validerDonnees(donneesSante: DonneesSante): Observable<{ valide: boolean; erreurs: string[] }> {
    return this.apiService.post<{ valide: boolean; erreurs: string[] }>(`${this.endpoint}/valider`, donneesSante);
  }
}
