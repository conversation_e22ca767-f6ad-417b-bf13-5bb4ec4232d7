# Déploiement - Medical Admin Service

## 🚀 Guide de déploiement

### Prérequis
- Docker 20.10+
- Docker Compose 2.0+
- 4 GB RAM minimum
- 10 GB espace disque libre

### Architecture de déploiement

```
Medical Admin Service
├── medical-admin-db (PostgreSQL:5435)
├── medical-admin-backend (Spring Boot:8083)
└── medical-admin-frontend (Angular/Nginx:4203)
```

## 📦 Déploiement local

### 1. Déploiement standalone
```bash
# Aller dans le répertoire du service
cd microservices/medical-admin-service

# Démarrer tous les services
docker-compose up -d

# Vérifier l'état
docker-compose ps

# Voir les logs
docker-compose logs -f
```

### 2. Déploiement avec script automatisé
```bash
# Déploiement simple
./deploy.sh

# Déploiement avec reconstruction
./deploy.sh dev --rebuild

# Déploiement production
./deploy.sh prod
```

### 3. Déploiement intégré (recommandé)
```bash
# Depuis la racine du projet SprintBot
docker-compose up -d medical-admin-db medical-admin-backend medical-admin-frontend

# Ou démarrer tout l'écosystème
docker-compose up -d
```

## 🔧 Configuration

### Variables d'environnement

**Base de données**
```env
POSTGRES_DB=medical_admin_db
POSTGRES_USER=medical_admin_user
POSTGRES_PASSWORD=medical_admin_password
```

**Backend**
```env
SPRING_PROFILES_ACTIVE=docker
SPRING_DATASOURCE_URL=************************************************************************************
JAVA_OPTS=-Xmx512m -Xms256m -XX:+UseG1GC
```

**Frontend**
```env
API_URL=http://medical-admin-backend:8083
```

### Ports utilisés
- **5435** : PostgreSQL (medical-admin-db)
- **8083** : Backend Spring Boot
- **4203** : Frontend Angular/Nginx

## 🏥 Services déployés

### 1. Base de données (medical-admin-db)
- **Image** : postgres:15-alpine
- **Port** : 5435
- **Schéma** : medical_admin
- **Données** : Volume persistant `medical_admin_db_data`

### 2. Backend (medical-admin-backend)
- **Port** : 8083
- **Health Check** : http://localhost:8083/actuator/health
- **API Documentation** : http://localhost:8083/swagger-ui.html
- **Profil** : docker

### 3. Frontend (medical-admin-frontend)
- **Port** : 4203
- **URL** : http://localhost:4203
- **Health Check** : http://localhost:4203/health
- **Serveur** : Nginx optimisé

## 📊 Monitoring et santé

### Health Checks configurés

**Base de données**
```bash
pg_isready -U medical_admin_user -d medical_admin_db
```

**Backend**
```bash
wget --spider http://localhost:8083/actuator/health
```

**Frontend**
```bash
wget --spider http://localhost:8080/health
```

### Endpoints de monitoring

| Service | Endpoint | Description |
|---------|----------|-------------|
| Backend | `/actuator/health` | État général |
| Backend | `/actuator/info` | Informations système |
| Backend | `/actuator/metrics` | Métriques |
| Frontend | `/health` | État Nginx |

## 🔍 Vérification du déploiement

### 1. Vérification automatique
```bash
# Script de vérification
./deploy.sh --check

# Ou manuellement
docker-compose ps
docker-compose logs --tail=50
```

### 2. Tests de connectivité
```bash
# Test base de données
docker-compose exec medical-admin-db psql -U medical_admin_user -d medical_admin_db -c "SELECT 1;"

# Test backend
curl http://localhost:8083/actuator/health

# Test frontend
curl http://localhost:4203/health
```

### 3. Tests fonctionnels
```bash
# Test API avec fichier HTTP
# Utiliser integration-test.http avec REST Client

# Test interface utilisateur
# Ouvrir http://localhost:4203 dans le navigateur
```

## 🐛 Dépannage

### Problèmes courants

**1. Base de données non accessible**
```bash
# Vérifier les logs
docker-compose logs medical-admin-db

# Redémarrer le service
docker-compose restart medical-admin-db
```

**2. Backend ne démarre pas**
```bash
# Vérifier la connectivité DB
docker-compose exec medical-admin-backend ping medical-admin-db

# Vérifier les variables d'environnement
docker-compose exec medical-admin-backend env | grep SPRING
```

**3. Frontend non accessible**
```bash
# Vérifier la configuration Nginx
docker-compose exec medical-admin-frontend nginx -t

# Redémarrer Nginx
docker-compose restart medical-admin-frontend
```

### Logs utiles
```bash
# Tous les services
docker-compose logs -f

# Service spécifique
docker-compose logs -f medical-admin-backend

# Dernières erreurs
docker-compose logs --tail=100 | grep ERROR
```

## 🔄 Mise à jour

### 1. Mise à jour du code
```bash
# Arrêter les services
docker-compose down

# Reconstruire les images
docker-compose build --no-cache

# Redémarrer
docker-compose up -d
```

### 2. Mise à jour de la base de données
```bash
# Les migrations Flyway s'exécutent automatiquement
# Vérifier dans les logs du backend
docker-compose logs medical-admin-backend | grep Flyway
```

### 3. Sauvegarde avant mise à jour
```bash
# Sauvegarder la base de données
docker-compose exec medical-admin-db pg_dump -U medical_admin_user medical_admin_db > backup.sql

# Sauvegarder les volumes
docker run --rm -v medical_admin_db_data:/data -v $(pwd):/backup alpine tar czf /backup/db-backup.tar.gz /data
```

## 🔐 Sécurité

### Configuration de production

**1. Variables d'environnement sécurisées**
```bash
# Utiliser des secrets Docker ou des fichiers .env
echo "POSTGRES_PASSWORD=secure_password" > .env
```

**2. Réseau isolé**
```yaml
# Utiliser des réseaux internes
networks:
  medical-admin-internal:
    internal: true
```

**3. Utilisateurs non-root**
```dockerfile
# Les Dockerfiles utilisent déjà des utilisateurs non-root
USER appuser
```

## 📈 Performance

### Optimisations appliquées

**Backend**
- JVM optimisée avec G1GC
- Pool de connexions configuré
- Cache Redis (optionnel)

**Frontend**
- Compression gzip activée
- Cache des assets statiques
- Build de production optimisé

**Base de données**
- Index sur les colonnes fréquemment utilisées
- Configuration PostgreSQL optimisée

### Monitoring des performances
```bash
# Métriques JVM
curl http://localhost:8083/actuator/metrics/jvm.memory.used

# Métriques base de données
docker-compose exec medical-admin-db psql -U medical_admin_user -d medical_admin_db -c "SELECT * FROM pg_stat_activity;"
```

## 🌐 Déploiement en production

### 1. Configuration production
```yaml
# docker-compose.prod.yml
environment:
  SPRING_PROFILES_ACTIVE: prod
  SPRING_DATASOURCE_URL: ***********************************************
```

### 2. Reverse proxy
```nginx
# Configuration Nginx pour production
upstream medical-admin-backend {
    server medical-admin-backend:8083;
}

server {
    listen 443 ssl;
    server_name medical-admin.sprintbot.com;
    
    location /api/ {
        proxy_pass http://medical-admin-backend;
    }
    
    location / {
        proxy_pass http://medical-admin-frontend:8080;
    }
}
```

### 3. SSL/TLS
```bash
# Certificats Let's Encrypt
certbot --nginx -d medical-admin.sprintbot.com
```

## 📞 Support

### Contacts
- **Équipe DevOps** : <EMAIL>
- **Équipe Backend** : <EMAIL>
- **Équipe Frontend** : <EMAIL>

### Documentation
- [API Documentation](http://localhost:8083/swagger-ui.html)
- [Frontend README](./frontend/README.md)
- [Backend README](./backend/README.md)
- [Architecture SprintBot](../../ARCHITECTURE.md)
