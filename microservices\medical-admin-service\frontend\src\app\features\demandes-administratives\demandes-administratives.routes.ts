import { Routes } from '@angular/router';

export const DEMANDES_ADMINISTRATIVES_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./demandes-administratives-list/demandes-administratives-list.component').then(m => m.DemandesAdministrativesListComponent)
  },
  {
    path: 'nouveau',
    loadComponent: () => import('./demandes-administratives-form/demandes-administratives-form.component').then(m => m.DemandesAdministrativesFormComponent)
  },
  {
    path: 'edit/:id',
    loadComponent: () => import('./demandes-administratives-form/demandes-administratives-form.component').then(m => m.DemandesAdministrativesFormComponent)
  },
  {
    path: 'detail/:id',
    loadComponent: () => import('./demandes-administratives-detail/demandes-administratives-detail.component').then(m => m.DemandesAdministrativesDetailComponent)
  },
  {
    path: 'workflow/:id',
    loadComponent: () => import('./demandes-administratives-workflow/demandes-administratives-workflow.component').then(m => m.DemandesAdministrativesWorkflowComponent)
  },
  {
    path: 'en-attente',
    loadComponent: () => import('./demandes-administratives-list/demandes-administratives-list.component').then(m => m.DemandesAdministrativesListComponent),
    data: { filter: 'en-attente' }
  },
  {
    path: 'urgentes',
    loadComponent: () => import('./demandes-administratives-list/demandes-administratives-list.component').then(m => m.DemandesAdministrativesListComponent),
    data: { filter: 'urgentes' }
  },
  {
    path: 'demandeur/:demandeurId',
    loadComponent: () => import('./demandes-administratives-list/demandes-administratives-list.component').then(m => m.DemandesAdministrativesListComponent),
    data: { filter: 'demandeur' }
  }
];
