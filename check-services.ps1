Write-Host "=== VERIFICATION DES SERVICES ===" -ForegroundColor Green
Write-Host "Club Olympique de Kelibia" -ForegroundColor Cyan
Write-Host ""

$services = @(
    @{ Name = "Discovery Service"; Url = "http://localhost:8761"; Port = "8761" },
    @{ Name = "Gateway Service"; Url = "http://localhost:8080/actuator/health"; Port = "8080" },
    @{ Name = "Auth User Service"; Url = "http://localhost:8081/actuator/health"; Port = "8081" },
    @{ Name = "Planning Performance Service"; Url = "http://localhost:8082/actuator/health"; Port = "8082" },
    @{ Name = "Frontend Angular"; Url = "http://localhost:4201"; Port = "4201" }
)

Write-Host "VERIFICATION DES PORTS :" -ForegroundColor Yellow
Write-Host "========================" -ForegroundColor Yellow

foreach ($service in $services) {
    Write-Host "Port $($service.Port) - $($service.Name)..." -ForegroundColor Cyan
    
    try {
        $connection = Test-NetConnection -ComputerName localhost -Port $service.Port -WarningAction SilentlyContinue
        if ($connection.TcpTestSucceeded) {
            Write-Host "OK - Port $($service.Port) ouvert" -ForegroundColor Green
        } else {
            Write-Host "ERREUR - Port $($service.Port) ferme" -ForegroundColor Red
        }
    } catch {
        Write-Host "ERREUR - Impossible de tester le port $($service.Port)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "VERIFICATION DES SERVICES :" -ForegroundColor Yellow
Write-Host "============================" -ForegroundColor Yellow

$servicesUp = 0
foreach ($service in $services) {
    Write-Host "Test de $($service.Name)..." -ForegroundColor Cyan
    
    try {
        $response = Invoke-WebRequest -Uri $service.Url -UseBasicParsing -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Host "OK - $($service.Name) operationnel" -ForegroundColor Green
            $servicesUp++
        } else {
            Write-Host "ATTENTION - $($service.Name) repond avec le code $($response.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "ERREUR - $($service.Name) non accessible" -ForegroundColor Red
        Write-Host "  Erreur: $($_.Exception.Message)" -ForegroundColor Gray
    }
    
    Start-Sleep -Seconds 2
}

Write-Host ""
Write-Host "RESULTAT FINAL :" -ForegroundColor Yellow
Write-Host "================" -ForegroundColor Yellow
Write-Host "Services operationnels : $servicesUp/5" -ForegroundColor White

if ($servicesUp -eq 5) {
    Write-Host "EXCELLENT ! Tous les services sont operationnels !" -ForegroundColor Green
} elseif ($servicesUp -ge 3) {
    Write-Host "BON ! La plupart des services fonctionnent." -ForegroundColor Yellow
} else {
    Write-Host "ATTENTION ! Peu de services sont operationnels." -ForegroundColor Red
}

Write-Host ""
Write-Host "URLS A TESTER :" -ForegroundColor Cyan
Write-Host "===============" -ForegroundColor Cyan
Write-Host "Frontend Principal : http://localhost:4201" -ForegroundColor White
Write-Host "Discovery Console  : http://localhost:8761" -ForegroundColor White
Write-Host "Gateway Health     : http://localhost:8080/actuator/health" -ForegroundColor White
Write-Host "Auth Health        : http://localhost:8081/actuator/health" -ForegroundColor White
Write-Host "Planning Health    : http://localhost:8082/actuator/health" -ForegroundColor White

Write-Host ""
Write-Host "PROCESSUS EN COURS :" -ForegroundColor Cyan
Write-Host "===================" -ForegroundColor Cyan

try {
    $javaProcesses = Get-Process -Name "java" -ErrorAction SilentlyContinue
    if ($javaProcesses) {
        Write-Host "Processus Java detectes : $($javaProcesses.Count)" -ForegroundColor Green
        foreach ($process in $javaProcesses) {
            Write-Host "  PID: $($process.Id) - Memoire: $([math]::Round($process.WorkingSet64/1MB, 2)) MB" -ForegroundColor Gray
        }
    } else {
        Write-Host "Aucun processus Java detecte" -ForegroundColor Red
    }
} catch {
    Write-Host "Impossible de verifier les processus Java" -ForegroundColor Yellow
}

try {
    $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
    if ($nodeProcesses) {
        Write-Host "Processus Node.js detectes : $($nodeProcesses.Count)" -ForegroundColor Green
        foreach ($process in $nodeProcesses) {
            Write-Host "  PID: $($process.Id) - Memoire: $([math]::Round($process.WorkingSet64/1MB, 2)) MB" -ForegroundColor Gray
        }
    } else {
        Write-Host "Aucun processus Node.js detecte" -ForegroundColor Red
    }
} catch {
    Write-Host "Impossible de verifier les processus Node.js" -ForegroundColor Yellow
}

Write-Host ""
if ($servicesUp -ge 3) {
    Write-Host "RECOMMANDATION :" -ForegroundColor Green
    Write-Host "Ouvrez votre navigateur et allez sur http://localhost:4201" -ForegroundColor White
    Write-Host "pour acceder au frontend du Club Olympique de Kelibia !" -ForegroundColor Cyan
} else {
    Write-Host "RECOMMANDATION :" -ForegroundColor Yellow
    Write-Host "Attendez quelques minutes que les services se lancent completement," -ForegroundColor White
    Write-Host "puis relancez cette verification." -ForegroundColor White
}

Write-Host ""
Write-Host "Club Olympique de Kelibia - Verification terminee !" -ForegroundColor Yellow

Read-Host "Appuyez sur Entree pour continuer"
