#!/bin/bash

# Script pour exécuter les tests Postman avec Newman
# Finance Service - SprintBot

set -e

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
COLLECTION_FILE="$SCRIPT_DIR/postman/Finance-Service-Tests.postman_collection.json"
ENVIRONMENT_FILE="$SCRIPT_DIR/postman/Finance-Service-Environment.postman_environment.json"
REPORTS_DIR="$SCRIPT_DIR/test-reports"
SERVICE_URL="http://localhost:8085"
MAX_WAIT_TIME=120

# Fonctions utilitaires
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier les prérequis
check_prerequisites() {
    log_info "Vérification des prérequis..."
    
    # Vérifier Newman
    if ! command -v newman &> /dev/null; then
        log_error "Newman n'est pas installé. Installation..."
        npm install -g newman
        npm install -g newman-reporter-html
        npm install -g newman-reporter-htmlextra
    else
        log_success "Newman est installé"
    fi
    
    # Vérifier les fichiers de collection
    if [ ! -f "$COLLECTION_FILE" ]; then
        log_error "Fichier de collection Postman non trouvé: $COLLECTION_FILE"
        exit 1
    fi
    
    if [ ! -f "$ENVIRONMENT_FILE" ]; then
        log_error "Fichier d'environnement Postman non trouvé: $ENVIRONMENT_FILE"
        exit 1
    fi
    
    log_success "Prérequis vérifiés"
}

# Attendre que le service soit disponible
wait_for_service() {
    log_info "Attente de la disponibilité du service Finance..."
    
    local count=0
    while [ $count -lt $MAX_WAIT_TIME ]; do
        if curl -s -f "$SERVICE_URL/actuator/health" > /dev/null 2>&1; then
            log_success "Service Finance disponible"
            return 0
        fi
        
        echo -n "."
        sleep 1
        count=$((count + 1))
    done
    
    log_error "Service Finance non disponible après ${MAX_WAIT_TIME}s"
    return 1
}

# Créer le répertoire de rapports
setup_reports_directory() {
    log_info "Configuration du répertoire de rapports..."
    
    if [ -d "$REPORTS_DIR" ]; then
        rm -rf "$REPORTS_DIR"
    fi
    
    mkdir -p "$REPORTS_DIR"
    log_success "Répertoire de rapports créé: $REPORTS_DIR"
}

# Exécuter les tests Newman
run_newman_tests() {
    log_info "Exécution des tests Newman..."
    
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local html_report="$REPORTS_DIR/finance-service-tests-$timestamp.html"
    local json_report="$REPORTS_DIR/finance-service-tests-$timestamp.json"
    local junit_report="$REPORTS_DIR/finance-service-tests-$timestamp.xml"
    
    # Commande Newman avec rapports multiples
    newman run "$COLLECTION_FILE" \
        --environment "$ENVIRONMENT_FILE" \
        --reporters cli,html,json,junit \
        --reporter-html-export "$html_report" \
        --reporter-json-export "$json_report" \
        --reporter-junit-export "$junit_report" \
        --timeout-request 30000 \
        --timeout-script 10000 \
        --delay-request 500 \
        --bail \
        --color on \
        --verbose
    
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        log_success "Tests Newman exécutés avec succès"
        log_info "Rapports générés:"
        log_info "  - HTML: $html_report"
        log_info "  - JSON: $json_report"
        log_info "  - JUnit: $junit_report"
    else
        log_error "Échec des tests Newman (code: $exit_code)"
        return $exit_code
    fi
}

# Exécuter les tests avec rapport détaillé
run_detailed_tests() {
    log_info "Exécution des tests avec rapport détaillé..."
    
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local detailed_report="$REPORTS_DIR/finance-service-detailed-$timestamp.html"
    
    # Utiliser newman-reporter-htmlextra pour un rapport plus détaillé
    newman run "$COLLECTION_FILE" \
        --environment "$ENVIRONMENT_FILE" \
        --reporters htmlextra \
        --reporter-htmlextra-export "$detailed_report" \
        --reporter-htmlextra-darkTheme \
        --reporter-htmlextra-title "Finance Service - Tests d'intégration" \
        --reporter-htmlextra-titleSize 4 \
        --reporter-htmlextra-logs \
        --reporter-htmlextra-testPaging \
        --reporter-htmlextra-browserTitle "Finance Service Tests" \
        --timeout-request 30000 \
        --delay-request 500
    
    if [ $? -eq 0 ]; then
        log_success "Rapport détaillé généré: $detailed_report"
    else
        log_warning "Échec de la génération du rapport détaillé"
    fi
}

# Analyser les résultats
analyze_results() {
    log_info "Analyse des résultats..."
    
    local latest_json=$(ls -t "$REPORTS_DIR"/*.json 2>/dev/null | head -n1)
    
    if [ -f "$latest_json" ]; then
        local total_tests=$(jq '.run.stats.tests.total' "$latest_json")
        local failed_tests=$(jq '.run.stats.tests.failed' "$latest_json")
        local passed_tests=$(jq '.run.stats.tests.passed' "$latest_json")
        local total_assertions=$(jq '.run.stats.assertions.total' "$latest_json")
        local failed_assertions=$(jq '.run.stats.assertions.failed' "$latest_json")
        
        echo ""
        log_info "=== RÉSULTATS DES TESTS ==="
        echo "Tests: $passed_tests/$total_tests passés"
        echo "Assertions: $((total_assertions - failed_assertions))/$total_assertions passées"
        
        if [ "$failed_tests" -eq 0 ] && [ "$failed_assertions" -eq 0 ]; then
            log_success "Tous les tests sont passés avec succès!"
            return 0
        else
            log_error "Certains tests ont échoué"
            return 1
        fi
    else
        log_warning "Impossible d'analyser les résultats (fichier JSON non trouvé)"
        return 1
    fi
}

# Nettoyer les anciens rapports
cleanup_old_reports() {
    log_info "Nettoyage des anciens rapports..."
    
    # Garder seulement les 10 derniers rapports
    find "$REPORTS_DIR" -name "*.html" -type f | sort -r | tail -n +11 | xargs rm -f 2>/dev/null || true
    find "$REPORTS_DIR" -name "*.json" -type f | sort -r | tail -n +11 | xargs rm -f 2>/dev/null || true
    find "$REPORTS_DIR" -name "*.xml" -type f | sort -r | tail -n +11 | xargs rm -f 2>/dev/null || true
    
    log_success "Nettoyage terminé"
}

# Afficher l'aide
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help              Afficher cette aide"
    echo "  -u, --url URL           URL du service (défaut: $SERVICE_URL)"
    echo "  -w, --wait SECONDS      Temps d'attente max pour le service (défaut: $MAX_WAIT_TIME)"
    echo "  -d, --detailed          Générer un rapport détaillé"
    echo "  -c, --cleanup           Nettoyer les anciens rapports"
    echo "  --no-wait              Ne pas attendre la disponibilité du service"
    echo ""
    echo "Exemples:"
    echo "  $0                      Exécuter les tests avec les paramètres par défaut"
    echo "  $0 -u http://localhost:8086  Tester sur un autre port"
    echo "  $0 -d                   Générer un rapport détaillé"
    echo "  $0 -c                   Nettoyer et exécuter les tests"
}

# Fonction principale
main() {
    local detailed=false
    local cleanup=false
    local wait_service=true
    
    # Traitement des arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -u|--url)
                SERVICE_URL="$2"
                shift 2
                ;;
            -w|--wait)
                MAX_WAIT_TIME="$2"
                shift 2
                ;;
            -d|--detailed)
                detailed=true
                shift
                ;;
            -c|--cleanup)
                cleanup=true
                shift
                ;;
            --no-wait)
                wait_service=false
                shift
                ;;
            *)
                log_error "Option inconnue: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    log_info "=== Tests Postman Finance Service ==="
    log_info "URL du service: $SERVICE_URL"
    log_info "Répertoire de rapports: $REPORTS_DIR"
    echo ""
    
    # Exécution des étapes
    check_prerequisites
    
    if [ "$cleanup" = true ]; then
        cleanup_old_reports
    fi
    
    setup_reports_directory
    
    if [ "$wait_service" = true ]; then
        wait_for_service || exit 1
    fi
    
    run_newman_tests || exit 1
    
    if [ "$detailed" = true ]; then
        run_detailed_tests
    fi
    
    analyze_results
    local result=$?
    
    echo ""
    log_info "=== Tests terminés ==="
    
    if [ $result -eq 0 ]; then
        log_success "Tous les tests sont passés avec succès!"
        exit 0
    else
        log_error "Certains tests ont échoué"
        exit 1
    fi
}

# Exécution du script
main "$@"
