# Intégration Frontend-Backend - Medical Admin Service

## 🔗 Configuration de l'intégration

### URLs et endpoints

**Backend Spring Boot** : `http://localhost:8083`
**Frontend Angular** : `http://localhost:4203`

### Configuration CORS

Le backend est configuré pour accepter les requêtes du frontend :

```java
@CrossOrigin(origins = {"http://localhost:4203", "http://localhost:4200"})
```

## 📡 Services Angular ↔ REST API

### 1. DonneesSanteService ↔ DonneesSanteController

| Méthode Angular | Endpoint REST | Description |
|----------------|---------------|-------------|
| `getAllDonneesSante()` | `GET /api/donnees-sante` | Récupérer toutes les données |
| `getDonneesSanteById(id)` | `GET /api/donnees-sante/{id}` | Récupérer par ID |
| `creerDonneesSante(donnees)` | `POST /api/donnees-sante` | Créer nouvelle donnée |
| `modifierDonneesSante(id, donnees)` | `PUT /api/donnees-sante/{id}` | Modifier donnée |
| `supprimerDonneesSante(id)` | `DELETE /api/donnees-sante/{id}` | Supprimer donnée |
| `getDonneesSanteParJoueur(joueurId)` | `GET /api/donnees-sante/joueur/{joueurId}` | Données par joueur |
| `rechercherDonneesSante(criteres)` | `GET /api/donnees-sante/recherche` | Recherche avec filtres |
| `getStatistiques()` | `GET /api/donnees-sante/stats` | Statistiques globales |

### 2. RendezVousService ↔ RendezVousController

| Méthode Angular | Endpoint REST | Description |
|----------------|---------------|-------------|
| `getAllRendezVous()` | `GET /api/rendez-vous` | Tous les rendez-vous |
| `getRendezVousById(id)` | `GET /api/rendez-vous/{id}` | Rendez-vous par ID |
| `creerRendezVous(rdv)` | `POST /api/rendez-vous` | Créer rendez-vous |
| `modifierRendezVous(id, rdv)` | `PUT /api/rendez-vous/{id}` | Modifier rendez-vous |
| `confirmerRendezVous(id)` | `PUT /api/rendez-vous/{id}/confirmer` | Confirmer RDV |
| `annulerRendezVous(id, motif)` | `PUT /api/rendez-vous/{id}/annuler` | Annuler RDV |
| `getRendezVousAujourdhui()` | `GET /api/rendez-vous/aujourd-hui` | RDV du jour |
| `getPlanningJournalier(date)` | `GET /api/rendez-vous/planning-journalier` | Planning par jour |

### 3. DemandesAdministrativesService ↔ DemandesAdministrativesController

| Méthode Angular | Endpoint REST | Description |
|----------------|---------------|-------------|
| `getAllDemandes()` | `GET /api/demandes-administratives` | Toutes les demandes |
| `getDemandeById(id)` | `GET /api/demandes-administratives/{id}` | Demande par ID |
| `creerDemande(demande)` | `POST /api/demandes-administratives` | Créer demande |
| `modifierDemande(id, demande)` | `PUT /api/demandes-administratives/{id}` | Modifier demande |
| `donnerValidationCoach(id, validation)` | `PUT /api/demandes-administratives/{id}/validation-coach` | Validation coach |
| `donnerValidationMedical(id, validation)` | `PUT /api/demandes-administratives/{id}/validation-medical` | Validation médicale |
| `approuverDemande(id, commentaire)` | `PUT /api/demandes-administratives/{id}/approuver` | Approuver demande |
| `getDemandesEnAttente()` | `GET /api/demandes-administratives/en-attente` | Demandes en attente |

## 🔧 Configuration des intercepteurs

### AuthInterceptor
```typescript
// Ajoute automatiquement le token JWT à toutes les requêtes
const authReq = req.clone({
  headers: req.headers.set('Authorization', `Bearer ${token}`)
});
```

### ErrorInterceptor
```typescript
// Gestion centralisée des erreurs HTTP
switch (error.status) {
  case 401: router.navigate(['/auth/login']); break;
  case 403: toastr.error('Accès interdit'); break;
  case 404: toastr.error('Ressource non trouvée'); break;
  case 500: toastr.error('Erreur serveur'); break;
}
```

### LoadingInterceptor
```typescript
// Affichage automatique du spinner de chargement
loadingService.show();
return next(req).pipe(finalize(() => loadingService.hide()));
```

## 📊 Modèles de données partagés

### DonneesSante
```typescript
// Frontend (TypeScript)
interface DonneesSante {
  id?: number;
  joueurId: number;
  typeExamen: TypeExamen;
  dateExamen: string;
  resultats?: string;
  statut: StatutDonneesSante;
}

// Backend (Java)
@Entity
public class DonneesSante {
  @Id @GeneratedValue
  private Long id;
  @Column(nullable = false)
  private Long joueurId;
  @Enumerated(EnumType.STRING)
  private TypeExamen typeExamen;
}
```

### RendezVousMedical
```typescript
// Frontend
interface RendezVousMedical {
  id?: number;
  joueurId: number;
  staffMedicalId: number;
  typeRendezVous: TypeRendezVous;
  dateRendezVous: string;
  heureDebut: string;
  statut: StatutRendezVous;
}

// Backend
@Entity
public class RendezVousMedical {
  @Id @GeneratedValue
  private Long id;
  private Long joueurId;
  private Long staffMedicalId;
  @Enumerated(EnumType.STRING)
  private TypeRendezVous typeRendezVous;
}
```

## 🚀 Tests d'intégration

### Démarrage des services
```bash
# Backend (port 8083)
cd microservices/medical-admin-service/backend
mvn spring-boot:run

# Frontend (port 4203)
cd microservices/medical-admin-service/frontend
npm start
```

### Tests avec REST Client
Utiliser le fichier `integration-test.http` pour tester tous les endpoints.

### Tests automatisés
```bash
# Tests backend
mvn test

# Tests frontend
npm run test

# Tests e2e
npm run e2e
```

## 🔐 Sécurité

### Authentification JWT
1. Le frontend récupère le token depuis le service d'authentification
2. L'AuthInterceptor ajoute automatiquement le token aux requêtes
3. Le backend valide le token avec Spring Security

### Gestion des erreurs
- **401 Unauthorized** : Redirection vers login
- **403 Forbidden** : Message d'erreur + log
- **404 Not Found** : Page d'erreur personnalisée
- **500 Server Error** : Notification d'erreur système

## 📱 Interface utilisateur

### Navigation
- **Navbar** : Navigation principale avec menu utilisateur
- **Sidebar** : Menu latéral avec badges de notification
- **Breadcrumb** : Fil d'Ariane pour la navigation

### Composants principaux
- **Dashboard** : Vue d'ensemble avec statistiques
- **Tables** : Listes paginées avec tri et filtres
- **Formulaires** : Création/édition avec validation
- **Modals** : Confirmations et détails rapides

### Responsive Design
- Interface adaptative mobile/desktop
- Navigation mobile avec menu hamburger
- Tableaux avec scroll horizontal sur mobile

## 🔄 Flux de données

### Création d'une donnée de santé
1. **Frontend** : Formulaire de saisie
2. **Validation** : Contrôles côté client
3. **Envoi** : POST vers `/api/donnees-sante`
4. **Backend** : Validation + sauvegarde
5. **Réponse** : Donnée créée avec ID
6. **Frontend** : Notification + redirection

### Consultation du planning
1. **Frontend** : Sélection de date
2. **Requête** : GET `/api/rendez-vous/planning-journalier`
3. **Backend** : Récupération des RDV du jour
4. **Frontend** : Affichage en calendrier/liste

### Workflow de validation
1. **Création** : Demande administrative
2. **Notifications** : Alertes aux validateurs
3. **Validations** : Coach → Médical → Financier
4. **Approbation** : Décision finale
5. **Notification** : Demandeur informé

## 📈 Performance

### Optimisations frontend
- Lazy loading des modules
- OnPush change detection
- Pagination côté serveur
- Cache des données statiques

### Optimisations backend
- Pagination JPA
- Requêtes optimisées
- Cache Redis (à implémenter)
- Index de base de données

## 🐛 Debugging

### Logs frontend
```typescript
// Console du navigateur
console.log('API Response:', response);

// Service de logging
this.logger.debug('User action', { userId, action });
```

### Logs backend
```java
// Logback configuration
@Slf4j
public class DonneesSanteService {
    log.info("Creating new health data for player {}", joueurId);
}
```

### Outils de développement
- **Angular DevTools** : Inspection des composants
- **Redux DevTools** : État de l'application
- **Network Tab** : Analyse des requêtes HTTP
- **Spring Boot Actuator** : Métriques backend

## 🔮 Améliorations futures

### Temps réel
- WebSocket pour notifications live
- Mise à jour automatique des données
- Chat intégré pour communication

### Offline
- Service Worker pour cache
- Synchronisation différée
- Mode hors ligne

### Analytics
- Tracking des actions utilisateur
- Métriques de performance
- Rapports d'utilisation
