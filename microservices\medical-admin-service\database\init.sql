-- Script d'initialisation de la base de données pour Medical Admin Service
-- Création du schéma et des tables pour la gestion médicale et administrative

-- Création du schéma dédié
CREATE SCHEMA IF NOT EXISTS medical_admin;

-- Utilisation du schéma
SET search_path TO medical_admin;

-- Table des données de santé
CREATE TABLE IF NOT EXISTS donnees_sante (
    id BIGSERIAL PRIMARY KEY,
    joueur_id BIGINT NOT NULL,
    staff_medical_id BIGINT,
    type_examen VARCHAR(100) NOT NULL,
    date_examen DATE NOT NULL,
    resultats TEXT,
    recommandations TEXT,
    blessures TEXT,
    traitements TEXT,
    medicaments TEXT,
    statut VARCHAR(50) DEFAULT 'ACTIF',
    gravite VARCHAR(50),
    date_guerison_prevue DATE,
    necessite_suivi BOOLEAN DEFAULT FALSE,
    visible_par_joueur BOOLEAN DEFAULT TRUE,
    notes_confidentielles TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Index pour optimiser les requêtes
CREATE INDEX IF NOT EXISTS idx_donnees_sante_joueur_id ON donnees_sante(joueur_id);
CREATE INDEX IF NOT EXISTS idx_donnees_sante_staff_medical_id ON donnees_sante(staff_medical_id);
CREATE INDEX IF NOT EXISTS idx_donnees_sante_type_examen ON donnees_sante(type_examen);
CREATE INDEX IF NOT EXISTS idx_donnees_sante_date_examen ON donnees_sante(date_examen);
CREATE INDEX IF NOT EXISTS idx_donnees_sante_statut ON donnees_sante(statut);
CREATE INDEX IF NOT EXISTS idx_donnees_sante_gravite ON donnees_sante(gravite);

-- Table des rendez-vous médicaux
CREATE TABLE IF NOT EXISTS rendez_vous_medicaux (
    id BIGSERIAL PRIMARY KEY,
    joueur_id BIGINT NOT NULL,
    staff_medical_id BIGINT NOT NULL,
    type_rendez_vous VARCHAR(100) NOT NULL,
    date_rendez_vous DATE NOT NULL,
    heure_debut TIME NOT NULL,
    heure_fin TIME NOT NULL,
    lieu VARCHAR(200),
    description TEXT,
    statut VARCHAR(50) DEFAULT 'PLANIFIE',
    priorite VARCHAR(50) DEFAULT 'NORMALE',
    rappel_envoye BOOLEAN DEFAULT FALSE,
    date_rappel TIMESTAMP,
    compte_rendu TEXT,
    prescriptions TEXT,
    prochaine_visite_prevue DATE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Index pour optimiser les requêtes
CREATE INDEX IF NOT EXISTS idx_rendez_vous_joueur_id ON rendez_vous_medicaux(joueur_id);
CREATE INDEX IF NOT EXISTS idx_rendez_vous_staff_medical_id ON rendez_vous_medicaux(staff_medical_id);
CREATE INDEX IF NOT EXISTS idx_rendez_vous_date ON rendez_vous_medicaux(date_rendez_vous);
CREATE INDEX IF NOT EXISTS idx_rendez_vous_statut ON rendez_vous_medicaux(statut);
CREATE INDEX IF NOT EXISTS idx_rendez_vous_type ON rendez_vous_medicaux(type_rendez_vous);
CREATE INDEX IF NOT EXISTS idx_rendez_vous_priorite ON rendez_vous_medicaux(priorite);

-- Table des demandes administratives
CREATE TABLE IF NOT EXISTS demandes_administratives (
    id BIGSERIAL PRIMARY KEY,
    demandeur_id BIGINT NOT NULL,
    type_demandeur VARCHAR(50) NOT NULL,
    approbateur_id BIGINT,
    type_demande VARCHAR(100) NOT NULL,
    titre VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    justification TEXT,
    date_soumission DATE NOT NULL,
    date_echeance DATE,
    statut VARCHAR(50) DEFAULT 'EN_ATTENTE',
    priorite VARCHAR(50) DEFAULT 'NORMALE',
    cout_estime DECIMAL(10,2),
    cout_reel DECIMAL(10,2),
    commentaire_approbateur TEXT,
    date_traitement TIMESTAMP,
    date_validation TIMESTAMP,
    necessite_validation_coach BOOLEAN DEFAULT FALSE,
    validation_coach BOOLEAN,
    necessite_validation_medical BOOLEAN DEFAULT FALSE,
    validation_medical BOOLEAN,
    necessite_validation_financier BOOLEAN DEFAULT FALSE,
    validation_financier BOOLEAN,
    documents_joints TEXT,
    historique_statuts TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Index pour optimiser les requêtes
CREATE INDEX IF NOT EXISTS idx_demandes_demandeur_id ON demandes_administratives(demandeur_id);
CREATE INDEX IF NOT EXISTS idx_demandes_approbateur_id ON demandes_administratives(approbateur_id);
CREATE INDEX IF NOT EXISTS idx_demandes_type_demande ON demandes_administratives(type_demande);
CREATE INDEX IF NOT EXISTS idx_demandes_statut ON demandes_administratives(statut);
CREATE INDEX IF NOT EXISTS idx_demandes_priorite ON demandes_administratives(priorite);
CREATE INDEX IF NOT EXISTS idx_demandes_date_soumission ON demandes_administratives(date_soumission);
CREATE INDEX IF NOT EXISTS idx_demandes_date_echeance ON demandes_administratives(date_echeance);

-- Fonction pour mettre à jour automatiquement updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers pour mettre à jour automatiquement updated_at
CREATE TRIGGER update_donnees_sante_updated_at 
    BEFORE UPDATE ON donnees_sante 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_rendez_vous_updated_at 
    BEFORE UPDATE ON rendez_vous_medicaux 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_demandes_updated_at 
    BEFORE UPDATE ON demandes_administratives 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Données de test pour le développement
INSERT INTO donnees_sante (joueur_id, staff_medical_id, type_examen, date_examen, resultats, statut, gravite) VALUES
(1, 1, 'BILAN_GENERAL', CURRENT_DATE - INTERVAL '30 days', 'Bilan général satisfaisant. Condition physique excellente.', 'ACTIF', 'FAIBLE'),
(2, 1, 'BLESSURE', CURRENT_DATE - INTERVAL '15 days', 'Entorse cheville droite. Repos recommandé.', 'EN_TRAITEMENT', 'MOYENNE'),
(3, 2, 'SUIVI', CURRENT_DATE - INTERVAL '7 days', 'Suivi post-blessure. Évolution positive.', 'SUIVI', 'FAIBLE'),
(1, 2, 'PREVENTION', CURRENT_DATE - INTERVAL '5 days', 'Séance de prévention. Exercices de renforcement.', 'ACTIF', 'FAIBLE');

INSERT INTO rendez_vous_medicaux (joueur_id, staff_medical_id, type_rendez_vous, date_rendez_vous, heure_debut, heure_fin, lieu, statut, priorite) VALUES
(1, 1, 'CONSULTATION', CURRENT_DATE + INTERVAL '1 day', '09:00', '09:30', 'Cabinet médical', 'PLANIFIE', 'NORMALE'),
(2, 1, 'SUIVI_BLESSURE', CURRENT_DATE + INTERVAL '2 days', '14:00', '14:45', 'Centre médical', 'CONFIRME', 'ELEVEE'),
(3, 2, 'BILAN_ANNUEL', CURRENT_DATE + INTERVAL '7 days', '10:00', '11:00', 'Clinique sportive', 'PLANIFIE', 'NORMALE'),
(1, 2, 'PREVENTION', CURRENT_DATE + INTERVAL '14 days', '16:00', '16:30', 'Centre de rééducation', 'PLANIFIE', 'FAIBLE');

INSERT INTO demandes_administratives (demandeur_id, type_demandeur, type_demande, titre, description, date_soumission, priorite, cout_estime, necessite_validation_coach, necessite_validation_medical) VALUES
(1, 'JOUEUR', 'CONGE', 'Demande de congé médical', 'Demande de congé pour récupération suite à blessure', CURRENT_DATE - INTERVAL '2 days', 'ELEVEE', 0.00, true, true),
(2, 'STAFF', 'MATERIEL', 'Achat équipement médical', 'Demande d''achat de matériel de rééducation', CURRENT_DATE - INTERVAL '5 days', 'NORMALE', 1500.00, false, true),
(3, 'JOUEUR', 'FORMATION', 'Formation premiers secours', 'Participation à une formation premiers secours', CURRENT_DATE - INTERVAL '1 day', 'FAIBLE', 300.00, true, false),
(1, 'STAFF', 'REMBOURSEMENT', 'Remboursement frais médicaux', 'Remboursement consultation spécialisée', CURRENT_DATE, 'NORMALE', 150.00, false, true);

-- Vues pour faciliter les requêtes courantes
CREATE OR REPLACE VIEW v_donnees_sante_actives AS
SELECT 
    ds.*,
    CASE 
        WHEN ds.date_guerison_prevue IS NOT NULL AND ds.date_guerison_prevue < CURRENT_DATE 
        THEN true 
        ELSE false 
    END as guerison_echue
FROM donnees_sante ds
WHERE ds.statut IN ('ACTIF', 'EN_TRAITEMENT', 'SUIVI');

CREATE OR REPLACE VIEW v_rendez_vous_planning AS
SELECT 
    rv.*,
    CASE 
        WHEN rv.date_rendez_vous = CURRENT_DATE THEN 'AUJOURD_HUI'
        WHEN rv.date_rendez_vous = CURRENT_DATE + INTERVAL '1 day' THEN 'DEMAIN'
        WHEN rv.date_rendez_vous BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '7 days' THEN 'CETTE_SEMAINE'
        ELSE 'PLUS_TARD'
    END as periode
FROM rendez_vous_medicaux rv
WHERE rv.statut IN ('PLANIFIE', 'CONFIRME', 'EN_COURS')
ORDER BY rv.date_rendez_vous, rv.heure_debut;

CREATE OR REPLACE VIEW v_demandes_workflow AS
SELECT 
    da.*,
    CASE 
        WHEN da.date_echeance IS NOT NULL AND da.date_echeance < CURRENT_DATE 
        THEN true 
        ELSE false 
    END as echue,
    CASE 
        WHEN da.necessite_validation_coach AND da.validation_coach IS NULL THEN false
        WHEN da.necessite_validation_medical AND da.validation_medical IS NULL THEN false
        WHEN da.necessite_validation_financier AND da.validation_financier IS NULL THEN false
        ELSE true
    END as toutes_validations_obtenues
FROM demandes_administratives da
WHERE da.statut IN ('EN_ATTENTE', 'EN_TRAITEMENT');

-- Commentaires sur les tables
COMMENT ON TABLE donnees_sante IS 'Table des données de santé et examens médicaux des joueurs';
COMMENT ON TABLE rendez_vous_medicaux IS 'Table des rendez-vous et consultations médicales';
COMMENT ON TABLE demandes_administratives IS 'Table des demandes administratives avec workflow de validation';

-- Commentaires sur les colonnes importantes
COMMENT ON COLUMN donnees_sante.type_examen IS 'Type d''examen: BILAN_GENERAL, BLESSURE, SUIVI, PREVENTION';
COMMENT ON COLUMN donnees_sante.statut IS 'Statut: ACTIF, GUERI, EN_TRAITEMENT, SUIVI';
COMMENT ON COLUMN donnees_sante.gravite IS 'Gravité: FAIBLE, MOYENNE, ELEVEE, CRITIQUE';

COMMENT ON COLUMN rendez_vous_medicaux.statut IS 'Statut: PLANIFIE, CONFIRME, EN_COURS, TERMINE, ANNULE, REPORTE';
COMMENT ON COLUMN rendez_vous_medicaux.priorite IS 'Priorité: FAIBLE, NORMALE, ELEVEE, URGENTE';

COMMENT ON COLUMN demandes_administratives.type_demande IS 'Type: CONGE, MATERIEL, FORMATION, REMBOURSEMENT, ACCES, AUTRE';
COMMENT ON COLUMN demandes_administratives.statut IS 'Statut: EN_ATTENTE, EN_TRAITEMENT, VALIDEE, REJETEE, SUSPENDUE';
COMMENT ON COLUMN demandes_administratives.priorite IS 'Priorité: FAIBLE, NORMALE, ELEVEE, URGENTE';

-- Permissions (à adapter selon l'environnement)
-- GRANT ALL PRIVILEGES ON SCHEMA medical_admin TO medical_admin_user;
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA medical_admin TO medical_admin_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA medical_admin TO medical_admin_user;
