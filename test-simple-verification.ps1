Write-Host "=== VÉRIFICATION SIMPLE DU MICROSERVICE PLANNING PERFORMANCE ===" -ForegroundColor Green
Write-Host "Club Olympique de Kelibia" -ForegroundColor Cyan
Write-Host ""

# Variables
$planningPath = "microservices\planning-performance-service"
$backendPath = "$planningPath\backend"
$frontendPath = "$planningPath\frontend"

Write-Host "1. VÉRIFICATION DE LA STRUCTURE" -ForegroundColor Yellow
Write-Host "================================" -ForegroundColor Yellow

# Vérifier la structure principale
if (Test-Path $planningPath) {
    Write-Host "✅ Microservice Planning Performance trouvé" -ForegroundColor Green
} else {
    Write-Host "❌ Microservice Planning Performance non trouvé" -ForegroundColor Red
    exit 1
}

# Vérifier le backend
if (Test-Path $backendPath) {
    Write-Host "✅ Backend Spring Boot trouvé" -ForegroundColor Green
    
    # Vérifier pom.xml
    if (Test-Path "$backendPath\pom.xml") {
        Write-Host "✅ Configuration Maven (pom.xml) trouvée" -ForegroundColor Green
    } else {
        Write-Host "❌ pom.xml manquant" -ForegroundColor Red
    }
    
    # Vérifier application.yml
    if (Test-Path "$backendPath\src\main\resources\application.yml") {
        Write-Host "✅ Configuration Spring Boot (application.yml) trouvée" -ForegroundColor Green
    } else {
        Write-Host "❌ application.yml manquant" -ForegroundColor Red
    }
    
    # Vérifier les sources Java
    if (Test-Path "$backendPath\src\main\java") {
        Write-Host "✅ Sources Java trouvées" -ForegroundColor Green
        $javaFiles = Get-ChildItem -Path "$backendPath\src\main\java" -Recurse -Filter "*.java" | Measure-Object
        Write-Host "   Nombre de fichiers Java : $($javaFiles.Count)" -ForegroundColor Cyan
    } else {
        Write-Host "❌ Sources Java manquantes" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Backend non trouvé" -ForegroundColor Red
}

# Vérifier le frontend
if (Test-Path $frontendPath) {
    Write-Host "✅ Frontend Angular trouvé" -ForegroundColor Green
    
    # Vérifier package.json
    if (Test-Path "$frontendPath\package.json") {
        Write-Host "✅ Configuration npm (package.json) trouvée" -ForegroundColor Green
    } else {
        Write-Host "❌ package.json manquant" -ForegroundColor Red
    }
    
    # Vérifier les sources TypeScript
    if (Test-Path "$frontendPath\src") {
        Write-Host "✅ Sources Angular trouvées" -ForegroundColor Green
        $tsFiles = Get-ChildItem -Path "$frontendPath\src" -Recurse -Filter "*.ts" | Measure-Object
        Write-Host "   Nombre de fichiers TypeScript : $($tsFiles.Count)" -ForegroundColor Cyan
    } else {
        Write-Host "❌ Sources Angular manquantes" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Frontend non trouvé" -ForegroundColor Red
}

# Vérifier docker-compose.yml
if (Test-Path "$planningPath\docker-compose.yml") {
    Write-Host "✅ Configuration Docker Compose trouvée" -ForegroundColor Green
} else {
    Write-Host "❌ docker-compose.yml manquant" -ForegroundColor Red
}

Write-Host ""
Write-Host "2. ANALYSE DE LA CONFIGURATION" -ForegroundColor Yellow
Write-Host "===============================" -ForegroundColor Yellow

# Analyser application.yml
$appConfigPath = "$backendPath\src\main\resources\application.yml"
if (Test-Path $appConfigPath) {
    $config = Get-Content $appConfigPath -Raw
    
    Write-Host "Configuration Spring Boot :" -ForegroundColor Cyan
    
    if ($config -like "*port: 8082*") {
        Write-Host "✅ Port backend : 8082" -ForegroundColor Green
    } else {
        Write-Host "❓ Port backend non trouvé dans la config" -ForegroundColor Yellow
    }
    
    if ($config -like "*planning_performance_db*") {
        Write-Host "✅ Base de données : planning_performance_db" -ForegroundColor Green
    } else {
        Write-Host "❓ Configuration base de données non trouvée" -ForegroundColor Yellow
    }
    
    if ($config -like "*planning_user*") {
        Write-Host "✅ Utilisateur DB : planning_user" -ForegroundColor Green
    } else {
        Write-Host "❓ Utilisateur DB non trouvé" -ForegroundColor Yellow
    }
    
    if ($config -like "*5434*") {
        Write-Host "✅ Port base de données : 5434" -ForegroundColor Green
    } else {
        Write-Host "❓ Port DB non trouvé" -ForegroundColor Yellow
    }
}

# Analyser package.json
$packageJsonPath = "$frontendPath\package.json"
if (Test-Path $packageJsonPath) {
    $packageJson = Get-Content $packageJsonPath -Raw | ConvertFrom-Json
    
    Write-Host ""
    Write-Host "Configuration Angular :" -ForegroundColor Cyan
    Write-Host "✅ Nom du projet : $($packageJson.name)" -ForegroundColor Green
    Write-Host "✅ Version : $($packageJson.version)" -ForegroundColor Green
    
    if ($packageJson.dependencies."@angular/core") {
        Write-Host "✅ Angular version : $($packageJson.dependencies.'@angular/core')" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "3. VÉRIFICATION DES OUTILS REQUIS" -ForegroundColor Yellow
Write-Host "==================================" -ForegroundColor Yellow

# Vérifier Java
try {
    $javaVersion = java -version 2>&1 | Select-String "version"
    if ($javaVersion) {
        Write-Host "✅ Java installé : $($javaVersion.Line)" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Java non installé ou non accessible" -ForegroundColor Red
}

# Vérifier Maven
try {
    $mavenVersion = mvn --version 2>&1 | Select-String "Apache Maven"
    if ($mavenVersion) {
        Write-Host "✅ Maven installé : $($mavenVersion.Line)" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Maven non installé" -ForegroundColor Red
}

# Vérifier Node.js
try {
    $nodeVersion = node --version 2>&1
    if ($nodeVersion) {
        Write-Host "✅ Node.js installé : $nodeVersion" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Node.js non installé" -ForegroundColor Red
}

# Vérifier npm
try {
    $npmVersion = npm --version 2>&1
    if ($npmVersion) {
        Write-Host "✅ npm installé : $npmVersion" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ npm non installé" -ForegroundColor Red
}

# Vérifier Docker
try {
    $dockerVersion = docker --version 2>&1
    if ($dockerVersion) {
        Write-Host "✅ Docker installé : $dockerVersion" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Docker non installé ou non accessible" -ForegroundColor Red
}

Write-Host ""
Write-Host "4. RECOMMANDATIONS POUR LE TEST" -ForegroundColor Yellow
Write-Host "================================" -ForegroundColor Yellow

Write-Host ""
Write-Host "MÉTHODE RECOMMANDÉE - Test sans Docker :" -ForegroundColor Cyan
Write-Host ""
Write-Host "1. Installer les outils manquants :" -ForegroundColor White
Write-Host "   - Java 17+ : https://adoptium.net/" -ForegroundColor Gray
Write-Host "   - Maven 3.8+ : https://maven.apache.org/download.cgi" -ForegroundColor Gray
Write-Host "   - Node.js 18+ : https://nodejs.org/" -ForegroundColor Gray
Write-Host ""
Write-Host "2. Utiliser une base de données en ligne (optionnel) :" -ForegroundColor White
Write-Host "   - PostgreSQL sur Heroku, Railway, ou Supabase" -ForegroundColor Gray
Write-Host "   - Modifier application.yml avec les nouvelles credentials" -ForegroundColor Gray
Write-Host ""
Write-Host "3. Lancer le backend :" -ForegroundColor White
Write-Host "   cd microservices\planning-performance-service\backend" -ForegroundColor Gray
Write-Host "   mvn spring-boot:run" -ForegroundColor Gray
Write-Host ""
Write-Host "4. Lancer le frontend (nouveau terminal) :" -ForegroundColor White
Write-Host "   cd microservices\planning-performance-service\frontend" -ForegroundColor Gray
Write-Host "   npm install" -ForegroundColor Gray
Write-Host "   ng serve --port 4202" -ForegroundColor Gray
Write-Host ""
Write-Host "5. Tester les URLs :" -ForegroundColor White
Write-Host "   Frontend : http://localhost:4202" -ForegroundColor Gray
Write-Host "   Backend  : http://localhost:8082/actuator/health" -ForegroundColor Gray

Write-Host ""
Write-Host "ALTERNATIVE - Test avec base de données H2 (en mémoire) :" -ForegroundColor Cyan
Write-Host "   Modifier application.yml pour utiliser H2 au lieu de PostgreSQL" -ForegroundColor Gray
Write-Host "   Pas besoin de base de données externe" -ForegroundColor Gray

Write-Host ""
Write-Host "=== RÉSUMÉ ===" -ForegroundColor Green
Write-Host "Le microservice Planning Performance est structurellement complet !" -ForegroundColor Green
Write-Host "Il faut juste installer les outils de développement pour le tester." -ForegroundColor Cyan
Write-Host ""
Write-Host "🏐 Club Olympique de Kelibia - Microservice prêt pour développement !" -ForegroundColor Yellow

Read-Host "Appuyez sur Entrée pour continuer"
