package com.sprintbot.communication.service;

import com.sprintbot.communication.entity.*;
import com.sprintbot.communication.repository.ConversationRepository;
import com.sprintbot.communication.repository.ParticipantConversationRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service pour la gestion des conversations
 */
@Service
@Transactional
public class ConversationService {

    private static final Logger logger = LoggerFactory.getLogger(ConversationService.class);

    @Autowired
    private ConversationRepository conversationRepository;

    @Autowired
    private ParticipantConversationRepository participantRepository;

    @Autowired
    private NotificationService notificationService;

    /**
     * Crée une nouvelle conversation
     */
    public Conversation creerConversation(String nom, TypeConversation type, Long createurId, 
                                        String description, List<Long> participantIds) {
        logger.info("Création d'une nouvelle conversation: {} de type: {} par utilisateur: {}", 
                   nom, type, createurId);

        Conversation conversation = new Conversation(nom, type, createurId);
        conversation.setDescription(description);
        conversation = conversationRepository.save(conversation);

        // Ajouter le créateur comme admin
        ajouterParticipant(conversation.getId(), createurId, RoleParticipant.ADMIN);

        // Ajouter les autres participants
        if (participantIds != null) {
            for (Long participantId : participantIds) {
                if (!participantId.equals(createurId)) {
                    ajouterParticipant(conversation.getId(), participantId, RoleParticipant.MEMBRE);
                }
            }
        }

        // Envoyer notification aux participants
        notifierNouvelleConversation(conversation, participantIds);

        logger.info("Conversation créée avec succès: ID {}", conversation.getId());
        return conversation;
    }

    /**
     * Trouve ou crée une conversation privée entre deux utilisateurs
     */
    public Conversation obtenirConversationPrivee(Long utilisateur1Id, Long utilisateur2Id) {
        Optional<Conversation> existante = conversationRepository
                .findConversationPriveeEntreUtilisateurs(utilisateur1Id, utilisateur2Id);

        if (existante.isPresent()) {
            return existante.get();
        }

        // Créer nouvelle conversation privée
        String nom = "Conversation privée";
        Conversation conversation = new Conversation(nom, TypeConversation.PRIVE, utilisateur1Id);
        conversation = conversationRepository.save(conversation);

        // Ajouter les deux participants
        ajouterParticipant(conversation.getId(), utilisateur1Id, RoleParticipant.MEMBRE);
        ajouterParticipant(conversation.getId(), utilisateur2Id, RoleParticipant.MEMBRE);

        logger.info("Nouvelle conversation privée créée entre {} et {}: ID {}", 
                   utilisateur1Id, utilisateur2Id, conversation.getId());
        return conversation;
    }

    /**
     * Ajoute un participant à une conversation
     */
    public void ajouterParticipant(Long conversationId, Long utilisateurId, RoleParticipant role) {
        Optional<Conversation> conversationOpt = conversationRepository.findById(conversationId);
        if (conversationOpt.isEmpty()) {
            throw new RuntimeException("Conversation non trouvée: " + conversationId);
        }

        Conversation conversation = conversationOpt.get();
        
        // Vérifier si déjà participant
        if (participantRepository.existsByConversationIdAndUtilisateurId(conversationId, utilisateurId)) {
            logger.warn("L'utilisateur {} est déjà participant de la conversation {}", 
                       utilisateurId, conversationId);
            return;
        }

        ParticipantConversation participant = new ParticipantConversation(conversation, utilisateurId, role);
        participantRepository.save(participant);

        conversation.ajouterParticipant(utilisateurId, role);
        conversationRepository.save(conversation);

        // Notifier le nouvel participant
        notificationService.notifierAjoutConversation(conversationId, utilisateurId);

        logger.info("Participant {} ajouté à la conversation {} avec le rôle {}", 
                   utilisateurId, conversationId, role);
    }

    /**
     * Supprime un participant d'une conversation
     */
    public void supprimerParticipant(Long conversationId, Long utilisateurId, Long demandeurId) {
        Optional<Conversation> conversationOpt = conversationRepository.findById(conversationId);
        if (conversationOpt.isEmpty()) {
            throw new RuntimeException("Conversation non trouvée: " + conversationId);
        }

        Conversation conversation = conversationOpt.get();

        // Vérifier les permissions
        if (!peutModifierParticipants(conversationId, demandeurId) && !demandeurId.equals(utilisateurId)) {
            throw new RuntimeException("Permission refusée pour modifier les participants");
        }

        participantRepository.deleteByConversationIdAndUtilisateurId(conversationId, utilisateurId);
        conversation.supprimerParticipant(utilisateurId);
        conversationRepository.save(conversation);

        // Notifier la suppression
        notificationService.notifierSuppressionConversation(conversationId, utilisateurId);

        logger.info("Participant {} supprimé de la conversation {}", utilisateurId, conversationId);
    }

    /**
     * Obtient les conversations d'un utilisateur
     */
    @Transactional(readOnly = true)
    public Page<Conversation> obtenirConversationsUtilisateur(Long utilisateurId, Pageable pageable) {
        return conversationRepository.findByUtilisateurIdAndStatut(
                utilisateurId, StatutConversation.ACTIF, pageable);
    }

    /**
     * Recherche des conversations
     */
    @Transactional(readOnly = true)
    public List<Conversation> rechercherConversations(Long utilisateurId, String recherche) {
        return conversationRepository.rechercherConversations(utilisateurId, recherche);
    }

    /**
     * Archive une conversation
     */
    public void archiverConversation(Long conversationId, Long utilisateurId) {
        Optional<Conversation> conversationOpt = conversationRepository.findById(conversationId);
        if (conversationOpt.isEmpty()) {
            throw new RuntimeException("Conversation non trouvée: " + conversationId);
        }

        Conversation conversation = conversationOpt.get();

        // Vérifier les permissions
        if (!conversation.peutEtreModifieeParUtilisateur(utilisateurId)) {
            throw new RuntimeException("Permission refusée pour archiver cette conversation");
        }

        conversation.archiver();
        conversationRepository.save(conversation);

        logger.info("Conversation {} archivée par l'utilisateur {}", conversationId, utilisateurId);
    }

    /**
     * Épingle/désépingle une conversation pour un utilisateur
     */
    public void toggleEpingleConversation(Long conversationId, Long utilisateurId) {
        Optional<ParticipantConversation> participantOpt = 
                participantRepository.findByConversationIdAndUtilisateurId(conversationId, utilisateurId);
        
        if (participantOpt.isEmpty()) {
            throw new RuntimeException("Utilisateur non participant de cette conversation");
        }

        ParticipantConversation participant = participantOpt.get();
        if (participant.getEstEpinglee()) {
            participant.desepingler();
        } else {
            participant.epingler();
        }
        participantRepository.save(participant);

        logger.info("Conversation {} épinglée/désépinglée pour l'utilisateur {}", 
                   conversationId, utilisateurId);
    }

    /**
     * Met à jour les paramètres de notification d'une conversation
     */
    public void mettreAJourNotifications(Long conversationId, Long utilisateurId, boolean activer) {
        Optional<ParticipantConversation> participantOpt = 
                participantRepository.findByConversationIdAndUtilisateurId(conversationId, utilisateurId);
        
        if (participantOpt.isEmpty()) {
            throw new RuntimeException("Utilisateur non participant de cette conversation");
        }

        ParticipantConversation participant = participantOpt.get();
        if (activer) {
            participant.activerNotifications();
        } else {
            participant.desactiverNotifications();
        }
        participantRepository.save(participant);

        logger.info("Notifications {} pour la conversation {} et l'utilisateur {}", 
                   activer ? "activées" : "désactivées", conversationId, utilisateurId);
    }

    /**
     * Marque une conversation comme lue
     */
    public void marquerCommeLue(Long conversationId, Long utilisateurId) {
        participantRepository.mettreAJourDerniereLecture(conversationId, utilisateurId, LocalDateTime.now());
        logger.debug("Conversation {} marquée comme lue par l'utilisateur {}", conversationId, utilisateurId);
    }

    /**
     * Obtient les statistiques d'une conversation
     */
    @Transactional(readOnly = true)
    public Object[] obtenirStatistiquesConversation(Long conversationId) {
        // Retourne: [nombreParticipants, nombreMessages, derniereActivite]
        Optional<Conversation> conversationOpt = conversationRepository.findById(conversationId);
        if (conversationOpt.isEmpty()) {
            return new Object[]{0, 0, null};
        }

        Conversation conversation = conversationOpt.get();
        return new Object[]{
                conversation.getNombreParticipants(),
                conversation.getNombreMessages(),
                conversation.getDerniereActivite()
        };
    }

    /**
     * Vérifie si un utilisateur peut modifier les participants
     */
    private boolean peutModifierParticipants(Long conversationId, Long utilisateurId) {
        Optional<ParticipantConversation> participantOpt = 
                participantRepository.findByConversationIdAndUtilisateurId(conversationId, utilisateurId);
        
        return participantOpt.isPresent() && participantOpt.get().peutModererConversation();
    }

    /**
     * Notifie les participants d'une nouvelle conversation
     */
    private void notifierNouvelleConversation(Conversation conversation, List<Long> participantIds) {
        if (participantIds != null) {
            for (Long participantId : participantIds) {
                if (!participantId.equals(conversation.getCreateurId())) {
                    notificationService.notifierNouvelleConversation(conversation.getId(), participantId);
                }
            }
        }
    }

    /**
     * Met à jour l'activité d'une conversation
     */
    public void mettreAJourActivite(Long conversationId) {
        conversationRepository.mettreAJourDerniereActivite(conversationId, LocalDateTime.now());
    }

    /**
     * Obtient les conversations avec messages non lus
     */
    @Transactional(readOnly = true)
    public List<Conversation> obtenirConversationsAvecMessagesNonLus(Long utilisateurId) {
        return conversationRepository.findConversationsAvecMessagesNonLus(utilisateurId);
    }

    /**
     * Nettoie les conversations archivées anciennes
     */
    @Transactional
    public void nettoyerConversationsArchivees() {
        LocalDateTime dateLimit = LocalDateTime.now().minusMonths(6);
        conversationRepository.supprimerConversationsArchiveesAnciennes(dateLimit);
        logger.info("Nettoyage des conversations archivées avant {}", dateLimit);
    }
}
