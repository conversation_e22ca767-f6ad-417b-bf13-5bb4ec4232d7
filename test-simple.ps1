Write-Host "=== Test Integration Microservices ===" -ForegroundColor Green

# Test 1: Verifier si le frontend auth-user-service fonctionne
Write-Host "1. Test du frontend auth-user-service..." -ForegroundColor Yellow

if (Test-Path "microservices\auth-user-service\frontend") {
    Set-Location "microservices\auth-user-service\frontend"
    
    Write-Host "   Installation des dependances..." -ForegroundColor Cyan
    npm install --silent
    
    Write-Host "   Demarrage du serveur de developpement..." -ForegroundColor Cyan
    Start-Process -FilePath "ng" -ArgumentList "serve", "--port", "4201" -WindowStyle Normal
    
    Set-Location "..\..\..\"
    Write-Host "   Frontend demarre sur http://localhost:4201" -ForegroundColor Green
} else {
    Write-Host "   Frontend non trouve" -ForegroundColor Red
}

Write-Host ""
Write-Host "2. Verification de l'integration..." -ForegroundColor Yellow
Write-Host "   - Accedez a http://localhost:4201" -ForegroundColor White
Write-Host "   - Connectez-vous avec admin/admin" -ForegroundColor White
Write-Host "   - Cliquez sur Planning dans la navigation" -ForegroundColor White
Write-Host "   - Testez le bouton 'Charger les entrainements'" -ForegroundColor White

Write-Host ""
Write-Host "Integration configuree avec succes!" -ForegroundColor Green
Write-Host "Le module Planning est maintenant integre au frontend." -ForegroundColor Cyan

Read-Host "Appuyez sur Entree pour continuer"
