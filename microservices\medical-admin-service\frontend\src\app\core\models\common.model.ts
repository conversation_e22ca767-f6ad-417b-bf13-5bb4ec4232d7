// Modèles communs utilisés dans l'application

export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
  timestamp: string;
}

export interface PagedResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
  numberOfElements: number;
}

export interface ErrorResponse {
  error: string;
  message: string;
  timestamp: string;
  status: number;
  path: string;
}

export interface User {
  id: number;
  nom: string;
  prenom: string;
  email: string;
  role: UserRole;
  actif: boolean;
}

export enum UserRole {
  ADMIN = 'ADMIN',
  COACH = 'COACH',
  JOUEUR = 'JOUEUR',
  STAFF_MEDICAL = 'STAFF_MEDICAL',
  STAFF_TECHNIQUE = 'STAFF_TECHNIQUE'
}

export interface Notification {
  id?: number;
  titre: string;
  message: string;
  type: NotificationType;
  lu: boolean;
  dateCreation: string;
  destinataireId: number;
}

export enum NotificationType {
  INFO = 'INFO',
  WARNING = 'WARNING',
  ERROR = 'ERROR',
  SUCCESS = 'SUCCESS'
}

export interface FilterOptions {
  page?: number;
  size?: number;
  sort?: string;
  direction?: 'ASC' | 'DESC';
}

export interface SearchCriteria extends FilterOptions {
  searchTerm?: string;
  filters?: { [key: string]: any };
}

export interface DashboardStats {
  totalDonneesSante: number;
  totalRendezVous: number;
  totalDemandes: number;
  alertesActives: number;
  rendezVousAujourdhui: number;
  demandesEnAttente: number;
}

export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string[];
    borderColor?: string[];
    borderWidth?: number;
  }[];
}

export interface MenuItem {
  label: string;
  icon: string;
  route?: string;
  children?: MenuItem[];
  badge?: string;
  badgeClass?: string;
}
