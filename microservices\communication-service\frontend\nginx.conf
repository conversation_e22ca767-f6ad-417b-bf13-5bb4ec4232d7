# Configuration Nginx pour le frontend Communication Service
# Optimisée pour les applications Angular avec WebSocket et PWA

# Configuration des événements
events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

# Configuration HTTP
http {
    # Types MIME
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;
    
    # Performance
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 25M;
    
    # Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Sécurité
    server_tokens off;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    
    # Configuration du serveur principal
    server {
        listen 80;
        listen [::]:80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;
        
        # Configuration pour Angular Router
        location / {
            try_files $uri $uri/ /index.html;
            
            # Headers pour PWA
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }
        
        # Cache pour les assets statiques
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary Accept-Encoding;
            
            # Compression spécifique pour les fonts
            location ~* \.(woff|woff2|ttf|eot)$ {
                add_header Access-Control-Allow-Origin *;
            }
        }
        
        # Configuration pour le manifest PWA
        location /manifest.json {
            add_header Content-Type application/manifest+json;
            add_header Cache-Control "public, max-age=86400";
        }
        
        # Configuration pour le service worker
        location /sw.js {
            add_header Content-Type application/javascript;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }
        
        # Proxy pour les API calls
        location /api/ {
            proxy_pass http://communication-backend:8084;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            proxy_read_timeout 86400;
        }
        
        # Configuration spéciale pour WebSocket
        location /ws {
            proxy_pass http://communication-backend:8084;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 86400;
            proxy_send_timeout 86400;
            proxy_connect_timeout 86400;
        }
        
        # Configuration pour les uploads de fichiers
        location /api/fichiers/upload {
            proxy_pass http://communication-backend:8084;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Augmenter les timeouts pour les gros fichiers
            proxy_read_timeout 300;
            proxy_connect_timeout 300;
            proxy_send_timeout 300;
            
            # Taille maximale des uploads
            client_max_body_size 25M;
        }
        
        # Headers de sécurité pour PWA
        location ~* \.(html|js)$ {
            add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://fonts.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' ws: wss:; manifest-src 'self';";
        }
        
        # Gestion des erreurs
        error_page 404 /index.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /50x.html {
            root /usr/share/nginx/html;
        }
        
        # Health check
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # Robots.txt
        location = /robots.txt {
            add_header Content-Type text/plain;
            return 200 "User-agent: *\nDisallow: /api/\nDisallow: /ws\n";
        }
        
        # Favicon
        location = /favicon.ico {
            log_not_found off;
            access_log off;
        }
    }
    
    # Configuration HTTPS (pour production)
    # server {
    #     listen 443 ssl http2;
    #     listen [::]:443 ssl http2;
    #     server_name your-domain.com;
    #     
    #     ssl_certificate /path/to/certificate.crt;
    #     ssl_certificate_key /path/to/private.key;
    #     ssl_protocols TLSv1.2 TLSv1.3;
    #     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    #     ssl_prefer_server_ciphers off;
    #     ssl_session_cache shared:SSL:10m;
    #     ssl_session_timeout 10m;
    #     
    #     # HSTS
    #     add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    #     
    #     # Même configuration que HTTP mais avec HTTPS
    #     root /usr/share/nginx/html;
    #     index index.html;
    #     
    #     # ... (répéter la configuration du serveur HTTP)
    # }
    
    # Redirection HTTP vers HTTPS (pour production)
    # server {
    #     listen 80;
    #     listen [::]:80;
    #     server_name your-domain.com;
    #     return 301 https://$server_name$request_uri;
    # }
}
