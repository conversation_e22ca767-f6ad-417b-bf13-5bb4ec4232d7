{"name": "auth-user-frontend", "version": "1.0.0", "description": "Frontend Angular pour le service d'authentification et gestion des utilisateurs", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/animations": "^17.0.0", "@angular/common": "^17.0.0", "@angular/compiler": "^17.0.0", "@angular/core": "^17.0.0", "@angular/forms": "^17.0.0", "@angular/platform-browser": "^17.0.0", "@angular/platform-browser-dynamic": "^17.0.0", "@angular/router": "^17.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.0"}, "devDependencies": {"@angular-devkit/build-angular": "^17.0.0", "@angular/cli": "^17.0.0", "@angular/compiler-cli": "^17.0.0", "@types/node": "^18.18.0", "typescript": "~5.2.0"}}