# Configuration Finance Service
# Copier ce fichier vers .env et adapter les valeurs

# ===========================================
# CONFIGURATION GÉNÉRALE
# ===========================================
VERSION=latest
ENVIRONMENT=development

# ===========================================
# BASE DE DONNÉES
# ===========================================
POSTGRES_DB=finance_db
POSTGRES_USER=finance_user
POSTGRES_PASSWORD=finance_secure_password_2024
DB_PORT=5437

# ===========================================
# REDIS
# ===========================================
REDIS_PASSWORD=finance_redis_password_2024
REDIS_PORT=6379

# ===========================================
# SÉCURITÉ JWT
# ===========================================
JWT_SECRET=finance-super-secret-key-2024-very-long-and-secure-for-production
JWT_EXPIRATION=86400

# ===========================================
# CONFIGURATION EMAIL
# ===========================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_email_password

# ===========================================
# URLS ET PORTS
# ===========================================
BACKEND_PORT=8085
FRONTEND_PORT=4205
API_URL=http://localhost:8085
CORS_ALLOWED_ORIGINS=http://localhost:4200,http://localhost:4205

# ===========================================
# SSL/TLS (Production)
# ===========================================
SSL_ENABLED=false
SSL_KEYSTORE=/path/to/keystore.p12
SSL_KEYSTORE_PASSWORD=keystore_password

# ===========================================
# MONITORING
# ===========================================
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
GRAFANA_PASSWORD=admin_secure_password

# ===========================================
# NGINX (Production)
# ===========================================
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443

# ===========================================
# CONFIGURATION DÉVELOPPEMENT
# ===========================================
# Activer le mode debug
DEBUG_MODE=true
# Afficher les requêtes SQL
SHOW_SQL=true
# Niveau de log
LOG_LEVEL=DEBUG

# ===========================================
# CONFIGURATION TEST
# ===========================================
TEST_DB_PORT=5438
TEST_BACKEND_PORT=8086
TEST_FRONTEND_PORT=4206

# ===========================================
# CONFIGURATION PRODUCTION
# ===========================================
# Optimisations JVM
JAVA_OPTS=-Xmx1G -Xms512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200

# Pool de connexions
DB_POOL_SIZE=20
DB_MIN_IDLE=5
DB_CONNECTION_TIMEOUT=30000

# Sécurité
MANAGEMENT_ENDPOINTS_EXPOSURE=health,metrics,info
HEALTH_SHOW_DETAILS=when_authorized

# ===========================================
# SAUVEGARDE
# ===========================================
BACKUP_RETENTION_DAYS=7
BACKUP_SCHEDULE=0 2 * * *

# ===========================================
# VARIABLES SPÉCIFIQUES À L'ENVIRONNEMENT
# ===========================================

# Développement
DEV_API_URL=http://localhost:8085
DEV_FRONTEND_URL=http://localhost:4205

# Test
TEST_API_URL=http://localhost:8086
TEST_FRONTEND_URL=http://localhost:4206

# Production
PROD_API_URL=https://api.sprintbot.com/finance
PROD_FRONTEND_URL=https://finance.sprintbot.com

# ===========================================
# CONFIGURATION DOCKER
# ===========================================
COMPOSE_PROJECT_NAME=sprintbot-finance
COMPOSE_FILE=docker-compose.yml

# ===========================================
# VARIABLES OPTIONNELLES
# ===========================================

# Intégration avec d'autres services
AUTH_SERVICE_URL=http://auth-user-backend:8081
PLANNING_SERVICE_URL=http://planning-performance-backend:8082
MEDICAL_SERVICE_URL=http://medical-admin-backend:8083
COMMUNICATION_SERVICE_URL=http://communication-backend:8084

# Configuration des rapports
REPORTS_STORAGE_PATH=/app/reports
UPLOADS_STORAGE_PATH=/app/uploads
MAX_FILE_SIZE=10MB

# Configuration des notifications
NOTIFICATION_EMAIL_ENABLED=true
NOTIFICATION_SMS_ENABLED=false
NOTIFICATION_PUSH_ENABLED=true

# Configuration des alertes financières
BUDGET_ALERT_THRESHOLD=80
TRANSACTION_VALIDATION_REQUIRED=true
AUTOMATIC_BACKUP_ENABLED=true

# Configuration de la performance
CACHE_TTL=3600
PAGINATION_DEFAULT_SIZE=20
PAGINATION_MAX_SIZE=100

# Configuration de l'audit
AUDIT_ENABLED=true
AUDIT_RETENTION_DAYS=365
SECURITY_LOG_ENABLED=true

# ===========================================
# NOTES D'UTILISATION
# ===========================================

# 1. Copier ce fichier vers .env :
#    cp .env.example .env

# 2. Modifier les valeurs selon votre environnement

# 3. Pour la production, utiliser des mots de passe forts :
#    - POSTGRES_PASSWORD : minimum 16 caractères
#    - JWT_SECRET : minimum 32 caractères
#    - REDIS_PASSWORD : minimum 16 caractères

# 4. Variables obligatoires pour la production :
#    - POSTGRES_PASSWORD
#    - JWT_SECRET
#    - REDIS_PASSWORD
#    - SMTP_PASSWORD (si email activé)

# 5. Variables optionnelles mais recommandées :
#    - SSL_ENABLED=true (production)
#    - GRAFANA_PASSWORD (monitoring)
#    - BACKUP_RETENTION_DAYS (sauvegarde)

# 6. Pour les tests d'intégration :
#    docker-compose -f docker-compose.test.yml up

# 7. Pour la production :
#    docker-compose -f docker-compose.prod.yml up -d
