#!/bin/bash

# Tests d'intégration pour Finance Service
# Teste les APIs et fonctionnalités du microservice

set -e

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKEND_URL="http://localhost:8085"
FRONTEND_URL="http://localhost:4205"
TIMEOUT=30
STOP_ON_ERROR=false

# Fonctions de logging
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_section() {
    echo -e "\n${BLUE}$1${NC}"
}

# Fonction utilitaire pour les requêtes HTTP
http_request() {
    local method=$1
    local url=$2
    local data=$3
    local expected_status=$4
    
    local response
    local status_code
    
    if [ -n "$data" ]; then
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$url" 2>/dev/null || echo -e "\n000")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            "$url" 2>/dev/null || echo -e "\n000")
    fi
    
    status_code=$(echo "$response" | tail -n1)
    
    if [ "$status_code" = "$expected_status" ]; then
        return 0
    else
        log_error "Statut attendu: $expected_status, reçu: $status_code"
        return 1
    fi
}

# Test de connectivité
test_connectivity() {
    log_section "=== Test de connectivité ==="
    
    # Test backend
    log_info "Test de connectivité backend ($BACKEND_URL)..."
    if http_request "GET" "$BACKEND_URL/actuator/health" "" "200"; then
        log_success "✓ Backend accessible"
    else
        log_error "✗ Backend non accessible"
        return 1
    fi
    
    # Test frontend
    log_info "Test de connectivité frontend ($FRONTEND_URL)..."
    if curl -s --max-time 5 "$FRONTEND_URL" > /dev/null; then
        log_success "✓ Frontend accessible"
    else
        log_warning "⚠ Frontend non accessible (normal si pas démarré)"
    fi
    
    return 0
}

# Test des APIs de budget
test_budget_api() {
    log_section "=== Test API Budget ==="
    
    # Test GET all
    log_info "Test récupération de tous les budgets..."
    if http_request "GET" "$BACKEND_URL/api/budgets" "" "200"; then
        log_success "✓ GET /api/budgets"
    fi
    
    # Test POST create
    log_info "Test création d'un nouveau budget..."
    data='{
        "nom": "Budget Test Intégration",
        "description": "Budget créé pour les tests automatisés",
        "montantTotal": 10000.00,
        "periode": "ANNUEL",
        "dateDebut": "2024-01-01",
        "dateFin": "2024-12-31",
        "statut": "ACTIF",
        "seuilAlerte": 80.0,
        "responsableId": 1
    }'
    
    local response
    if response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$data" \
        "$BACKEND_URL/api/budgets" 2>/dev/null); then
        
        local budget_id=$(echo "$response" | grep -o '"id":[0-9]*' | cut -d':' -f2)
        if [ -n "$budget_id" ]; then
            log_success "✓ POST /api/budgets (ID: $budget_id)"
            
            # Test GET by ID
            log_info "Test récupération du budget créé..."
            if http_request "GET" "$BACKEND_URL/api/budgets/$budget_id" "" "200"; then
                log_success "✓ GET /api/budgets/$budget_id"
            fi
            
            # Test PUT update
            log_info "Test mise à jour du budget..."
            update_data='{"nom": "Budget Test Modifié", "description": "Budget modifié par test"}'
            if http_request "PUT" "$BACKEND_URL/api/budgets/$budget_id" "$update_data" "200"; then
                log_success "✓ PUT /api/budgets/$budget_id"
            fi
        fi
    fi
    
    return 0
}

# Test des APIs de transaction
test_transaction_api() {
    log_section "=== Test API Transaction ==="
    
    # Test GET all
    log_info "Test récupération de toutes les transactions..."
    if http_request "GET" "$BACKEND_URL/api/transactions" "" "200"; then
        log_success "✓ GET /api/transactions"
    fi
    
    # Test POST create
    log_info "Test création d'une nouvelle transaction..."
    data='{
        "reference": "TEST-INT-001",
        "description": "Transaction de test d'\''intégration",
        "montant": 500.00,
        "typeTransaction": "DEPENSE",
        "statut": "EN_ATTENTE",
        "modePaiement": "CARTE",
        "categorieTransactionId": 1,
        "utilisateurId": 1
    }'
    
    local response
    if response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$data" \
        "$BACKEND_URL/api/transactions" 2>/dev/null); then
        
        local transaction_id=$(echo "$response" | grep -o '"id":[0-9]*' | cut -d':' -f2)
        if [ -n "$transaction_id" ]; then
            log_success "✓ POST /api/transactions (ID: $transaction_id)"
            
            # Test validation
            log_info "Test validation de la transaction..."
            validation_data='{"validation": true, "commentaire": "Test automatisé"}'
            if http_request "PUT" "$BACKEND_URL/api/transactions/$transaction_id/validation" "$validation_data" "200"; then
                log_success "✓ PUT /api/transactions/$transaction_id/validation"
            fi
        fi
    fi
    
    return 0
}

# Test des APIs de sponsor
test_sponsor_api() {
    log_section "=== Test API Sponsor ==="
    
    # Test GET all
    log_info "Test récupération de tous les sponsors..."
    if http_request "GET" "$BACKEND_URL/api/sponsors" "" "200"; then
        log_success "✓ GET /api/sponsors"
    fi
    
    # Test POST create
    log_info "Test création d'un nouveau sponsor..."
    data='{
        "nom": "Sponsor Test",
        "typePartenaire": "SPONSOR_PRINCIPAL",
        "secteurActivite": "TECHNOLOGIE",
        "montantContrat": 15000.00,
        "dateDebutContrat": "2024-01-01",
        "dateFinContrat": "2024-12-31",
        "statut": "ACTIF",
        "contactPrincipal": "<EMAIL>",
        "telephone": "+33123456789",
        "adresse": "123 Rue du Test, 75001 Paris"
    }'
    
    local response
    if response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$data" \
        "$BACKEND_URL/api/sponsors" 2>/dev/null); then
        
        local sponsor_id=$(echo "$response" | grep -o '"id":[0-9]*' | cut -d':' -f2)
        if [ -n "$sponsor_id" ]; then
            log_success "✓ POST /api/sponsors (ID: $sponsor_id)"
        fi
    fi
    
    return 0
}

# Test des APIs de salaire
test_salaire_api() {
    log_section "=== Test API Salaire ==="
    
    # Test GET all
    log_info "Test récupération de tous les salaires..."
    if http_request "GET" "$BACKEND_URL/api/salaires" "" "200"; then
        log_success "✓ GET /api/salaires"
    fi
    
    # Test statistiques
    log_info "Test récupération des statistiques de salaires..."
    if http_request "GET" "$BACKEND_URL/api/salaires/stats" "" "200"; then
        log_success "✓ GET /api/salaires/stats"
    fi
    
    return 0
}

# Test des fonctionnalités avancées
test_advanced_features() {
    log_section "=== Test des fonctionnalités avancées ==="
    
    # Test rapports
    log_info "Test génération de rapport financier..."
    if http_request "GET" "$BACKEND_URL/api/rapports/dashboard" "" "200"; then
        log_success "✓ GET /api/rapports/dashboard"
    fi
    
    # Test statistiques globales
    log_info "Test récupération des statistiques globales..."
    if http_request "GET" "$BACKEND_URL/api/rapports/stats-globales" "" "200"; then
        log_success "✓ GET /api/rapports/stats-globales"
    fi
    
    return 0
}

# Test de gestion d'erreurs
test_error_handling() {
    log_section "=== Test de gestion d'erreurs ==="
    
    # Test 404
    log_info "Test ressource inexistante..."
    if http_request "GET" "$BACKEND_URL/api/budgets/99999" "" "404"; then
        log_success "✓ Gestion 404 correcte"
    fi
    
    # Test validation
    log_info "Test validation des données..."
    invalid_data='{"nom": "", "montantTotal": -100}'
    if http_request "POST" "$BACKEND_URL/api/budgets" "$invalid_data" "400"; then
        log_success "✓ Validation des données correcte"
    fi
    
    return 0
}

# Test de performance
test_performance() {
    log_section "=== Test de performance ==="
    
    log_info "Test de temps de réponse des APIs..."
    
    local start_time=$(date +%s%N)
    http_request "GET" "$BACKEND_URL/api/budgets" "" "200" > /dev/null
    local end_time=$(date +%s%N)
    
    local duration=$(( (end_time - start_time) / 1000000 ))
    log_info "Temps de réponse GET /api/budgets: ${duration}ms"
    
    if [ $duration -lt 1000 ]; then
        log_success "✓ Performance acceptable (<1s)"
    else
        log_warning "⚠ Performance lente (>1s)"
    fi
    
    return 0
}

# Fonction principale
main() {
    log_info "=== Début des tests d'intégration Finance Service ==="
    echo ""
    
    local total_tests=0
    local passed_tests=0
    
    # Exécution des tests
    tests=(
        "test_connectivity"
        "test_budget_api"
        "test_transaction_api"
        "test_sponsor_api"
        "test_salaire_api"
        "test_advanced_features"
        "test_error_handling"
        "test_performance"
    )
    
    for test in "${tests[@]}"; do
        total_tests=$((total_tests + 1))
        echo ""
        if $test; then
            passed_tests=$((passed_tests + 1))
        else
            log_error "Test $test échoué"
            if [ "$STOP_ON_ERROR" = true ]; then
                break
            fi
        fi
    done
    
    # Résumé final
    echo ""
    log_section "=== Résumé des tests ==="
    log_info "Tests exécutés: $total_tests"
    log_info "Tests réussis: $passed_tests"
    log_info "Tests échoués: $((total_tests - passed_tests))"
    
    if [ $passed_tests -eq $total_tests ]; then
        log_success "🎉 Tous les tests d'intégration ont réussi!"
        exit 0
    else
        log_error "❌ Certains tests ont échoué"
        exit 1
    fi
}

# Gestion des arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --backend-url)
            BACKEND_URL="$2"
            shift 2
            ;;
        --frontend-url)
            FRONTEND_URL="$2"
            shift 2
            ;;
        --timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        --stop-on-error)
            STOP_ON_ERROR=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --backend-url URL    URL du backend (défaut: $BACKEND_URL)"
            echo "  --frontend-url URL   URL du frontend (défaut: $FRONTEND_URL)"
            echo "  --timeout SECONDS    Timeout des requêtes (défaut: $TIMEOUT)"
            echo "  --stop-on-error      Arrêter au premier échec"
            echo "  -h, --help           Afficher cette aide"
            exit 0
            ;;
        *)
            log_error "Option inconnue: $1"
            exit 1
            ;;
    esac
done

# Exécution
main
