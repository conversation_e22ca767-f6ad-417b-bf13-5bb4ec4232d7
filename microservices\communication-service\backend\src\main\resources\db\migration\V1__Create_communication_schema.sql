-- Migration V1: Création du schéma de base pour le service de communication
-- Auteur: SprintBot Team
-- Date: 2024-01-15

-- Création du schéma dédié
CREATE SCHEMA IF NOT EXISTS communication;

-- Table des conversations
CREATE TABLE communication.conversations (
    id BIGSERIAL PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL CHECK (type IN ('PRIVE', 'GROUPE', 'EQUIPE', 'CANAL')),
    createur_id BIGINT NOT NULL,
    est_archive BOOLEAN DEFAULT FALSE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des participants aux conversations
CREATE TABLE communication.participants_conversation (
    id BIGSERIAL PRIMARY KEY,
    conversation_id BIGINT NOT NULL REFERENCES communication.conversations(id) ON DELETE CASCADE,
    utilisateur_id BIGINT NOT NULL,
    role VARCHAR(50) NOT NULL CHECK (role IN ('MEMBRE', 'ADMIN', 'MODERATEUR')),
    date_ajout TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_derniere_lecture TIMESTAMP,
    notifications_actives BOOLEAN DEFAULT TRUE,
    est_epingle BOOLEAN DEFAULT FALSE,
    UNIQUE(conversation_id, utilisateur_id)
);

-- Table des messages
CREATE TABLE communication.messages (
    id BIGSERIAL PRIMARY KEY,
    conversation_id BIGINT NOT NULL REFERENCES communication.conversations(id) ON DELETE CASCADE,
    expediteur_id BIGINT NOT NULL,
    contenu TEXT NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('TEXTE', 'IMAGE', 'VIDEO', 'AUDIO', 'FICHIER', 'SYSTEME')),
    url_fichier VARCHAR(500),
    message_parent_id BIGINT REFERENCES communication.messages(id) ON DELETE SET NULL,
    est_modifie BOOLEAN DEFAULT FALSE,
    est_supprime BOOLEAN DEFAULT FALSE,
    est_epingle BOOLEAN DEFAULT FALSE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des réactions aux messages
CREATE TABLE communication.reactions_message (
    id BIGSERIAL PRIMARY KEY,
    message_id BIGINT NOT NULL REFERENCES communication.messages(id) ON DELETE CASCADE,
    utilisateur_id BIGINT NOT NULL,
    emoji VARCHAR(10) NOT NULL,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(message_id, utilisateur_id, emoji)
);

-- Table des lectures de messages
CREATE TABLE communication.lectures_message (
    id BIGSERIAL PRIMARY KEY,
    message_id BIGINT NOT NULL REFERENCES communication.messages(id) ON DELETE CASCADE,
    utilisateur_id BIGINT NOT NULL,
    date_lecture TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(message_id, utilisateur_id)
);

-- Table des notifications
CREATE TABLE communication.notifications (
    id BIGSERIAL PRIMARY KEY,
    destinataire_id BIGINT NOT NULL,
    type VARCHAR(100) NOT NULL CHECK (type IN (
        'NOUVEAU_MESSAGE', 'NOUVELLE_CONVERSATION', 'AJOUT_CONVERSATION', 
        'SUPPRESSION_CONVERSATION', 'ESCALADE_CHATBOT', 'SYSTEME'
    )),
    canal VARCHAR(50) NOT NULL CHECK (canal IN ('PUSH', 'EMAIL', 'SMS', 'INTERNE')),
    titre VARCHAR(255) NOT NULL,
    contenu TEXT NOT NULL,
    priorite VARCHAR(50) DEFAULT 'NORMALE' CHECK (priorite IN ('BASSE', 'NORMALE', 'HAUTE', 'URGENTE')),
    statut VARCHAR(50) DEFAULT 'EN_ATTENTE' CHECK (statut IN (
        'EN_ATTENTE', 'EN_COURS', 'ENVOYE', 'LU', 'ERREUR', 'EXPIRE'
    )),
    date_programmee TIMESTAMP,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_envoi TIMESTAMP,
    date_lecture TIMESTAMP,
    nombre_tentatives INTEGER DEFAULT 0,
    message_erreur TEXT,
    donnees JSONB
);

-- Table des préférences de notification
CREATE TABLE communication.preferences_notification (
    id BIGSERIAL PRIMARY KEY,
    utilisateur_id BIGINT NOT NULL,
    type_notification VARCHAR(100) NOT NULL,
    canal VARCHAR(50) NOT NULL,
    active BOOLEAN DEFAULT TRUE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(utilisateur_id, type_notification, canal)
);

-- Table des conversations chatbot
CREATE TABLE communication.conversations_chatbot (
    id BIGSERIAL PRIMARY KEY,
    utilisateur_id BIGINT NOT NULL,
    session_id VARCHAR(255) NOT NULL UNIQUE,
    langue VARCHAR(10) DEFAULT 'fr',
    statut VARCHAR(50) DEFAULT 'ACTIF' CHECK (statut IN ('ACTIF', 'TERMINE', 'ESCALADE')),
    nombre_messages INTEGER DEFAULT 0,
    note_satisfaction INTEGER CHECK (note_satisfaction BETWEEN 1 AND 5),
    commentaire_satisfaction TEXT,
    raison_escalade TEXT,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_fin TIMESTAMP
);

-- Table des messages chatbot
CREATE TABLE communication.messages_chatbot (
    id BIGSERIAL PRIMARY KEY,
    conversation_id BIGINT NOT NULL REFERENCES communication.conversations_chatbot(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL CHECK (type IN ('UTILISATEUR', 'BOT')),
    contenu TEXT NOT NULL,
    intention_detectee VARCHAR(100),
    modele_utilise VARCHAR(100),
    confiance_score DECIMAL(3,2),
    temps_reponse BIGINT,
    tokens_utilises INTEGER,
    cout_estimation DECIMAL(10,6),
    est_utile BOOLEAN,
    feedback_utilisateur TEXT,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table de présence des utilisateurs
CREATE TABLE communication.presence_utilisateur (
    id BIGSERIAL PRIMARY KEY,
    utilisateur_id BIGINT NOT NULL UNIQUE,
    statut VARCHAR(50) NOT NULL CHECK (statut IN ('EN_LIGNE', 'ABSENT', 'OCCUPE', 'HORS_LIGNE')),
    message_statut VARCHAR(255),
    session_id VARCHAR(255),
    plateforme VARCHAR(50),
    est_mobile BOOLEAN DEFAULT FALSE,
    invisible BOOLEAN DEFAULT FALSE,
    derniere_activite TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Index pour optimiser les performances
CREATE INDEX idx_conversations_createur ON communication.conversations(createur_id);
CREATE INDEX idx_conversations_type ON communication.conversations(type);
CREATE INDEX idx_conversations_date_creation ON communication.conversations(date_creation);

CREATE INDEX idx_participants_conversation ON communication.participants_conversation(conversation_id);
CREATE INDEX idx_participants_utilisateur ON communication.participants_conversation(utilisateur_id);
CREATE INDEX idx_participants_role ON communication.participants_conversation(role);

CREATE INDEX idx_messages_conversation ON communication.messages(conversation_id);
CREATE INDEX idx_messages_expediteur ON communication.messages(expediteur_id);
CREATE INDEX idx_messages_type ON communication.messages(type);
CREATE INDEX idx_messages_date_creation ON communication.messages(date_creation);
CREATE INDEX idx_messages_parent ON communication.messages(message_parent_id);

CREATE INDEX idx_reactions_message ON communication.reactions_message(message_id);
CREATE INDEX idx_reactions_utilisateur ON communication.reactions_message(utilisateur_id);

CREATE INDEX idx_lectures_message ON communication.lectures_message(message_id);
CREATE INDEX idx_lectures_utilisateur ON communication.lectures_message(utilisateur_id);

CREATE INDEX idx_notifications_destinataire ON communication.notifications(destinataire_id);
CREATE INDEX idx_notifications_type ON communication.notifications(type);
CREATE INDEX idx_notifications_statut ON communication.notifications(statut);
CREATE INDEX idx_notifications_date_programmee ON communication.notifications(date_programmee);
CREATE INDEX idx_notifications_date_creation ON communication.notifications(date_creation);

CREATE INDEX idx_preferences_utilisateur ON communication.preferences_notification(utilisateur_id);

CREATE INDEX idx_chatbot_conversations_utilisateur ON communication.conversations_chatbot(utilisateur_id);
CREATE INDEX idx_chatbot_conversations_session ON communication.conversations_chatbot(session_id);
CREATE INDEX ******************************** ON communication.conversations_chatbot(statut);

CREATE INDEX idx_chatbot_messages_conversation ON communication.messages_chatbot(conversation_id);
CREATE INDEX idx_chatbot_messages_type ON communication.messages_chatbot(type);
CREATE INDEX idx_chatbot_messages_date_creation ON communication.messages_chatbot(date_creation);

CREATE INDEX idx_presence_utilisateur ON communication.presence_utilisateur(utilisateur_id);
CREATE INDEX idx_presence_statut ON communication.presence_utilisateur(statut);
CREATE INDEX idx_presence_session ON communication.presence_utilisateur(session_id);
CREATE INDEX idx_presence_derniere_activite ON communication.presence_utilisateur(derniere_activite);

-- Triggers pour mettre à jour automatiquement date_modification
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.date_modification = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_conversations_modtime 
    BEFORE UPDATE ON communication.conversations 
    FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_participants_modtime 
    BEFORE UPDATE ON communication.participants_conversation 
    FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_messages_modtime 
    BEFORE UPDATE ON communication.messages 
    FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_preferences_modtime 
    BEFORE UPDATE ON communication.preferences_notification 
    FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_presence_modtime 
    BEFORE UPDATE ON communication.presence_utilisateur 
    FOR EACH ROW EXECUTE FUNCTION update_modified_column();

-- Commentaires sur les tables
COMMENT ON SCHEMA communication IS 'Schéma pour le microservice de communication';
COMMENT ON TABLE communication.conversations IS 'Table des conversations (privées, groupes, équipes, canaux)';
COMMENT ON TABLE communication.participants_conversation IS 'Table des participants aux conversations avec leurs rôles';
COMMENT ON TABLE communication.messages IS 'Table des messages avec support pour différents types de contenu';
COMMENT ON TABLE communication.reactions_message IS 'Table des réactions emoji aux messages';
COMMENT ON TABLE communication.lectures_message IS 'Table de suivi des lectures de messages';
COMMENT ON TABLE communication.notifications IS 'Table des notifications système avec support multi-canal';
COMMENT ON TABLE communication.preferences_notification IS 'Table des préférences de notification par utilisateur';
COMMENT ON TABLE communication.conversations_chatbot IS 'Table des conversations avec le chatbot IA';
COMMENT ON TABLE communication.messages_chatbot IS 'Table des messages du chatbot avec métriques IA';
COMMENT ON TABLE communication.presence_utilisateur IS 'Table de présence en temps réel des utilisateurs';
