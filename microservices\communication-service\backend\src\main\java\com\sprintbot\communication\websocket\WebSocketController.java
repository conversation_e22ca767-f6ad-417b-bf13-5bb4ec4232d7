package com.sprintbot.communication.websocket;

import com.sprintbot.communication.entity.Message;
import com.sprintbot.communication.service.MessageService;
import com.sprintbot.communication.service.UserPresenceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Controller;

import java.util.Map;

/**
 * Contrôleur WebSocket pour la gestion des messages en temps réel
 */
@Controller
public class WebSocketController {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketController.class);

    @Autowired
    private MessageService messageService;

    @Autowired
    private UserPresenceService presenceService;

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @Autowired
    private WebSocketEventHandler webSocketEventHandler;

    /**
     * Envoie un message via WebSocket
     */
    @MessageMapping("/conversation/{conversationId}/message")
    public void envoyerMessage(
            @DestinationVariable Long conversationId,
            @Payload MessageWebSocketRequest request,
            SimpMessageHeaderAccessor headerAccessor) {
        try {
            String sessionId = headerAccessor.getSessionId();
            Long utilisateurId = webSocketEventHandler.getUtilisateurParSession(sessionId);

            if (utilisateurId == null) {
                logger.warn("Tentative d'envoi de message sans utilisateur authentifié - Session: {}", sessionId);
                return;
            }

            // Mettre à jour l'activité de l'utilisateur
            presenceService.mettreAJourActivite(utilisateurId);

            // Envoyer le message
            Message message;
            if (request.messageParentId() != null) {
                message = messageService.envoyerReponse(
                        conversationId,
                        utilisateurId,
                        request.contenu(),
                        request.messageParentId()
                );
            } else {
                message = messageService.envoyerMessage(
                        conversationId,
                        utilisateurId,
                        request.contenu()
                );
            }

            // Le message sera automatiquement diffusé par le MessageService
            logger.debug("Message envoyé via WebSocket: {} dans conversation {}", message.getId(), conversationId);

        } catch (Exception e) {
            logger.error("Erreur lors de l'envoi de message WebSocket: {}", e.getMessage(), e);
            
            // Envoyer une erreur à l'expéditeur
            String destination = "/queue/errors/" + headerAccessor.getSessionId();
            messagingTemplate.convertAndSend(destination, Map.of(
                    "erreur", e.getMessage(),
                    "type", "MESSAGE_ERROR"
            ));
        }
    }

    /**
     * Indique qu'un utilisateur est en train de taper
     */
    @MessageMapping("/conversation/{conversationId}/typing")
    public void indiquerEnTrainDeTaper(
            @DestinationVariable Long conversationId,
            @Payload TypingRequest request,
            SimpMessageHeaderAccessor headerAccessor) {
        try {
            String sessionId = headerAccessor.getSessionId();
            Long utilisateurId = webSocketEventHandler.getUtilisateurParSession(sessionId);

            if (utilisateurId == null) {
                return;
            }

            // Mettre à jour l'activité
            presenceService.mettreAJourActivite(utilisateurId);

            // Diffuser l'indication de frappe aux autres participants
            String destination = "/topic/conversation/" + conversationId + "/typing";
            Map<String, Object> typingData = Map.of(
                    "utilisateurId", utilisateurId,
                    "enTrainDeTaper", request.enTrainDeTaper()
            );

            messagingTemplate.convertAndSend(destination, typingData);

        } catch (Exception e) {
            logger.error("Erreur lors de l'indication de frappe: {}", e.getMessage(), e);
        }
    }

    /**
     * Marque un message comme lu via WebSocket
     */
    @MessageMapping("/message/{messageId}/lu")
    public void marquerMessageLu(
            @DestinationVariable Long messageId,
            SimpMessageHeaderAccessor headerAccessor) {
        try {
            String sessionId = headerAccessor.getSessionId();
            Long utilisateurId = webSocketEventHandler.getUtilisateurParSession(sessionId);

            if (utilisateurId == null) {
                return;
            }

            // Marquer comme lu
            messageService.marquerCommeLu(messageId, utilisateurId);

            // Mettre à jour l'activité
            presenceService.mettreAJourActivite(utilisateurId);

        } catch (Exception e) {
            logger.error("Erreur lors du marquage comme lu via WebSocket: {}", e.getMessage(), e);
        }
    }

    /**
     * Ajoute une réaction à un message via WebSocket
     */
    @MessageMapping("/message/{messageId}/reaction")
    public void ajouterReaction(
            @DestinationVariable Long messageId,
            @Payload ReactionRequest request,
            SimpMessageHeaderAccessor headerAccessor) {
        try {
            String sessionId = headerAccessor.getSessionId();
            Long utilisateurId = webSocketEventHandler.getUtilisateurParSession(sessionId);

            if (utilisateurId == null) {
                return;
            }

            // Ajouter la réaction
            messageService.ajouterReaction(messageId, utilisateurId, request.emoji());

            // Mettre à jour l'activité
            presenceService.mettreAJourActivite(utilisateurId);

        } catch (Exception e) {
            logger.error("Erreur lors de l'ajout de réaction via WebSocket: {}", e.getMessage(), e);
        }
    }

    /**
     * Ping pour maintenir la connexion active
     */
    @MessageMapping("/ping")
    @SendTo("/topic/pong")
    public Map<String, Object> ping(SimpMessageHeaderAccessor headerAccessor) {
        try {
            String sessionId = headerAccessor.getSessionId();
            Long utilisateurId = webSocketEventHandler.getUtilisateurParSession(sessionId);

            if (utilisateurId != null) {
                // Mettre à jour l'activité
                presenceService.mettreAJourActivite(utilisateurId);
            }

            return Map.of(
                    "timestamp", System.currentTimeMillis(),
                    "sessionId", sessionId
            );

        } catch (Exception e) {
            logger.error("Erreur lors du ping: {}", e.getMessage(), e);
            return Map.of("erreur", e.getMessage());
        }
    }

    /**
     * Rejoint une conversation (pour recevoir les messages)
     */
    @MessageMapping("/conversation/{conversationId}/join")
    public void rejoindreConversation(
            @DestinationVariable Long conversationId,
            SimpMessageHeaderAccessor headerAccessor) {
        try {
            String sessionId = headerAccessor.getSessionId();
            Long utilisateurId = webSocketEventHandler.getUtilisateurParSession(sessionId);

            if (utilisateurId == null) {
                return;
            }

            // Vérifier que l'utilisateur peut accéder à cette conversation
            // TODO: Ajouter une vérification des permissions

            // Mettre à jour l'activité
            presenceService.mettreAJourActivite(utilisateurId);

            logger.debug("Utilisateur {} a rejoint la conversation {} via WebSocket", utilisateurId, conversationId);

        } catch (Exception e) {
            logger.error("Erreur lors de la jonction à la conversation: {}", e.getMessage(), e);
        }
    }

    /**
     * Quitte une conversation
     */
    @MessageMapping("/conversation/{conversationId}/leave")
    public void quitterConversation(
            @DestinationVariable Long conversationId,
            SimpMessageHeaderAccessor headerAccessor) {
        try {
            String sessionId = headerAccessor.getSessionId();
            Long utilisateurId = webSocketEventHandler.getUtilisateurParSession(sessionId);

            if (utilisateurId == null) {
                return;
            }

            logger.debug("Utilisateur {} a quitté la conversation {} via WebSocket", utilisateurId, conversationId);

        } catch (Exception e) {
            logger.error("Erreur lors de la sortie de conversation: {}", e.getMessage(), e);
        }
    }

    // Classes pour les requêtes WebSocket
    public static class MessageWebSocketRequest {
        private String contenu;
        private Long messageParentId;

        public String contenu() { return contenu; }
        public Long messageParentId() { return messageParentId; }

        public void setContenu(String contenu) { this.contenu = contenu; }
        public void setMessageParentId(Long messageParentId) { this.messageParentId = messageParentId; }
    }

    public static class TypingRequest {
        private boolean enTrainDeTaper;

        public boolean enTrainDeTaper() { return enTrainDeTaper; }
        public void setEnTrainDeTaper(boolean enTrainDeTaper) { this.enTrainDeTaper = enTrainDeTaper; }
    }

    public static class ReactionRequest {
        private String emoji;

        public String emoji() { return emoji; }
        public void setEmoji(String emoji) { this.emoji = emoji; }
    }
}
