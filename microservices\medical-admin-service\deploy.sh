#!/bin/bash

# Script de déploiement pour Medical Admin Service
# Usage: ./deploy.sh [dev|prod] [--rebuild]

set -e

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVICE_NAME="medical-admin-service"
COMPOSE_FILE="docker-compose.yml"
ENV=${1:-dev}
REBUILD=${2:-false}

# Fonctions utilitaires
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérification des prérequis
check_prerequisites() {
    log_info "Vérification des prérequis..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker n'est pas installé"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose n'est pas installé"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker n'est pas démarré"
        exit 1
    fi
    
    log_success "Prérequis validés"
}

# Création du réseau externe si nécessaire
create_network() {
    log_info "Vérification du réseau sprintbot-network..."
    
    if ! docker network ls | grep -q "sprintbot-network"; then
        log_info "Création du réseau sprintbot-network..."
        docker network create sprintbot-network
        log_success "Réseau sprintbot-network créé"
    else
        log_info "Réseau sprintbot-network existe déjà"
    fi
}

# Arrêt des services existants
stop_services() {
    log_info "Arrêt des services existants..."
    docker-compose -f $COMPOSE_FILE down --remove-orphans
    log_success "Services arrêtés"
}

# Construction des images
build_images() {
    if [ "$REBUILD" = "--rebuild" ] || [ "$REBUILD" = "true" ]; then
        log_info "Construction des images Docker..."
        docker-compose -f $COMPOSE_FILE build --no-cache
        log_success "Images construites"
    else
        log_info "Construction des images Docker (avec cache)..."
        docker-compose -f $COMPOSE_FILE build
        log_success "Images construites"
    fi
}

# Démarrage des services
start_services() {
    log_info "Démarrage des services..."
    
    # Démarrer la base de données en premier
    log_info "Démarrage de la base de données..."
    docker-compose -f $COMPOSE_FILE up -d medical-admin-db medical-admin-redis
    
    # Attendre que la base soit prête
    log_info "Attente de la disponibilité de la base de données..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if docker-compose -f $COMPOSE_FILE exec -T medical-admin-db pg_isready -U medical_admin_user -d medical_admin_db &> /dev/null; then
            log_success "Base de données prête"
            break
        fi
        sleep 2
        timeout=$((timeout - 2))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "Timeout: La base de données n'est pas prête"
        exit 1
    fi
    
    # Démarrer le backend
    log_info "Démarrage du backend..."
    docker-compose -f $COMPOSE_FILE up -d medical-admin-backend
    
    # Attendre que le backend soit prêt
    log_info "Attente de la disponibilité du backend..."
    timeout=120
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:8083/actuator/health &> /dev/null; then
            log_success "Backend prêt"
            break
        fi
        sleep 3
        timeout=$((timeout - 3))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "Timeout: Le backend n'est pas prêt"
        exit 1
    fi
    
    # Démarrer le frontend
    log_info "Démarrage du frontend..."
    docker-compose -f $COMPOSE_FILE up -d medical-admin-frontend
    
    log_success "Tous les services sont démarrés"
}

# Vérification de l'état des services
check_services() {
    log_info "Vérification de l'état des services..."
    
    # Vérifier la base de données
    if docker-compose -f $COMPOSE_FILE ps medical-admin-db | grep -q "Up"; then
        log_success "✓ Base de données: En cours d'exécution"
    else
        log_error "✗ Base de données: Arrêtée"
    fi
    
    # Vérifier le backend
    if docker-compose -f $COMPOSE_FILE ps medical-admin-backend | grep -q "Up"; then
        if curl -f http://localhost:8083/actuator/health &> /dev/null; then
            log_success "✓ Backend: En cours d'exécution et accessible"
        else
            log_warning "⚠ Backend: En cours d'exécution mais non accessible"
        fi
    else
        log_error "✗ Backend: Arrêté"
    fi
    
    # Vérifier le frontend
    if docker-compose -f $COMPOSE_FILE ps medical-admin-frontend | grep -q "Up"; then
        if curl -f http://localhost:4203/health &> /dev/null; then
            log_success "✓ Frontend: En cours d'exécution et accessible"
        else
            log_warning "⚠ Frontend: En cours d'exécution mais non accessible"
        fi
    else
        log_error "✗ Frontend: Arrêté"
    fi
    
    # Vérifier Redis
    if docker-compose -f $COMPOSE_FILE ps medical-admin-redis | grep -q "Up"; then
        log_success "✓ Redis: En cours d'exécution"
    else
        log_warning "⚠ Redis: Arrêté"
    fi
}

# Affichage des logs
show_logs() {
    log_info "Affichage des logs récents..."
    docker-compose -f $COMPOSE_FILE logs --tail=50
}

# Nettoyage
cleanup() {
    log_info "Nettoyage des ressources inutilisées..."
    docker system prune -f
    log_success "Nettoyage terminé"
}

# Affichage de l'aide
show_help() {
    echo "Usage: $0 [ENVIRONMENT] [OPTIONS]"
    echo ""
    echo "ENVIRONMENT:"
    echo "  dev     Déploiement en environnement de développement (défaut)"
    echo "  prod    Déploiement en environnement de production"
    echo ""
    echo "OPTIONS:"
    echo "  --rebuild    Force la reconstruction des images Docker"
    echo "  --help       Affiche cette aide"
    echo ""
    echo "Exemples:"
    echo "  $0                    # Déploiement dev avec cache"
    echo "  $0 dev --rebuild      # Déploiement dev avec reconstruction"
    echo "  $0 prod               # Déploiement production"
}

# Fonction principale
main() {
    if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
        show_help
        exit 0
    fi
    
    log_info "=== Déploiement de $SERVICE_NAME ==="
    log_info "Environnement: $ENV"
    log_info "Reconstruction: $REBUILD"
    echo ""
    
    check_prerequisites
    create_network
    stop_services
    build_images
    start_services
    
    echo ""
    log_info "=== Vérification finale ==="
    check_services
    
    echo ""
    log_success "=== Déploiement terminé ==="
    log_info "URLs d'accès:"
    log_info "  - Frontend: http://localhost:4203"
    log_info "  - Backend API: http://localhost:8083"
    log_info "  - Backend Health: http://localhost:8083/actuator/health"
    log_info "  - Swagger UI: http://localhost:8083/swagger-ui.html"
    log_info "  - Base de données: localhost:5435"
    log_info "  - Redis: localhost:6382"
    
    echo ""
    log_info "Commandes utiles:"
    log_info "  - Voir les logs: docker-compose -f $COMPOSE_FILE logs -f"
    log_info "  - Arrêter: docker-compose -f $COMPOSE_FILE down"
    log_info "  - Redémarrer: docker-compose -f $COMPOSE_FILE restart"
    log_info "  - État: docker-compose -f $COMPOSE_FILE ps"
}

# Gestion des signaux
trap 'log_error "Déploiement interrompu"; exit 1' INT TERM

# Exécution
main "$@"
