import { Routes } from '@angular/router';

export const RENDEZ_VOUS_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./rendez-vous-list/rendez-vous-list.component').then(m => m.RendezVousListComponent)
  },
  {
    path: 'nouveau',
    loadComponent: () => import('./rendez-vous-form/rendez-vous-form.component').then(m => m.RendezVousFormComponent)
  },
  {
    path: 'edit/:id',
    loadComponent: () => import('./rendez-vous-form/rendez-vous-form.component').then(m => m.RendezVousFormComponent)
  },
  {
    path: 'detail/:id',
    loadComponent: () => import('./rendez-vous-detail/rendez-vous-detail.component').then(m => m.RendezVousDetailComponent)
  },
  {
    path: 'planning',
    loadComponent: () => import('./rendez-vous-planning/rendez-vous-planning.component').then(m => m.RendezVousPlanningComponent)
  },
  {
    path: 'aujourd-hui',
    loadComponent: () => import('./rendez-vous-list/rendez-vous-list.component').then(m => m.RendezVousListComponent),
    data: { filter: 'aujourd-hui' }
  },
  {
    path: 'joueur/:joueurId',
    loadComponent: () => import('./rendez-vous-list/rendez-vous-list.component').then(m => m.RendezVousListComponent),
    data: { filter: 'joueur' }
  }
];
