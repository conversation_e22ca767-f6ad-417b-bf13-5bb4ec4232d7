package com.sprintbot.medicaladmin.service;

import com.sprintbot.medicaladmin.entity.DonneesSante;
import com.sprintbot.medicaladmin.repository.DonneesSanteRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Service pour la gestion des données de santé
 * Implémente la logique métier pour le suivi médical des joueurs
 */
@Service
@Transactional
public class DonneesSanteService {

    private static final Logger logger = LoggerFactory.getLogger(DonneesSanteService.class);

    @Autowired
    private DonneesSanteRepository donneesSanteRepository;

    // CRUD Operations
    public DonneesSante creerDonneesSante(DonneesSante donneesSante) {
        logger.info("Création de nouvelles données de santé pour le joueur ID: {}", donneesSante.getJoueurId());
        
        // Validation métier
        if (donneesSante.getDateExamen() == null) {
            donneesSante.setDateExamen(LocalDate.now());
        }
        
        if (donneesSante.getStatut() == null) {
            donneesSante.setStatut("ACTIF");
        }
        
        // Définir la visibilité par défaut selon le type d'examen
        if (donneesSante.getVisibleJoueur() == null) {
            donneesSante.setVisibleJoueur(!"BLESSURE".equals(donneesSante.getTypeExamen()));
        }
        
        DonneesSante saved = donneesSanteRepository.save(donneesSante);
        logger.info("Données de santé créées avec l'ID: {}", saved.getId());
        return saved;
    }

    @Transactional(readOnly = true)
    public Optional<DonneesSante> obtenirDonneesSante(Long id) {
        return donneesSanteRepository.findById(id);
    }

    @Transactional(readOnly = true)
    public List<DonneesSante> obtenirToutesDonneesSante() {
        return donneesSanteRepository.findAll();
    }

    @Transactional(readOnly = true)
    public Page<DonneesSante> obtenirDonneesSantePaginees(Pageable pageable) {
        return donneesSanteRepository.findAll(pageable);
    }

    public DonneesSante mettreAJourDonneesSante(DonneesSante donneesSante) {
        logger.info("Mise à jour des données de santé ID: {}", donneesSante.getId());
        
        if (!donneesSanteRepository.existsById(donneesSante.getId())) {
            throw new RuntimeException("Données de santé non trouvées avec l'ID: " + donneesSante.getId());
        }
        
        return donneesSanteRepository.save(donneesSante);
    }

    public void supprimerDonneesSante(Long id) {
        logger.info("Suppression des données de santé ID: {}", id);
        
        if (!donneesSanteRepository.existsById(id)) {
            throw new RuntimeException("Données de santé non trouvées avec l'ID: " + id);
        }
        
        donneesSanteRepository.deleteById(id);
    }

    // Recherches par joueur
    @Transactional(readOnly = true)
    public List<DonneesSante> obtenirDonneesSanteParJoueur(Long joueurId) {
        return donneesSanteRepository.findByJoueurIdOrderByDateExamenDesc(joueurId);
    }

    @Transactional(readOnly = true)
    public Page<DonneesSante> obtenirDonneesSanteParJoueurPaginees(Long joueurId, Pageable pageable) {
        return donneesSanteRepository.findByJoueurIdOrderByDateExamenDesc(joueurId, pageable);
    }

    @Transactional(readOnly = true)
    public Optional<DonneesSante> obtenirDernieresDonneesSante(Long joueurId) {
        return donneesSanteRepository.findFirstByJoueurIdOrderByDateExamenDesc(joueurId);
    }

    @Transactional(readOnly = true)
    public List<DonneesSante> obtenirDonneesSanteVisiblesParJoueur(Long joueurId) {
        return donneesSanteRepository.findByJoueurIdAndVisibleJoueurTrueOrderByDateExamenDesc(joueurId);
    }

    // Recherches par staff médical
    @Transactional(readOnly = true)
    public List<DonneesSante> obtenirDonneesSanteParStaffMedical(Long staffMedicalId) {
        return donneesSanteRepository.findByStaffMedicalIdOrderByDateExamenDesc(staffMedicalId);
    }

    @Transactional(readOnly = true)
    public Page<DonneesSante> obtenirDonneesSanteParStaffMedicalPaginees(Long staffMedicalId, Pageable pageable) {
        return donneesSanteRepository.findByStaffMedicalIdOrderByDateExamenDesc(staffMedicalId, pageable);
    }

    // Recherches par type et statut
    @Transactional(readOnly = true)
    public List<DonneesSante> obtenirDonneesSanteParType(String typeExamen) {
        return donneesSanteRepository.findByTypeExamenOrderByDateExamenDesc(typeExamen);
    }

    @Transactional(readOnly = true)
    public List<DonneesSante> obtenirDonneesSanteParStatut(String statut) {
        return donneesSanteRepository.findByStatutOrderByDateExamenDesc(statut);
    }

    @Transactional(readOnly = true)
    public List<DonneesSante> obtenirDonneesNecessitantSuivi() {
        return donneesSanteRepository.findDonneesNecessitantSuivi();
    }

    @Transactional(readOnly = true)
    public List<DonneesSante> obtenirSuiviParJoueur(Long joueurId) {
        return donneesSanteRepository.findSuiviParJoueur(joueurId);
    }

    // Gestion des blessures
    @Transactional(readOnly = true)
    public List<DonneesSante> obtenirDonneesAvecBlessures() {
        return donneesSanteRepository.findDonneesAvecBlessures();
    }

    @Transactional(readOnly = true)
    public List<DonneesSante> obtenirBlessuresParJoueur(Long joueurId) {
        return donneesSanteRepository.findBlessuresParJoueur(joueurId);
    }

    @Transactional(readOnly = true)
    public List<DonneesSante> obtenirDonneesGraves() {
        return donneesSanteRepository.findDonneesGraves();
    }

    public DonneesSante ajouterBlessure(Long donneesId, String nouvelleBlessure) {
        logger.info("Ajout d'une blessure aux données de santé ID: {}", donneesId);
        
        Optional<DonneesSante> optionalDonnees = donneesSanteRepository.findById(donneesId);
        if (optionalDonnees.isEmpty()) {
            throw new RuntimeException("Données de santé non trouvées avec l'ID: " + donneesId);
        }
        
        DonneesSante donnees = optionalDonnees.get();
        donnees.ajouterBlessure(nouvelleBlessure);
        
        return donneesSanteRepository.save(donnees);
    }

    // Gestion des statuts
    public DonneesSante marquerCommeGueri(Long donneesId) {
        logger.info("Marquage comme guéri des données de santé ID: {}", donneesId);
        
        Optional<DonneesSante> optionalDonnees = donneesSanteRepository.findById(donneesId);
        if (optionalDonnees.isEmpty()) {
            throw new RuntimeException("Données de santé non trouvées avec l'ID: " + donneesId);
        }
        
        DonneesSante donnees = optionalDonnees.get();
        donnees.marquerCommeGueri();
        
        return donneesSanteRepository.save(donnees);
    }

    public DonneesSante commencerTraitement(Long donneesId) {
        logger.info("Début de traitement pour les données de santé ID: {}", donneesId);
        
        Optional<DonneesSante> optionalDonnees = donneesSanteRepository.findById(donneesId);
        if (optionalDonnees.isEmpty()) {
            throw new RuntimeException("Données de santé non trouvées avec l'ID: " + donneesId);
        }
        
        DonneesSante donnees = optionalDonnees.get();
        donnees.commencerTraitement();
        
        return donneesSanteRepository.save(donnees);
    }

    public DonneesSante mettreEnSuivi(Long donneesId) {
        logger.info("Mise en suivi des données de santé ID: {}", donneesId);
        
        Optional<DonneesSante> optionalDonnees = donneesSanteRepository.findById(donneesId);
        if (optionalDonnees.isEmpty()) {
            throw new RuntimeException("Données de santé non trouvées avec l'ID: " + donneesId);
        }
        
        DonneesSante donnees = optionalDonnees.get();
        donnees.mettreEnSuivi();
        
        return donneesSanteRepository.save(donnees);
    }

    // Recherches par période
    @Transactional(readOnly = true)
    public List<DonneesSante> obtenirDonneesSanteParPeriode(LocalDate dateDebut, LocalDate dateFin) {
        return donneesSanteRepository.findByDateExamenBetweenOrderByDateExamenDesc(dateDebut, dateFin);
    }

    @Transactional(readOnly = true)
    public List<DonneesSante> obtenirDonneesSanteParJoueurEtPeriode(Long joueurId, LocalDate dateDebut, LocalDate dateFin) {
        return donneesSanteRepository.findByJoueurIdAndDateExamenBetweenOrderByDateExamenDesc(joueurId, dateDebut, dateFin);
    }

    @Transactional(readOnly = true)
    public List<DonneesSante> obtenirDonneesRecentes(int nombreJours) {
        LocalDate dateDebut = LocalDate.now().minusDays(nombreJours);
        return donneesSanteRepository.findDonneesRecentes(dateDebut);
    }

    @Transactional(readOnly = true)
    public List<DonneesSante> obtenirDonneesRecentesParJoueur(Long joueurId, int nombreJours) {
        LocalDate dateDebut = LocalDate.now().minusDays(nombreJours);
        return donneesSanteRepository.findDonneesRecentesParJoueur(joueurId, dateDebut);
    }

    // Alertes et notifications
    @Transactional(readOnly = true)
    public List<DonneesSante> obtenirGuerisonsPrevuesEchues() {
        return donneesSanteRepository.findGuerisonsPrevuesEchues(LocalDate.now());
    }

    @Transactional(readOnly = true)
    public List<DonneesSante> obtenirGuerisonsPrevuesProchaines(int nombreJours) {
        LocalDate dateDebut = LocalDate.now();
        LocalDate dateFin = LocalDate.now().plusDays(nombreJours);
        return donneesSanteRepository.findGuerisonsPrevuesProchaines(dateDebut, dateFin);
    }

    // Statistiques
    @Transactional(readOnly = true)
    public long compterDonneesSanteParJoueur(Long joueurId) {
        return donneesSanteRepository.countByJoueurId(joueurId);
    }

    @Transactional(readOnly = true)
    public long compterBlessuresParJoueur(Long joueurId) {
        return donneesSanteRepository.countBlessuresParJoueur(joueurId);
    }

    @Transactional(readOnly = true)
    public long compterExamensAujourdhui() {
        return donneesSanteRepository.countExamensAujourdhui();
    }

    @Transactional(readOnly = true)
    public long compterExamensDepuis(int nombreJours) {
        LocalDate dateDebut = LocalDate.now().minusDays(nombreJours);
        return donneesSanteRepository.countExamensDepuis(dateDebut);
    }

    // Recherche avec filtres
    @Transactional(readOnly = true)
    public Page<DonneesSante> rechercherAvecFiltres(Long joueurId, Long staffMedicalId, String typeExamen,
                                                   String statut, String gravite, LocalDate dateDebut,
                                                   LocalDate dateFin, Pageable pageable) {
        return donneesSanteRepository.findWithFilters(joueurId, staffMedicalId, typeExamen, statut, gravite,
                dateDebut, dateFin, pageable);
    }

    @Transactional(readOnly = true)
    public List<DonneesSante> rechercherParTexte(String searchTerm) {
        return donneesSanteRepository.searchByText(searchTerm);
    }

    // Rapports et statistiques avancées
    @Transactional(readOnly = true)
    public List<Object[]> obtenirStatistiquesParJoueur(LocalDate dateDebut, LocalDate dateFin) {
        return donneesSanteRepository.getStatistiquesParJoueur(dateDebut, dateFin);
    }

    @Transactional(readOnly = true)
    public List<Object[]> obtenirStatistiquesParStaffMedical(LocalDate dateDebut, LocalDate dateFin) {
        return donneesSanteRepository.getStatistiquesParStaffMedical(dateDebut, dateFin);
    }

    @Transactional(readOnly = true)
    public List<Object[]> obtenirRepartitionParStatut() {
        return donneesSanteRepository.countByStatutGlobal();
    }
}
