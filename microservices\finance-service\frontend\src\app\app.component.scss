// Variables
$sidenav-width: 280px;
$toolbar-height: 64px;
$primary-color: #1976d2;
$accent-color: #ff4081;
$warn-color: #f44336;
$success-color: #4caf50;

// Layout principal
.sidenav-container {
  height: 100vh;
  background-color: #fafafa;
}

// Sidenav
.sidenav {
  width: $sidenav-width;
  background-color: #fff;
  box-shadow: 3px 0 6px rgba(0, 0, 0, 0.1);
  border-right: 1px solid #e0e0e0;
  
  .sidenav-header {
    padding: 20px 16px;
    background: linear-gradient(135deg, $primary-color 0%, #1565c0 100%);
    color: white;
    
    .logo-container {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      
      .logo-icon {
        font-size: 32px;
        width: 32px;
        height: 32px;
        margin-right: 12px;
      }
      
      .logo-text {
        margin: 0;
        font-size: 24px;
        font-weight: 300;
        letter-spacing: 1px;
      }
    }
    
    .user-info {
      display: flex;
      align-items: center;
      padding: 12px;
      background-color: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      
      .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        
        mat-icon {
          font-size: 24px;
          width: 24px;
          height: 24px;
        }
      }
      
      .user-details {
        flex: 1;
        
        .user-name {
          font-weight: 500;
          font-size: 14px;
          margin-bottom: 2px;
        }
        
        .user-role {
          font-size: 12px;
          opacity: 0.8;
        }
      }
    }
  }
  
  .nav-list {
    padding-top: 8px;
    
    .nav-item {
      margin: 4px 8px;
      border-radius: 8px;
      transition: all 0.3s ease;
      cursor: pointer;
      
      &:hover {
        background-color: #f5f5f5;
      }
      
      &.active {
        background-color: rgba($primary-color, 0.1);
        color: $primary-color;
        
        .mat-icon {
          color: $primary-color;
        }
        
        .active-indicator {
          color: $primary-color;
        }
      }
      
      .mat-icon {
        color: #666;
        transition: color 0.3s ease;
      }
    }
  }
  
  .sidenav-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 8px;
    border-top: 1px solid #e0e0e0;
    
    .logout-item {
      margin: 4px;
      border-radius: 8px;
      color: $warn-color;
      
      &:hover {
        background-color: rgba($warn-color, 0.1);
      }
      
      .mat-icon {
        color: $warn-color;
      }
    }
  }
}

// Contenu principal
.main-content {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

// Toolbar
.toolbar {
  position: sticky;
  top: 0;
  z-index: 1000;
  height: $toolbar-height;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  .toolbar-title {
    display: flex;
    align-items: center;
    margin-left: 16px;
    
    .page-icon {
      margin-right: 8px;
      font-size: 24px;
      width: 24px;
      height: 24px;
    }
    
    .page-title {
      font-size: 20px;
      font-weight: 400;
    }
  }
  
  .toolbar-spacer {
    flex: 1 1 auto;
  }
  
  .toolbar-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    
    button {
      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }
}

// Contenu des pages
.page-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  background-color: #fafafa;
}

// Menus
.notification-menu,
.user-menu {
  .menu-header {
    padding: 16px;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 500;
    
    .user-info-menu {
      .user-name {
        font-weight: 500;
        margin-bottom: 4px;
      }
      
      .user-email {
        font-size: 12px;
        color: #666;
      }
    }
  }
  
  .mat-menu-item {
    display: flex;
    align-items: center;
    
    .mat-icon {
      margin-right: 12px;
      color: #666;
    }
    
    &.view-all {
      color: $primary-color;
      font-weight: 500;
    }
  }
}

// Responsive
@media (max-width: 768px) {
  .sidenav {
    width: 100vw;
    max-width: 320px;
  }
  
  .toolbar-title {
    .page-title {
      font-size: 18px;
    }
  }
  
  .page-content {
    padding: 16px;
  }
}

// Animations
@keyframes slideIn {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.sidenav {
  animation: slideIn 0.3s ease-out;
}

// Scrollbar personnalisée
.page-content {
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    
    &:hover {
      background: #a8a8a8;
    }
  }
}

// États de chargement
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
