import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-rapports',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="rapports">
      <!-- Header -->
      <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 class="h3 mb-0">Rapports et Statistiques</h1>
          <p class="text-muted">Analyse des données médicales et administratives</p>
        </div>
      </div>

      <!-- Types de rapports -->
      <div class="row">
        <div class="col-md-4 mb-4">
          <div class="card h-100">
            <div class="card-body text-center">
              <i class="fas fa-notes-medical fa-3x text-primary mb-3"></i>
              <h5 class="card-title">Rapports Médicaux</h5>
              <p class="card-text">Statistiques sur les données de santé, blessures et traitements</p>
              <button class="btn btn-primary" disabled>
                Générer rapport
              </button>
            </div>
          </div>
        </div>

        <div class="col-md-4 mb-4">
          <div class="card h-100">
            <div class="card-body text-center">
              <i class="fas fa-calendar-alt fa-3x text-success mb-3"></i>
              <h5 class="card-title">Rapports Rendez-vous</h5>
              <p class="card-text">Analyse du planning et des consultations médicales</p>
              <button class="btn btn-success" disabled>
                Générer rapport
              </button>
            </div>
          </div>
        </div>

        <div class="col-md-4 mb-4">
          <div class="card h-100">
            <div class="card-body text-center">
              <i class="fas fa-file-alt fa-3x text-warning mb-3"></i>
              <h5 class="card-title">Rapports Administratifs</h5>
              <p class="card-text">Suivi des demandes et processus administratifs</p>
              <button class="btn btn-warning" disabled>
                Générer rapport
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Message informatif -->
      <div class="alert alert-info mt-4">
        <i class="fas fa-info-circle me-2"></i>
        <strong>Fonctionnalité en développement</strong><br>
        Les rapports détaillés seront disponibles dans une prochaine version.
        Pour le moment, vous pouvez consulter les statistiques dans le tableau de bord.
      </div>
    </div>
  `,
  styles: [`
    .rapports {
      padding: 0;
    }
    
    .card {
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      border: none;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
    
    .card-body {
      padding: 2rem;
    }
    
    .fa-3x {
      opacity: 0.8;
    }
    
    .btn:disabled {
      opacity: 0.6;
    }
  `]
})
export class RapportsComponent {}
