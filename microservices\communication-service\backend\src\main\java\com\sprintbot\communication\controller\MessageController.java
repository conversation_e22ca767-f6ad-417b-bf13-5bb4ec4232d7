package com.sprintbot.communication.controller;

import com.sprintbot.communication.entity.Message;
import com.sprintbot.communication.entity.ReactionMessage;
import com.sprintbot.communication.entity.TypeMessage;
import com.sprintbot.communication.service.MessageService;
import com.sprintbot.communication.service.FileStorageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * Contrôleur REST pour la gestion des messages
 */
@RestController
@RequestMapping("/api/messages")
@CrossOrigin(origins = {"http://localhost:4200", "http://localhost:4204"})
public class MessageController {

    private static final Logger logger = LoggerFactory.getLogger(MessageController.class);

    @Autowired
    private MessageService messageService;

    @Autowired
    private FileStorageService fileStorageService;

    /**
     * Envoie un message texte
     */
    @PostMapping
    public ResponseEntity<?> envoyerMessage(@Valid @RequestBody EnvoyerMessageRequest request) {
        try {
            Message message = messageService.envoyerMessage(
                    request.conversationId(),
                    request.expediteurId(),
                    request.contenu()
            );

            return ResponseEntity.status(HttpStatus.CREATED).body(message);

        } catch (Exception e) {
            logger.error("Erreur lors de l'envoi de message: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Envoie un message avec fichier
     */
    @PostMapping("/avec-fichier")
    public ResponseEntity<?> envoyerMessageAvecFichier(
            @RequestParam Long conversationId,
            @RequestParam Long expediteurId,
            @RequestParam(required = false) String contenu,
            @RequestParam("fichier") MultipartFile fichier) {
        try {
            // Sauvegarder le fichier
            String urlFichier = fileStorageService.sauvegarderFichier(fichier);

            // Déterminer le type de message selon le fichier
            TypeMessage type = determinerTypeMessage(fichier.getContentType());

            Message message = messageService.envoyerMessage(
                    conversationId,
                    expediteurId,
                    contenu != null ? contenu : "Fichier partagé",
                    type,
                    urlFichier,
                    null
            );

            return ResponseEntity.status(HttpStatus.CREATED).body(message);

        } catch (Exception e) {
            logger.error("Erreur lors de l'envoi de message avec fichier: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Répond à un message
     */
    @PostMapping("/repondre")
    public ResponseEntity<?> repondreAMessage(@Valid @RequestBody RepondreMessageRequest request) {
        try {
            Message message = messageService.envoyerReponse(
                    request.conversationId(),
                    request.expediteurId(),
                    request.contenu(),
                    request.messageParentId()
            );

            return ResponseEntity.status(HttpStatus.CREATED).body(message);

        } catch (Exception e) {
            logger.error("Erreur lors de la réponse au message: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Obtient les messages d'une conversation
     */
    @GetMapping("/conversation/{conversationId}")
    public ResponseEntity<?> obtenirMessagesConversation(
            @PathVariable Long conversationId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "50") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Message> messages = messageService.obtenirMessagesConversation(conversationId, pageable);

            return ResponseEntity.ok(messages);

        } catch (Exception e) {
            logger.error("Erreur lors de l'obtention des messages: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Recherche des messages
     */
    @GetMapping("/rechercher")
    public ResponseEntity<?> rechercherMessages(
            @RequestParam Long conversationId,
            @RequestParam String recherche,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Message> messages = messageService.rechercherMessages(conversationId, recherche, pageable);

            return ResponseEntity.ok(messages);

        } catch (Exception e) {
            logger.error("Erreur lors de la recherche de messages: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Ajoute une réaction à un message
     */
    @PostMapping("/{messageId}/reactions")
    public ResponseEntity<?> ajouterReaction(
            @PathVariable Long messageId,
            @RequestBody AjouterReactionRequest request) {
        try {
            messageService.ajouterReaction(messageId, request.utilisateurId(), request.emoji());

            return ResponseEntity.ok(Map.of("message", "Réaction ajoutée avec succès"));

        } catch (Exception e) {
            logger.error("Erreur lors de l'ajout de réaction: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Supprime une réaction d'un message
     */
    @DeleteMapping("/{messageId}/reactions")
    public ResponseEntity<?> supprimerReaction(
            @PathVariable Long messageId,
            @RequestParam Long utilisateurId,
            @RequestParam String emoji) {
        try {
            messageService.supprimerReaction(messageId, utilisateurId, emoji);

            return ResponseEntity.ok(Map.of("message", "Réaction supprimée avec succès"));

        } catch (Exception e) {
            logger.error("Erreur lors de la suppression de réaction: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Marque un message comme lu
     */
    @PostMapping("/{messageId}/marquer-lu")
    public ResponseEntity<?> marquerMessageCommeLu(
            @PathVariable Long messageId,
            @RequestParam Long utilisateurId) {
        try {
            messageService.marquerCommeLu(messageId, utilisateurId);

            return ResponseEntity.ok(Map.of("message", "Message marqué comme lu"));

        } catch (Exception e) {
            logger.error("Erreur lors du marquage comme lu: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Modifie un message
     */
    @PutMapping("/{messageId}")
    public ResponseEntity<?> modifierMessage(
            @PathVariable Long messageId,
            @RequestBody ModifierMessageRequest request) {
        try {
            Message message = messageService.modifierMessage(
                    messageId, 
                    request.utilisateurId(), 
                    request.nouveauContenu()
            );

            return ResponseEntity.ok(message);

        } catch (Exception e) {
            logger.error("Erreur lors de la modification de message: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Supprime un message
     */
    @DeleteMapping("/{messageId}")
    public ResponseEntity<?> supprimerMessage(
            @PathVariable Long messageId,
            @RequestParam Long utilisateurId) {
        try {
            messageService.supprimerMessage(messageId, utilisateurId);

            return ResponseEntity.ok(Map.of("message", "Message supprimé avec succès"));

        } catch (Exception e) {
            logger.error("Erreur lors de la suppression de message: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Obtient les messages non lus d'un utilisateur
     */
    @GetMapping("/non-lus/{utilisateurId}")
    public ResponseEntity<?> obtenirMessagesNonLus(@PathVariable Long utilisateurId) {
        try {
            List<Message> messages = messageService.obtenirMessagesNonLus(utilisateurId);

            return ResponseEntity.ok(messages);

        } catch (Exception e) {
            logger.error("Erreur lors de l'obtention des messages non lus: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Compte les messages non lus d'un utilisateur
     */
    @GetMapping("/non-lus/{utilisateurId}/count")
    public ResponseEntity<?> compterMessagesNonLus(@PathVariable Long utilisateurId) {
        try {
            Long count = messageService.compterMessagesNonLus(utilisateurId);

            return ResponseEntity.ok(Map.of("count", count));

        } catch (Exception e) {
            logger.error("Erreur lors du comptage des messages non lus: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Obtient les réactions d'un message
     */
    @GetMapping("/{messageId}/reactions")
    public ResponseEntity<?> obtenirReactionsMessage(@PathVariable Long messageId) {
        try {
            List<ReactionMessage> reactions = messageService.obtenirReactionsMessage(messageId);

            return ResponseEntity.ok(reactions);

        } catch (Exception e) {
            logger.error("Erreur lors de l'obtention des réactions: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Obtient les messages épinglés d'une conversation
     */
    @GetMapping("/conversation/{conversationId}/epingles")
    public ResponseEntity<?> obtenirMessagesEpingles(@PathVariable Long conversationId) {
        try {
            List<Message> messages = messageService.obtenirMessagesEpingles(conversationId);

            return ResponseEntity.ok(messages);

        } catch (Exception e) {
            logger.error("Erreur lors de l'obtention des messages épinglés: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Épingle/désépingle un message
     */
    @PutMapping("/{messageId}/epingler")
    public ResponseEntity<?> toggleEpingleMessage(
            @PathVariable Long messageId,
            @RequestParam Long utilisateurId) {
        try {
            messageService.toggleEpingleMessage(messageId, utilisateurId);

            return ResponseEntity.ok(Map.of("message", "Statut épinglé mis à jour"));

        } catch (Exception e) {
            logger.error("Erreur lors de l'épinglage de message: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Détermine le type de message selon le type MIME
     */
    private TypeMessage determinerTypeMessage(String contentType) {
        if (contentType == null) return TypeMessage.FICHIER;

        if (contentType.startsWith("image/")) {
            return TypeMessage.IMAGE;
        } else if (contentType.startsWith("video/")) {
            return TypeMessage.VIDEO;
        } else if (contentType.startsWith("audio/")) {
            return TypeMessage.AUDIO;
        } else {
            return TypeMessage.FICHIER;
        }
    }

    // Records pour les requêtes
    public record EnvoyerMessageRequest(
            Long conversationId,
            Long expediteurId,
            String contenu
    ) {}

    public record RepondreMessageRequest(
            Long conversationId,
            Long expediteurId,
            String contenu,
            Long messageParentId
    ) {}

    public record AjouterReactionRequest(
            Long utilisateurId,
            String emoji
    ) {}

    public record ModifierMessageRequest(
            Long utilisateurId,
            String nouveauContenu
    ) {}
}
