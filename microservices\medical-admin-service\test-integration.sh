#!/bin/bash

# Script de test d'intégration pour Medical Admin Service
# Usage: ./test-integration.sh [--verbose] [--stop-on-error]

set -e

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKEND_URL="http://localhost:8083"
FRONTEND_URL="http://localhost:4203"
VERBOSE=false
STOP_ON_ERROR=false

# Traitement des arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --verbose)
            VERBOSE=true
            shift
            ;;
        --stop-on-error)
            STOP_ON_ERROR=true
            shift
            ;;
        *)
            echo "Usage: $0 [--verbose] [--stop-on-error]"
            exit 1
            ;;
    esac
done

# Fonctions utilitaires
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_verbose() {
    if [ "$VERBOSE" = true ]; then
        echo -e "${BLUE}[VERBOSE]${NC} $1"
    fi
}

# Fonction pour exécuter une requête HTTP
http_request() {
    local method=$1
    local url=$2
    local data=$3
    local expected_status=$4
    
    if [ -n "$data" ]; then
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -d "$data" "$url" 2>/dev/null)
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" "$url" 2>/dev/null)
    fi
    
    body=$(echo "$response" | head -n -1)
    status=$(echo "$response" | tail -n 1)
    
    log_verbose "Request: $method $url"
    log_verbose "Response Status: $status"
    log_verbose "Response Body: $body"
    
    if [ "$status" = "$expected_status" ]; then
        return 0
    else
        log_error "Expected status $expected_status, got $status"
        if [ "$STOP_ON_ERROR" = true ]; then
            exit 1
        fi
        return 1
    fi
}

# Test de connectivité des services
test_connectivity() {
    log_info "=== Test de connectivité ==="
    
    # Test backend health
    log_info "Test de santé du backend..."
    if http_request "GET" "$BACKEND_URL/actuator/health" "" "200"; then
        log_success "✓ Backend accessible et en bonne santé"
    else
        log_error "✗ Backend non accessible"
        return 1
    fi
    
    # Test frontend
    log_info "Test de santé du frontend..."
    if http_request "GET" "$FRONTEND_URL/health" "" "200"; then
        log_success "✓ Frontend accessible"
    else
        log_error "✗ Frontend non accessible"
        return 1
    fi
    
    return 0
}

# Test des APIs de données de santé
test_donnees_sante_api() {
    log_info "=== Test API Données de Santé ==="
    
    # Test GET all
    log_info "Test récupération de toutes les données de santé..."
    if http_request "GET" "$BACKEND_URL/api/donnees-sante" "" "200"; then
        log_success "✓ GET /api/donnees-sante"
    fi
    
    # Test POST create
    log_info "Test création d'une nouvelle donnée de santé..."
    data='{
        "joueurId": 1,
        "staffMedicalId": 1,
        "typeExamen": "BILAN_GENERAL",
        "dateExamen": "2024-07-29",
        "resultats": "Test d'\''intégration",
        "statut": "ACTIF",
        "gravite": "FAIBLE",
        "necessiteSuivi": false,
        "visibleParJoueur": true
    }'
    
    if http_request "POST" "$BACKEND_URL/api/donnees-sante" "$data" "201"; then
        log_success "✓ POST /api/donnees-sante"
        # Extraire l'ID de la réponse pour les tests suivants
        created_id=$(echo "$body" | grep -o '"id":[0-9]*' | cut -d':' -f2)
        log_verbose "ID créé: $created_id"
    fi
    
    # Test GET by ID
    if [ -n "$created_id" ]; then
        log_info "Test récupération par ID..."
        if http_request "GET" "$BACKEND_URL/api/donnees-sante/$created_id" "" "200"; then
            log_success "✓ GET /api/donnees-sante/$created_id"
        fi
    fi
    
    # Test recherche
    log_info "Test recherche avec filtres..."
    if http_request "GET" "$BACKEND_URL/api/donnees-sante/recherche?typeExamen=BILAN_GENERAL" "" "200"; then
        log_success "✓ GET /api/donnees-sante/recherche"
    fi
    
    # Test statistiques
    log_info "Test récupération des statistiques..."
    if http_request "GET" "$BACKEND_URL/api/donnees-sante/stats" "" "200"; then
        log_success "✓ GET /api/donnees-sante/stats"
    fi
    
    return 0
}

# Test des APIs de rendez-vous
test_rendez_vous_api() {
    log_info "=== Test API Rendez-vous ==="
    
    # Test GET all
    log_info "Test récupération de tous les rendez-vous..."
    if http_request "GET" "$BACKEND_URL/api/rendez-vous" "" "200"; then
        log_success "✓ GET /api/rendez-vous"
    fi
    
    # Test POST create
    log_info "Test création d'un nouveau rendez-vous..."
    data='{
        "joueurId": 1,
        "staffMedicalId": 1,
        "typeRendezVous": "CONSULTATION",
        "dateRendezVous": "2024-07-30",
        "heureDebut": "09:00",
        "heureFin": "09:30",
        "lieu": "Cabinet médical",
        "description": "Test d'\''intégration",
        "statut": "PLANIFIE",
        "priorite": "NORMALE",
        "rappelEnvoye": false
    }'
    
    if http_request "POST" "$BACKEND_URL/api/rendez-vous" "$data" "201"; then
        log_success "✓ POST /api/rendez-vous"
        rdv_id=$(echo "$body" | grep -o '"id":[0-9]*' | cut -d':' -f2)
        log_verbose "ID RDV créé: $rdv_id"
    fi
    
    # Test GET aujourd'hui
    log_info "Test récupération des rendez-vous d'aujourd'hui..."
    if http_request "GET" "$BACKEND_URL/api/rendez-vous/aujourd-hui" "" "200"; then
        log_success "✓ GET /api/rendez-vous/aujourd-hui"
    fi
    
    # Test planning journalier
    log_info "Test récupération du planning journalier..."
    if http_request "GET" "$BACKEND_URL/api/rendez-vous/planning-journalier?date=2024-07-30" "" "200"; then
        log_success "✓ GET /api/rendez-vous/planning-journalier"
    fi
    
    # Test vérification disponibilité
    log_info "Test vérification de disponibilité..."
    if http_request "GET" "$BACKEND_URL/api/rendez-vous/verifier-disponibilite?staffId=1&date=2024-07-30&heureDebut=10:00&heureFin=10:30" "" "200"; then
        log_success "✓ GET /api/rendez-vous/verifier-disponibilite"
    fi
    
    return 0
}

# Test des APIs de demandes administratives
test_demandes_admin_api() {
    log_info "=== Test API Demandes Administratives ==="
    
    # Test GET all
    log_info "Test récupération de toutes les demandes..."
    if http_request "GET" "$BACKEND_URL/api/demandes-administratives" "" "200"; then
        log_success "✓ GET /api/demandes-administratives"
    fi
    
    # Test POST create
    log_info "Test création d'une nouvelle demande..."
    data='{
        "demandeurId": 1,
        "typeDemandeur": "JOUEUR",
        "typeDemande": "CONGE",
        "titre": "Test d'\''intégration",
        "description": "Demande de test automatisé",
        "dateSoumission": "2024-07-29",
        "priorite": "NORMALE",
        "coutEstime": 0.00,
        "necessiteValidationCoach": true,
        "necessiteValidationMedical": false,
        "necessiteValidationFinancier": false
    }'
    
    if http_request "POST" "$BACKEND_URL/api/demandes-administratives" "$data" "201"; then
        log_success "✓ POST /api/demandes-administratives"
        demande_id=$(echo "$body" | grep -o '"id":[0-9]*' | cut -d':' -f2)
        log_verbose "ID demande créée: $demande_id"
    fi
    
    # Test GET en attente
    log_info "Test récupération des demandes en attente..."
    if http_request "GET" "$BACKEND_URL/api/demandes-administratives/en-attente" "" "200"; then
        log_success "✓ GET /api/demandes-administratives/en-attente"
    fi
    
    # Test validation coach
    if [ -n "$demande_id" ]; then
        log_info "Test validation coach..."
        validation_data='{"validation": true, "commentaire": "Test automatisé"}'
        if http_request "PUT" "$BACKEND_URL/api/demandes-administratives/$demande_id/validation-coach" "$validation_data" "200"; then
            log_success "✓ PUT /api/demandes-administratives/$demande_id/validation-coach"
        fi
    fi
    
    # Test statistiques
    log_info "Test récupération des statistiques..."
    if http_request "GET" "$BACKEND_URL/api/demandes-administratives/stats" "" "200"; then
        log_success "✓ GET /api/demandes-administratives/stats"
    fi
    
    return 0
}

# Test des fonctionnalités avancées
test_advanced_features() {
    log_info "=== Test Fonctionnalités Avancées ==="
    
    # Test pagination
    log_info "Test pagination..."
    if http_request "GET" "$BACKEND_URL/api/donnees-sante?page=0&size=5" "" "200"; then
        log_success "✓ Pagination des données de santé"
    fi
    
    # Test tri
    log_info "Test tri..."
    if http_request "GET" "$BACKEND_URL/api/rendez-vous?sort=dateRendezVous&direction=DESC" "" "200"; then
        log_success "✓ Tri des rendez-vous"
    fi
    
    # Test filtrage par période
    log_info "Test filtrage par période..."
    if http_request "GET" "$BACKEND_URL/api/donnees-sante/periode?dateDebut=2024-07-01&dateFin=2024-07-31" "" "200"; then
        log_success "✓ Filtrage par période"
    fi
    
    return 0
}

# Test de gestion d'erreurs
test_error_handling() {
    log_info "=== Test Gestion d'Erreurs ==="
    
    # Test 404 - Ressource inexistante
    log_info "Test erreur 404..."
    if http_request "GET" "$BACKEND_URL/api/donnees-sante/99999" "" "404"; then
        log_success "✓ Gestion erreur 404"
    fi
    
    # Test 400 - Données invalides
    log_info "Test erreur 400..."
    invalid_data='{"typeExamen": "INVALID_TYPE"}'
    if http_request "POST" "$BACKEND_URL/api/donnees-sante" "$invalid_data" "400"; then
        log_success "✓ Gestion erreur 400"
    fi
    
    return 0
}

# Test de performance basique
test_performance() {
    log_info "=== Test Performance Basique ==="
    
    log_info "Test temps de réponse API..."
    start_time=$(date +%s%N)
    http_request "GET" "$BACKEND_URL/api/donnees-sante" "" "200" > /dev/null
    end_time=$(date +%s%N)
    
    duration=$(( (end_time - start_time) / 1000000 )) # en millisecondes
    
    if [ $duration -lt 1000 ]; then
        log_success "✓ Temps de réponse acceptable: ${duration}ms"
    else
        log_warning "⚠ Temps de réponse élevé: ${duration}ms"
    fi
    
    return 0
}

# Nettoyage des données de test
cleanup_test_data() {
    log_info "=== Nettoyage des données de test ==="
    
    # Note: En production, implémenter un endpoint de nettoyage
    # ou utiliser des transactions rollback pour les tests
    
    log_info "Nettoyage des données de test créées..."
    # Ici on pourrait supprimer les données créées pendant les tests
    # Pour l'instant, on laisse les données pour inspection manuelle
    
    log_success "✓ Nettoyage terminé"
    
    return 0
}

# Fonction principale
main() {
    log_info "=== Début des tests d'intégration Medical Admin Service ==="
    echo ""
    
    local total_tests=0
    local passed_tests=0
    
    # Exécution des tests
    tests=(
        "test_connectivity"
        "test_donnees_sante_api"
        "test_rendez_vous_api"
        "test_demandes_admin_api"
        "test_advanced_features"
        "test_error_handling"
        "test_performance"
    )
    
    for test in "${tests[@]}"; do
        total_tests=$((total_tests + 1))
        echo ""
        if $test; then
            passed_tests=$((passed_tests + 1))
        else
            log_error "Test $test échoué"
            if [ "$STOP_ON_ERROR" = true ]; then
                break
            fi
        fi
    done
    
    # Nettoyage
    echo ""
    cleanup_test_data
    
    # Résumé
    echo ""
    log_info "=== Résumé des tests ==="
    log_info "Tests exécutés: $total_tests"
    log_info "Tests réussis: $passed_tests"
    log_info "Tests échoués: $((total_tests - passed_tests))"
    
    if [ $passed_tests -eq $total_tests ]; then
        log_success "🎉 Tous les tests sont passés avec succès!"
        exit 0
    else
        log_error "❌ Certains tests ont échoué"
        exit 1
    fi
}

# Vérification que les services sont démarrés
check_services_running() {
    log_info "Vérification que les services sont démarrés..."
    
    if ! curl -f "$BACKEND_URL/actuator/health" &> /dev/null; then
        log_error "Le backend n'est pas accessible sur $BACKEND_URL"
        log_info "Assurez-vous que les services sont démarrés avec: docker-compose up -d"
        exit 1
    fi
    
    if ! curl -f "$FRONTEND_URL/health" &> /dev/null; then
        log_error "Le frontend n'est pas accessible sur $FRONTEND_URL"
        log_info "Assurez-vous que les services sont démarrés avec: docker-compose up -d"
        exit 1
    fi
    
    log_success "Services accessibles"
}

# Point d'entrée
check_services_running
main "$@"
