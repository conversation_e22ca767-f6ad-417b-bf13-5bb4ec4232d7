#!/bin/bash

# Script de validation pour Discovery Service (Eureka Server)
# SprintBot - Service de découverte pour l'écosystème microservices

set -e

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SERVICE_URL="http://localhost:8761"
MAX_WAIT_TIME=120

# Fonctions utilitaires
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Fonction d'affichage du banner
show_banner() {
    echo ""
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║              🔍 VALIDATION DISCOVERY SERVICE                 ║"
    echo "║                   Eureka Server v1.0.0                      ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo ""
}

# Vérifier la structure du projet
check_project_structure() {
    log_info "📁 Vérification de la structure du projet..."
    
    local required_files=(
        "README.md"
        "backend/pom.xml"
        "backend/Dockerfile"
        "backend/docker-entrypoint.sh"
        "backend/src/main/java/com/sprintbot/discovery/DiscoveryServiceApplication.java"
        "backend/src/main/resources/application.yml"
        "docker-compose.yml"
    )
    
    local missing_files=()
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$SCRIPT_DIR/$file" ]; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -eq 0 ]; then
        log_success "✅ Structure du projet validée"
        return 0
    else
        log_error "❌ Fichiers manquants:"
        for file in "${missing_files[@]}"; do
            log_error "   - $file"
        done
        return 1
    fi
}

# Vérifier la configuration Maven
check_maven_config() {
    log_info "📦 Vérification de la configuration Maven..."
    
    local pom_file="$SCRIPT_DIR/backend/pom.xml"
    
    if [ ! -f "$pom_file" ]; then
        log_error "❌ Fichier pom.xml non trouvé"
        return 1
    fi
    
    # Vérifier les dépendances critiques
    local required_deps=(
        "spring-boot-starter-web"
        "spring-cloud-starter-netflix-eureka-server"
        "spring-boot-starter-actuator"
        "spring-boot-starter-security"
    )
    
    for dep in "${required_deps[@]}"; do
        if ! grep -q "$dep" "$pom_file"; then
            log_error "❌ Dépendance manquante: $dep"
            return 1
        fi
    done
    
    log_success "✅ Configuration Maven validée"
    return 0
}

# Vérifier la configuration Spring
check_spring_config() {
    log_info "⚙️ Vérification de la configuration Spring..."
    
    local config_file="$SCRIPT_DIR/backend/src/main/resources/application.yml"
    
    if [ ! -f "$config_file" ]; then
        log_error "❌ Fichier application.yml non trouvé"
        return 1
    fi
    
    # Vérifier les configurations critiques
    local required_configs=(
        "spring.application.name"
        "server.port"
        "eureka.client.register-with-eureka"
        "eureka.client.fetch-registry"
        "eureka.server.enable-self-preservation"
    )
    
    for config in "${required_configs[@]}"; do
        if ! grep -q "$config" "$config_file"; then
            log_error "❌ Configuration manquante: $config"
            return 1
        fi
    done
    
    log_success "✅ Configuration Spring validée"
    return 0
}

# Vérifier la configuration Docker
check_docker_config() {
    log_info "🐳 Vérification de la configuration Docker..."
    
    local dockerfile="$SCRIPT_DIR/backend/Dockerfile"
    local compose_file="$SCRIPT_DIR/docker-compose.yml"
    local entrypoint="$SCRIPT_DIR/backend/docker-entrypoint.sh"
    
    # Vérifier Dockerfile
    if [ ! -f "$dockerfile" ]; then
        log_error "❌ Dockerfile non trouvé"
        return 1
    fi
    
    # Vérifier docker-compose.yml
    if [ ! -f "$compose_file" ]; then
        log_error "❌ docker-compose.yml non trouvé"
        return 1
    fi
    
    # Vérifier script d'entrée
    if [ ! -f "$entrypoint" ]; then
        log_error "❌ docker-entrypoint.sh non trouvé"
        return 1
    fi
    
    # Vérifier que le script d'entrée est exécutable
    if [ ! -x "$entrypoint" ]; then
        log_warning "⚠️ docker-entrypoint.sh n'est pas exécutable, correction..."
        chmod +x "$entrypoint"
    fi
    
    log_success "✅ Configuration Docker validée"
    return 0
}

# Tester le build Maven
test_maven_build() {
    log_info "🔨 Test du build Maven..."
    
    cd "$SCRIPT_DIR/backend"
    
    if command -v mvn &> /dev/null; then
        if mvn clean compile -q; then
            log_success "✅ Build Maven réussi"
            return 0
        else
            log_error "❌ Échec du build Maven"
            return 1
        fi
    else
        log_warning "⚠️ Maven non installé, test de build ignoré"
        return 0
    fi
}

# Tester le build Docker
test_docker_build() {
    log_info "🐳 Test du build Docker..."
    
    cd "$SCRIPT_DIR"
    
    if command -v docker &> /dev/null; then
        if docker-compose build discovery-service; then
            log_success "✅ Build Docker réussi"
            return 0
        else
            log_error "❌ Échec du build Docker"
            return 1
        fi
    else
        log_warning "⚠️ Docker non installé, test de build ignoré"
        return 0
    fi
}

# Attendre que le service soit disponible
wait_for_service() {
    log_info "⏳ Attente de la disponibilité du service..."
    
    local count=0
    while [ $count -lt $MAX_WAIT_TIME ]; do
        if curl -s -f "$SERVICE_URL/actuator/health" > /dev/null 2>&1; then
            log_success "✅ Service Discovery disponible"
            return 0
        fi
        
        echo -n "."
        sleep 1
        count=$((count + 1))
    done
    
    log_error "❌ Service Discovery non disponible après ${MAX_WAIT_TIME}s"
    return 1
}

# Tester les endpoints du service
test_service_endpoints() {
    log_info "🌐 Test des endpoints du service..."
    
    # Test health check
    if curl -s -f "$SERVICE_URL/actuator/health" > /dev/null; then
        log_success "✅ Endpoint health OK"
    else
        log_error "❌ Endpoint health KO"
        return 1
    fi
    
    # Test dashboard Eureka
    if curl -s -f "$SERVICE_URL/" > /dev/null; then
        log_success "✅ Dashboard Eureka accessible"
    else
        log_error "❌ Dashboard Eureka inaccessible"
        return 1
    fi
    
    # Test endpoint info
    if curl -s -f "$SERVICE_URL/actuator/info" > /dev/null; then
        log_success "✅ Endpoint info OK"
    else
        log_warning "⚠️ Endpoint info non accessible"
    fi
    
    # Test endpoint metrics
    if curl -s -f "$SERVICE_URL/actuator/metrics" > /dev/null; then
        log_success "✅ Endpoint metrics OK"
    else
        log_warning "⚠️ Endpoint metrics non accessible"
    fi
    
    return 0
}

# Tester la fonctionnalité Eureka
test_eureka_functionality() {
    log_info "🔍 Test de la fonctionnalité Eureka..."
    
    # Test de l'endpoint apps (registry)
    if curl -s -f "$SERVICE_URL/eureka/apps" > /dev/null; then
        log_success "✅ Registry Eureka accessible"
    else
        log_error "❌ Registry Eureka inaccessible"
        return 1
    fi
    
    # Vérifier le statut du serveur Eureka
    local status_response=$(curl -s "$SERVICE_URL/eureka/status" 2>/dev/null || echo "")
    if [[ "$status_response" == *"UP"* ]]; then
        log_success "✅ Statut Eureka Server: UP"
    else
        log_warning "⚠️ Statut Eureka Server indéterminé"
    fi
    
    return 0
}

# Afficher les informations du service
show_service_info() {
    log_info "📋 Informations du service:"
    echo ""
    echo "🌐 URLs d'accès:"
    echo "   - Dashboard Eureka: $SERVICE_URL"
    echo "   - Health Check: $SERVICE_URL/actuator/health"
    echo "   - Metrics: $SERVICE_URL/actuator/metrics"
    echo "   - Registry: $SERVICE_URL/eureka/apps"
    echo ""
    echo "🔐 Authentification Dashboard:"
    echo "   - Utilisateur: admin"
    echo "   - Mot de passe: admin123"
    echo ""
}

# Afficher l'aide
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help              Afficher cette aide"
    echo "  -q, --quick             Validation rapide (structure + config)"
    echo "  -f, --full              Validation complète (avec build et tests)"
    echo "  -s, --service-only      Tester uniquement le service en cours"
    echo "  -u, --url URL           URL du service (défaut: $SERVICE_URL)"
    echo ""
    echo "Exemples:"
    echo "  $0                      Validation standard"
    echo "  $0 --quick              Validation rapide"
    echo "  $0 --full               Validation complète avec builds"
    echo "  $0 --service-only       Test du service uniquement"
}

# Fonction principale
main() {
    local quick=false
    local full=false
    local service_only=false
    
    # Traitement des arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -q|--quick)
                quick=true
                shift
                ;;
            -f|--full)
                full=true
                shift
                ;;
            -s|--service-only)
                service_only=true
                shift
                ;;
            -u|--url)
                SERVICE_URL="$2"
                shift 2
                ;;
            *)
                log_error "Option inconnue: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    show_banner
    
    log_info "🔍 Validation du Discovery Service (Eureka Server)"
    log_info "URL du service: $SERVICE_URL"
    echo ""
    
    # Validation selon le mode choisi
    if [ "$service_only" = true ]; then
        wait_for_service || exit 1
        test_service_endpoints || exit 1
        test_eureka_functionality || exit 1
        show_service_info
    elif [ "$quick" = true ]; then
        check_project_structure || exit 1
        check_maven_config || exit 1
        check_spring_config || exit 1
        check_docker_config || exit 1
    elif [ "$full" = true ]; then
        check_project_structure || exit 1
        check_maven_config || exit 1
        check_spring_config || exit 1
        check_docker_config || exit 1
        test_maven_build || exit 1
        test_docker_build || exit 1
    else
        # Validation standard
        check_project_structure || exit 1
        check_maven_config || exit 1
        check_spring_config || exit 1
        check_docker_config || exit 1
    fi
    
    echo ""
    log_success "🎉 Validation du Discovery Service terminée avec succès!"
    
    if [ "$service_only" != true ]; then
        echo ""
        log_info "💡 Pour démarrer le service:"
        log_info "   docker-compose up -d discovery-service"
        echo ""
        log_info "💡 Pour tester le service en cours:"
        log_info "   $0 --service-only"
    fi
}

# Exécution du script
main "$@"
