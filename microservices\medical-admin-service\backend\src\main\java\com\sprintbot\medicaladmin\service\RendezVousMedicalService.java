package com.sprintbot.medicaladmin.service;

import com.sprintbot.medicaladmin.entity.RendezVousMedical;
import com.sprintbot.medicaladmin.repository.RendezVousMedicalRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * Service pour la gestion des rendez-vous médicaux
 * Implémente la logique métier pour la planification et le suivi des consultations
 */
@Service
@Transactional
public class RendezVousMedicalService {

    private static final Logger logger = LoggerFactory.getLogger(RendezVousMedicalService.class);

    @Autowired
    private RendezVousMedicalRepository rendezVousRepository;

    // CRUD Operations
    public RendezVousMedical creerRendezVous(RendezVousMedical rendezVous) {
        logger.info("Création d'un nouveau rendez-vous médical pour le joueur ID: {}", rendezVous.getJoueurId());
        
        // Validation métier
        if (rendezVous.getStatut() == null) {
            rendezVous.setStatut("PLANIFIE");
        }
        
        if (rendezVous.getPriorite() == null) {
            rendezVous.setPriorite("NORMALE");
        }
        
        // Vérifier les conflits de créneaux
        if (verifierConflitCreneau(rendezVous)) {
            throw new RuntimeException("Conflit de créneau détecté pour ce rendez-vous");
        }
        
        RendezVousMedical saved = rendezVousRepository.save(rendezVous);
        logger.info("Rendez-vous médical créé avec l'ID: {}", saved.getId());
        return saved;
    }

    @Transactional(readOnly = true)
    public Optional<RendezVousMedical> obtenirRendezVous(Long id) {
        return rendezVousRepository.findById(id);
    }

    @Transactional(readOnly = true)
    public List<RendezVousMedical> obtenirTousRendezVous() {
        return rendezVousRepository.findAll();
    }

    @Transactional(readOnly = true)
    public Page<RendezVousMedical> obtenirRendezVousPagines(Pageable pageable) {
        return rendezVousRepository.findAll(pageable);
    }

    public RendezVousMedical mettreAJourRendezVous(RendezVousMedical rendezVous) {
        logger.info("Mise à jour du rendez-vous médical ID: {}", rendezVous.getId());
        
        if (!rendezVousRepository.existsById(rendezVous.getId())) {
            throw new RuntimeException("Rendez-vous non trouvé avec l'ID: " + rendezVous.getId());
        }
        
        return rendezVousRepository.save(rendezVous);
    }

    public void supprimerRendezVous(Long id) {
        logger.info("Suppression du rendez-vous médical ID: {}", id);
        
        if (!rendezVousRepository.existsById(id)) {
            throw new RuntimeException("Rendez-vous non trouvé avec l'ID: " + id);
        }
        
        rendezVousRepository.deleteById(id);
    }

    // Gestion des statuts
    public RendezVousMedical confirmerRendezVous(Long id) {
        logger.info("Confirmation du rendez-vous ID: {}", id);
        
        Optional<RendezVousMedical> optionalRendezVous = rendezVousRepository.findById(id);
        if (optionalRendezVous.isEmpty()) {
            throw new RuntimeException("Rendez-vous non trouvé avec l'ID: " + id);
        }
        
        RendezVousMedical rendezVous = optionalRendezVous.get();
        rendezVous.confirmer();
        
        return rendezVousRepository.save(rendezVous);
    }

    public RendezVousMedical commencerRendezVous(Long id) {
        logger.info("Début du rendez-vous ID: {}", id);
        
        Optional<RendezVousMedical> optionalRendezVous = rendezVousRepository.findById(id);
        if (optionalRendezVous.isEmpty()) {
            throw new RuntimeException("Rendez-vous non trouvé avec l'ID: " + id);
        }
        
        RendezVousMedical rendezVous = optionalRendezVous.get();
        rendezVous.commencer();
        
        return rendezVousRepository.save(rendezVous);
    }

    public RendezVousMedical terminerRendezVous(Long id, String compteRendu, String prescriptions) {
        logger.info("Fin du rendez-vous ID: {}", id);
        
        Optional<RendezVousMedical> optionalRendezVous = rendezVousRepository.findById(id);
        if (optionalRendezVous.isEmpty()) {
            throw new RuntimeException("Rendez-vous non trouvé avec l'ID: " + id);
        }
        
        RendezVousMedical rendezVous = optionalRendezVous.get();
        rendezVous.terminer();
        rendezVous.setCompteRendu(compteRendu);
        rendezVous.setPrescriptions(prescriptions);
        
        return rendezVousRepository.save(rendezVous);
    }

    public RendezVousMedical annulerRendezVous(Long id) {
        logger.info("Annulation du rendez-vous ID: {}", id);
        
        Optional<RendezVousMedical> optionalRendezVous = rendezVousRepository.findById(id);
        if (optionalRendezVous.isEmpty()) {
            throw new RuntimeException("Rendez-vous non trouvé avec l'ID: " + id);
        }
        
        RendezVousMedical rendezVous = optionalRendezVous.get();
        rendezVous.annuler();
        
        return rendezVousRepository.save(rendezVous);
    }

    public RendezVousMedical reporterRendezVous(Long id, LocalDate nouvelleDate, LocalTime nouvelleHeure) {
        logger.info("Report du rendez-vous ID: {} vers le {} à {}", id, nouvelleDate, nouvelleHeure);
        
        Optional<RendezVousMedical> optionalRendezVous = rendezVousRepository.findById(id);
        if (optionalRendezVous.isEmpty()) {
            throw new RuntimeException("Rendez-vous non trouvé avec l'ID: " + id);
        }
        
        RendezVousMedical rendezVous = optionalRendezVous.get();
        
        // Créer un nouveau rendez-vous temporaire pour vérifier les conflits
        RendezVousMedical tempRendezVous = new RendezVousMedical();
        tempRendezVous.setJoueurId(rendezVous.getJoueurId());
        tempRendezVous.setStaffMedicalId(rendezVous.getStaffMedicalId());
        tempRendezVous.setDateRendezVous(nouvelleDate);
        tempRendezVous.setHeureRendezVous(nouvelleHeure);
        tempRendezVous.setDureePrevue(rendezVous.getDureePrevue());
        
        if (verifierConflitCreneau(tempRendezVous)) {
            throw new RuntimeException("Conflit de créneau détecté pour la nouvelle date/heure");
        }
        
        rendezVous.reporter(nouvelleDate, nouvelleHeure);
        
        return rendezVousRepository.save(rendezVous);
    }

    // Recherches par joueur
    @Transactional(readOnly = true)
    public List<RendezVousMedical> obtenirRendezVousParJoueur(Long joueurId) {
        return rendezVousRepository.findByJoueurIdOrderByDateRendezVousDescHeureRendezVousDesc(joueurId);
    }

    @Transactional(readOnly = true)
    public Page<RendezVousMedical> obtenirRendezVousParJoueurPagines(Long joueurId, Pageable pageable) {
        return rendezVousRepository.findByJoueurIdOrderByDateRendezVousDescHeureRendezVousDesc(joueurId, pageable);
    }

    @Transactional(readOnly = true)
    public Optional<RendezVousMedical> obtenirProchainRendezVousJoueur(Long joueurId) {
        List<String> statutsActifs = Arrays.asList("PLANIFIE", "CONFIRME");
        return rendezVousRepository.findFirstByJoueurIdAndStatutInOrderByDateRendezVousAscHeureRendezVousAsc(
                joueurId, statutsActifs);
    }

    // Recherches par staff médical
    @Transactional(readOnly = true)
    public List<RendezVousMedical> obtenirRendezVousParStaffMedical(Long staffMedicalId) {
        return rendezVousRepository.findByStaffMedicalIdOrderByDateRendezVousAscHeureRendezVousAsc(staffMedicalId);
    }

    @Transactional(readOnly = true)
    public Page<RendezVousMedical> obtenirRendezVousParStaffMedicalPagines(Long staffMedicalId, Pageable pageable) {
        return rendezVousRepository.findByStaffMedicalIdOrderByDateRendezVousAscHeureRendezVousAsc(staffMedicalId, pageable);
    }

    // Planning et calendrier
    @Transactional(readOnly = true)
    public List<RendezVousMedical> obtenirRendezVousAujourdhui() {
        return rendezVousRepository.findRendezVousAujourdhui();
    }

    @Transactional(readOnly = true)
    public List<RendezVousMedical> obtenirRendezVousAujourdhuiParStaff(Long staffMedicalId) {
        return rendezVousRepository.findRendezVousAujourdhuiParStaff(staffMedicalId);
    }

    @Transactional(readOnly = true)
    public List<RendezVousMedical> obtenirRendezVousAujourdhuiParJoueur(Long joueurId) {
        return rendezVousRepository.findRendezVousAujourdhuiParJoueur(joueurId);
    }

    @Transactional(readOnly = true)
    public List<RendezVousMedical> obtenirRendezVousDemain() {
        return rendezVousRepository.findRendezVousDemain();
    }

    @Transactional(readOnly = true)
    public List<RendezVousMedical> obtenirRendezVousSemaineProchaine() {
        return rendezVousRepository.findRendezVousSemaineProchaine();
    }

    @Transactional(readOnly = true)
    public List<RendezVousMedical> obtenirPlanningStaffMedical(Long staffMedicalId, LocalDate dateDebut, LocalDate dateFin) {
        return rendezVousRepository.findPlanningStaffMedical(staffMedicalId, dateDebut, dateFin);
    }

    @Transactional(readOnly = true)
    public List<RendezVousMedical> obtenirPlanningJoueur(Long joueurId, LocalDate dateDebut, LocalDate dateFin) {
        return rendezVousRepository.findPlanningJoueur(joueurId, dateDebut, dateFin);
    }

    // Gestion des rappels
    @Transactional(readOnly = true)
    public List<RendezVousMedical> obtenirRendezVousNecessitantRappel() {
        return rendezVousRepository.findRendezVousNecessitantRappel();
    }

    public RendezVousMedical marquerRappelEnvoye(Long id) {
        logger.info("Marquage du rappel comme envoyé pour le rendez-vous ID: {}", id);
        
        Optional<RendezVousMedical> optionalRendezVous = rendezVousRepository.findById(id);
        if (optionalRendezVous.isEmpty()) {
            throw new RuntimeException("Rendez-vous non trouvé avec l'ID: " + id);
        }
        
        RendezVousMedical rendezVous = optionalRendezVous.get();
        rendezVous.marquerRappelEnvoye();
        
        return rendezVousRepository.save(rendezVous);
    }

    // Recherches par statut et type
    @Transactional(readOnly = true)
    public List<RendezVousMedical> obtenirRendezVousParStatut(String statut) {
        return rendezVousRepository.findByStatutOrderByDateRendezVousAscHeureRendezVousAsc(statut);
    }

    @Transactional(readOnly = true)
    public List<RendezVousMedical> obtenirRendezVousParType(String typeRendezVous) {
        return rendezVousRepository.findByTypeRendezVousOrderByDateRendezVousAscHeureRendezVousAsc(typeRendezVous);
    }

    @Transactional(readOnly = true)
    public List<RendezVousMedical> obtenirRendezVousUrgents() {
        return rendezVousRepository.findRendezVousUrgents();
    }

    // Vérification de disponibilité
    @Transactional(readOnly = true)
    public boolean verifierDisponibiliteStaff(Long staffMedicalId, LocalDate date, LocalTime heureDebut, LocalTime heureFin) {
        List<RendezVousMedical> conflits = rendezVousRepository.findConflitsCreneauStaff(
                staffMedicalId, date, heureDebut, heureFin);
        return conflits.isEmpty();
    }

    @Transactional(readOnly = true)
    public boolean verifierDisponibiliteJoueur(Long joueurId, LocalDate date, LocalTime heureDebut, LocalTime heureFin) {
        List<RendezVousMedical> conflits = rendezVousRepository.findConflitsCreneauJoueur(
                joueurId, date, heureDebut, heureFin);
        return conflits.isEmpty();
    }

    private boolean verifierConflitCreneau(RendezVousMedical rendezVous) {
        if (rendezVous.getDureePrevue() == null) {
            rendezVous.setDureePrevue(30); // Durée par défaut de 30 minutes
        }
        
        LocalTime heureDebut = rendezVous.getHeureRendezVous();
        LocalTime heureFin = heureDebut.plusMinutes(rendezVous.getDureePrevue());
        
        // Vérifier les conflits pour le staff médical
        boolean conflitStaff = !verifierDisponibiliteStaff(
                rendezVous.getStaffMedicalId(),
                rendezVous.getDateRendezVous(),
                heureDebut,
                heureFin
        );
        
        // Vérifier les conflits pour le joueur
        boolean conflitJoueur = !verifierDisponibiliteJoueur(
                rendezVous.getJoueurId(),
                rendezVous.getDateRendezVous(),
                heureDebut,
                heureFin
        );
        
        return conflitStaff || conflitJoueur;
    }

    // Statistiques
    @Transactional(readOnly = true)
    public long compterRendezVousParJoueur(Long joueurId) {
        return rendezVousRepository.countByJoueurId(joueurId);
    }

    @Transactional(readOnly = true)
    public long compterRendezVousParStaffMedical(Long staffMedicalId) {
        return rendezVousRepository.countByStaffMedicalId(staffMedicalId);
    }

    @Transactional(readOnly = true)
    public long compterRendezVousAujourdhui() {
        return rendezVousRepository.countRendezVousAujourdhui();
    }

    @Transactional(readOnly = true)
    public long compterRendezVousDemain() {
        return rendezVousRepository.countRendezVousDemain();
    }

    // Recherche avec filtres
    @Transactional(readOnly = true)
    public Page<RendezVousMedical> rechercherAvecFiltres(Long joueurId, Long staffMedicalId, String typeRendezVous,
                                                        String statut, String priorite, LocalDate dateDebut,
                                                        LocalDate dateFin, Pageable pageable) {
        return rendezVousRepository.findWithFilters(joueurId, staffMedicalId, typeRendezVous, statut, priorite,
                dateDebut, dateFin, pageable);
    }

    @Transactional(readOnly = true)
    public List<RendezVousMedical> rechercherParTexte(String searchTerm) {
        return rendezVousRepository.searchByText(searchTerm);
    }

    // Alertes
    @Transactional(readOnly = true)
    public List<RendezVousMedical> obtenirRendezVousEchus() {
        return rendezVousRepository.findRendezVousEchus();
    }

    @Transactional(readOnly = true)
    public List<RendezVousMedical> obtenirProchainesVisitesEchues() {
        return rendezVousRepository.findProchainesVisitesEchues();
    }

    // Rapports
    @Transactional(readOnly = true)
    public List<Object[]> obtenirStatistiquesParJoueur(LocalDate dateDebut, LocalDate dateFin) {
        return rendezVousRepository.getStatistiquesParJoueur(dateDebut, dateFin);
    }

    @Transactional(readOnly = true)
    public List<Object[]> obtenirStatistiquesParStaffMedical(LocalDate dateDebut, LocalDate dateFin) {
        return rendezVousRepository.getStatistiquesParStaffMedical(dateDebut, dateFin);
    }
}
