import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { Client, IMessage, StompConfig } from '@stomp/stompjs';
import SockJS from 'sockjs-client';
// import { environment } from '../../environments/environment';

// Configuration temporaire pour le développement
const environment = {
  production: false,
  apiUrl: 'http://localhost:8084'
};

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: number;
}

export interface TypingIndicator {
  utilisateurId: number;
  conversationId: number;
  enTrainDeTaper: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class WebSocketService {
  private client: Client;
  private connected$ = new BehaviorSubject<boolean>(false);
  private messageSubject = new Subject<WebSocketMessage>();
  private typingSubject = new Subject<TypingIndicator>();
  private presenceSubject = new Subject<any>();

  private utilisateurId: number | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 5000;

  constructor() {
    this.initializeWebSocket();
  }

  private initializeWebSocket(): void {
    const stompConfig: StompConfig = {
      webSocketFactory: () => new SockJS(`${environment.apiUrl}/ws`),
      connectHeaders: {
        'utilisateurId': this.utilisateurId?.toString() || '',
        'plateforme': this.detectPlatform(),
        'estMobile': this.isMobile().toString()
      },
      debug: (str: string) => {
        if (!environment.production) {
          console.log('STOMP Debug:', str);
        }
      },
      reconnectDelay: this.reconnectInterval,
      heartbeatIncoming: 4000,
      heartbeatOutgoing: 4000,
      onConnect: () => {
        console.log('WebSocket connecté');
        this.connected$.next(true);
        this.reconnectAttempts = 0;
        this.subscribeToTopics();
      },
      onDisconnect: () => {
        console.log('WebSocket déconnecté');
        this.connected$.next(false);
      },
      onStompError: (frame) => {
        console.error('Erreur STOMP:', frame);
        this.handleReconnection();
      },
      onWebSocketError: (error) => {
        console.error('Erreur WebSocket:', error);
        this.handleReconnection();
      }
    };

    this.client = new Client(stompConfig);
  }

  private handleReconnection(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Tentative de reconnexion ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
      
      setTimeout(() => {
        if (!this.client.connected) {
          this.connect();
        }
      }, this.reconnectInterval * this.reconnectAttempts);
    } else {
      console.error('Nombre maximum de tentatives de reconnexion atteint');
    }
  }

  private subscribeToTopics(): void {
    if (!this.client.connected) return;

    // Abonnement aux erreurs personnelles
    this.client.subscribe(`/queue/errors/${this.client.webSocket?.url}`, (message: IMessage) => {
      const error = JSON.parse(message.body);
      console.error('Erreur WebSocket reçue:', error);
    });

    // Abonnement aux notifications personnelles
    if (this.utilisateurId) {
      this.client.subscribe(`/user/queue/notifications`, (message: IMessage) => {
        const notification = JSON.parse(message.body);
        this.messageSubject.next({
          type: 'NOTIFICATION',
          data: notification,
          timestamp: Date.now()
        });
      });
    }

    // Abonnement au pong pour le ping
    this.client.subscribe('/topic/pong', (message: IMessage) => {
      const pong = JSON.parse(message.body);
      console.debug('Pong reçu:', pong);
    });
  }

  connect(utilisateurId?: number): void {
    if (utilisateurId) {
      this.utilisateurId = utilisateurId;
      this.client.connectHeaders = {
        ...this.client.connectHeaders,
        'utilisateurId': utilisateurId.toString()
      };
    }

    if (!this.client.connected) {
      this.client.activate();
    }
  }

  disconnect(): void {
    if (this.client.connected) {
      this.client.deactivate();
    }
    this.connected$.next(false);
  }

  isConnected(): Observable<boolean> {
    return this.connected$.asObservable();
  }

  // Gestion des conversations
  joinConversation(conversationId: number): void {
    if (!this.client.connected) return;

    // S'abonner aux messages de la conversation
    this.client.subscribe(`/topic/conversation/${conversationId}`, (message: IMessage) => {
      const messageData = JSON.parse(message.body);
      this.messageSubject.next({
        type: 'MESSAGE',
        data: messageData,
        timestamp: Date.now()
      });
    });

    // S'abonner aux indicateurs de frappe
    this.client.subscribe(`/topic/conversation/${conversationId}/typing`, (message: IMessage) => {
      const typingData = JSON.parse(message.body);
      this.typingSubject.next(typingData);
    });

    // S'abonner aux réactions
    this.client.subscribe(`/topic/conversation/${conversationId}/reactions`, (message: IMessage) => {
      const reactionData = JSON.parse(message.body);
      this.messageSubject.next({
        type: 'REACTION',
        data: reactionData,
        timestamp: Date.now()
      });
    });

    // S'abonner aux lectures
    this.client.subscribe(`/topic/conversation/${conversationId}/read`, (message: IMessage) => {
      const readData = JSON.parse(message.body);
      this.messageSubject.next({
        type: 'READ',
        data: readData,
        timestamp: Date.now()
      });
    });

    // Notifier le serveur que l'utilisateur a rejoint
    this.client.publish({
      destination: `/app/conversation/${conversationId}/join`,
      body: JSON.stringify({})
    });
  }

  leaveConversation(conversationId: number): void {
    if (!this.client.connected) return;

    this.client.publish({
      destination: `/app/conversation/${conversationId}/leave`,
      body: JSON.stringify({})
    });
  }

  sendMessage(conversationId: number, contenu: string, messageParentId?: number): void {
    if (!this.client.connected) return;

    this.client.publish({
      destination: `/app/conversation/${conversationId}/message`,
      body: JSON.stringify({
        contenu,
        messageParentId
      })
    });
  }

  sendTypingIndicator(conversationId: number, enTrainDeTaper: boolean): void {
    if (!this.client.connected) return;

    this.client.publish({
      destination: `/app/conversation/${conversationId}/typing`,
      body: JSON.stringify({ enTrainDeTaper })
    });
  }

  markMessageAsRead(messageId: number): void {
    if (!this.client.connected) return;

    this.client.publish({
      destination: `/app/message/${messageId}/lu`,
      body: JSON.stringify({})
    });
  }

  addReaction(messageId: number, emoji: string): void {
    if (!this.client.connected) return;

    this.client.publish({
      destination: `/app/message/${messageId}/reaction`,
      body: JSON.stringify({ emoji })
    });
  }

  // Ping pour maintenir la connexion
  ping(): void {
    if (!this.client.connected) return;

    this.client.publish({
      destination: '/app/ping',
      body: JSON.stringify({ timestamp: Date.now() })
    });
  }

  // Observables pour les composants
  getMessages(): Observable<WebSocketMessage> {
    return this.messageSubject.asObservable();
  }

  getTypingIndicators(): Observable<TypingIndicator> {
    return this.typingSubject.asObservable();
  }

  getPresenceUpdates(): Observable<any> {
    return this.presenceSubject.asObservable();
  }

  // Utilitaires
  private detectPlatform(): string {
    const userAgent = navigator.userAgent.toLowerCase();
    if (userAgent.includes('android')) return 'android';
    if (userAgent.includes('iphone') || userAgent.includes('ipad')) return 'ios';
    if (userAgent.includes('windows')) return 'windows';
    if (userAgent.includes('mac')) return 'mac';
    if (userAgent.includes('linux')) return 'linux';
    return 'web';
  }

  private isMobile(): boolean {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  }

  // Nettoyage
  ngOnDestroy(): void {
    this.disconnect();
    this.messageSubject.complete();
    this.typingSubject.complete();
    this.presenceSubject.complete();
    this.connected$.complete();
  }
}
