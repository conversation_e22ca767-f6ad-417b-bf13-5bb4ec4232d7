// Styles pour le composant principal de l'application

.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-toolbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .menu-button {
    margin-right: 16px;
  }

  .app-title {
    font-size: 1.2rem;
    font-weight: 500;
  }

  .spacer {
    flex: 1 1 auto;
  }

  button {
    margin-left: 8px;
  }
}

.app-sidenav-container {
  flex: 1;
  margin-top: 64px; // Hauteur de la toolbar
}

.app-sidenav {
  width: 280px;
  background-color: #fafafa;
  border-right: 1px solid #e0e0e0;

  .mat-nav-list {
    padding-top: 16px;

    h3[matSubheader] {
      color: #666;
      font-weight: 500;
      margin: 16px 0 8px 0;
      padding: 0 16px;
    }

    .nav-item {
      margin: 4px 8px;
      border-radius: 8px;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }

      &.active-nav-item {
        background-color: rgba(63, 81, 181, 0.1);
        color: #3f51b5;

        .mat-icon {
          color: #3f51b5;
        }
      }

      .nav-badge {
        margin-left: auto;

        .mat-chip {
          height: 20px;
          font-size: 11px;
          min-height: 20px;
        }
      }
    }
  }
}

.app-content {
  background-color: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.content-wrapper {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
  min-height: calc(100vh - 112px); // 64px toolbar + 48px padding
}

// Menu utilisateur
.user-info {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 8px;

  .user-name {
    font-weight: 500;
    font-size: 16px;
    margin-bottom: 4px;
  }

  .user-email {
    color: #666;
    font-size: 14px;
  }
}

// Overlay de chargement
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  p {
    margin-top: 16px;
    color: #666;
    font-size: 14px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .app-sidenav {
    width: 100%;
    position: fixed;
    z-index: 999;
  }

  .content-wrapper {
    padding: 16px;
  }

  .app-toolbar {
    .app-title {
      font-size: 1rem;
    }

    button {
      margin-left: 4px;
    }
  }
}

@media (max-width: 480px) {
  .content-wrapper {
    padding: 8px;
  }

  .app-toolbar {
    .app-title {
      display: none;
    }
  }
}

// Animations
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.content-wrapper {
  animation: fadeIn 0.3s ease-out;
}

// Thème sombre (optionnel)
.dark-theme {
  .app-sidenav {
    background-color: #303030;
    border-right-color: #424242;

    .mat-nav-list {
      h3[matSubheader] {
        color: #bbb;
      }

      .nav-item {
        color: #fff;

        &:hover {
          background-color: rgba(255, 255, 255, 0.08);
        }

        &.active-nav-item {
          background-color: rgba(63, 81, 181, 0.2);
        }
      }
    }
  }

  .app-content {
    background-color: #424242;
  }

  .user-info {
    background-color: #303030;
    border-bottom-color: #424242;

    .user-name {
      color: #fff;
    }

    .user-email {
      color: #bbb;
    }
  }
}

// Utilitaires
.mat-badge-content {
  font-size: 11px !important;
  font-weight: 500 !important;
}

.mat-icon {
  vertical-align: middle;
}

// Scrollbar personnalisée
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
