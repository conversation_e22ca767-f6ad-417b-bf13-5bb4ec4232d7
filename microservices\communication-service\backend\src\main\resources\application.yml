# Configuration Communication Service
spring:
  application:
    name: communication-service
  
  profiles:
    active: dev
  
  # Configuration JPA
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        jdbc:
          lob:
            non_contextual_creation: true
        default_schema: communication
    open-in-view: false
  
  # Configuration Flyway
  flyway:
    enabled: true
    locations: classpath:db/migration
    schemas: communication
    baseline-on-migrate: true
    validate-on-migrate: true
  
  # Configuration Cache Redis
  cache:
    type: redis
    redis:
      time-to-live: 600000 # 10 minutes
  
  # Configuration Mail
  mail:
    host: ${EMAIL_HOST:smtp.gmail.com}
    port: ${EMAIL_PORT:587}
    username: ${EMAIL_USERNAME:<EMAIL>}
    password: ${EMAIL_PASSWORD:}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
  
  # Configuration JSON
  jackson:
    serialization:
      write-dates-as-timestamps: false
    time-zone: Europe/Paris
    default-property-inclusion: non_null

# Configuration serveur
server:
  port: 8084

# Configuration Eureka Client
eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_CLIENT_SERVICE_URL:http://localhost:8761/eureka/}
    register-with-eureka: true
    fetch-registry: true
    registry-fetch-interval-seconds: 30
  instance:
    hostname: ${EUREKA_INSTANCE_HOSTNAME:localhost}
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90
    metadata-map:
      version: "1.0.0"
      description: "Communication Service - Messagerie et notifications"
      team: "SprintBot"
  servlet:
    context-path: /
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
  http2:
    enabled: true

# Configuration sécurité JWT
jwt:
  secret: ${JWT_SECRET:communication_jwt_secret_key_very_long_and_secure}
  expiration: ${JWT_EXPIRATION:86400} # 24 heures
  refresh-expiration: ${JWT_REFRESH_EXPIRATION:604800} # 7 jours

# Configuration CORS
cors:
  allowed-origins:
    - http://localhost:4200
    - http://localhost:4201
    - http://localhost:4202
    - http://localhost:4203
    - http://localhost:4204
  allowed-methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
  allowed-headers:
    - "*"
  allow-credentials: true

# Configuration WebSocket
websocket:
  allowed-origins:
    - http://localhost:4200
    - http://localhost:4201
    - http://localhost:4202
    - http://localhost:4203
    - http://localhost:4204
  endpoint: /ws
  destination-prefix: /app
  broker-prefix: /topic

# Configuration Chatbot
chatbot:
  openai:
    api-key: ${OPENAI_API_KEY:}
    model: ${CHATBOT_MODEL:gpt-3.5-turbo}
    max-tokens: 150
    temperature: 0.7
  enabled: ${CHATBOT_ENABLED:true}
  default-language: fr
  session-timeout: 1800 # 30 minutes

# Configuration Notifications
notifications:
  push:
    vapid:
      public-key: ${VAPID_PUBLIC_KEY:}
      private-key: ${VAPID_PRIVATE_KEY:}
      subject: ${VAPID_SUBJECT:mailto:<EMAIL>}
  email:
    templates-path: classpath:templates/email/
    from-address: ${EMAIL_FROM:<EMAIL>}
    from-name: SprintBot Notifications
  batch-size: 100
  retry-attempts: 3

# Configuration Upload de fichiers
file:
  upload:
    max-size: 10MB
    allowed-types: 
      - image/jpeg
      - image/png
      - image/gif
      - application/pdf
      - text/plain
      - application/msword
      - application/vnd.openxmlformats-officedocument.wordprocessingml.document
    storage-path: ${FILE_STORAGE_PATH:./uploads/communication/}

# Configuration Actuator
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,websocket
  endpoint:
    health:
      show-details: always
  health:
    db:
      enabled: true
    redis:
      enabled: true
    mail:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

# Configuration spécifique au microservice
microservice:
  communication:
    name: "Communication Service"
    version: "1.0.0"
    description: "Microservice de communication avec messagerie, notifications et chatbot"

# Configuration Logging
logging:
  level:
    com.sprintbot.communication: INFO
    org.springframework.web.socket: DEBUG
    org.springframework.messaging: DEBUG
    org.springframework.security: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/communication-service.log

---
# Profil de développement
spring:
  config:
    activate:
      on-profile: dev
  
  datasource:
    url: *****************************************************************************
    username: communication_user
    password: communication_password
    driver-class-name: org.postgresql.Driver
  
  # Configuration Redis pour développement
  data:
    redis:
      host: localhost
      port: 6380
      password: 
      database: 0
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0

# Configuration CORS pour développement
cors:
  allowed-origins:
    - http://localhost:4200
    - http://localhost:4201
    - http://localhost:4202
    - http://localhost:4203
    - http://localhost:4204

---
# Profil Docker
spring:
  config:
    activate:
      on-profile: docker
  
  datasource:
    url: ************************************************************************************
    username: communication_user
    password: communication_password
  
  # Configuration Redis pour Docker
  data:
    redis:
      host: communication-redis
      port: 6379
      password: 
      database: 0

# Configuration CORS pour Docker
cors:
  allowed-origins:
    - http://localhost:4200
    - http://localhost:4201
    - http://localhost:4202
    - http://localhost:4203
    - http://localhost:4204
    - http://communication-frontend:80
    - http://frontend:80

server:
  port: 8084

# Configuration Eureka pour Docker
eureka:
  client:
    service-url:
      defaultZone: http://discovery-service:8761/eureka/
  instance:
    hostname: communication-service
    prefer-ip-address: true

---
# Profil de production
spring:
  config:
    activate:
      on-profile: prod
  
  datasource:
    url: ${DATABASE_URL:************************************************************************************}
    username: ${DATABASE_USERNAME:communication_user}
    password: ${DATABASE_PASSWORD:communication_password}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  # Configuration Redis pour production
  data:
    redis:
      host: ${REDIS_HOST:communication-redis}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: 0
      ssl: false
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5

# Configuration sécurisée pour production
jwt:
  secret: ${JWT_SECRET}
  expiration: ${JWT_EXPIRATION:3600} # 1 heure en production

# Configuration CORS pour production
cors:
  allowed-origins:
    - ${FRONTEND_URL:https://sprintbot.com}
    - ${ADMIN_URL:https://admin.sprintbot.com}

# Logging en production
logging:
  level:
    com.sprintbot.communication: INFO
    org.springframework.web.socket: WARN
    org.springframework.messaging: WARN
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
