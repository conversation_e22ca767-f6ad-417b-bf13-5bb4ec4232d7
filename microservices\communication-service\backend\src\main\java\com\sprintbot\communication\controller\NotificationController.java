package com.sprintbot.communication.controller;

import com.sprintbot.communication.entity.*;
import com.sprintbot.communication.service.NotificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Contrôleur REST pour la gestion des notifications
 */
@RestController
@RequestMapping("/api/notifications")
@CrossOrigin(origins = {"http://localhost:4200", "http://localhost:4204"})
public class NotificationController {

    private static final Logger logger = LoggerFactory.getLogger(NotificationController.class);

    @Autowired
    private NotificationService notificationService;

    /**
     * Crée une nouvelle notification
     */
    @PostMapping
    public ResponseEntity<?> creerNotification(@Valid @RequestBody CreerNotificationRequest request) {
        try {
            Notification notification = notificationService.creerNotification(
                    request.destinataireId(),
                    request.type(),
                    request.canal(),
                    request.titre(),
                    request.contenu(),
                    request.priorite(),
                    request.dateProgrammee(),
                    request.donnees()
            );

            return ResponseEntity.status(HttpStatus.CREATED).body(notification);

        } catch (Exception e) {
            logger.error("Erreur lors de la création de notification: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Obtient les notifications d'un utilisateur
     */
    @GetMapping("/utilisateur/{utilisateurId}")
    public ResponseEntity<?> obtenirNotificationsUtilisateur(
            @PathVariable Long utilisateurId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Notification> notifications = notificationService
                    .obtenirNotificationsUtilisateur(utilisateurId, pageable);

            return ResponseEntity.ok(notifications);

        } catch (Exception e) {
            logger.error("Erreur lors de l'obtention des notifications: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Obtient les notifications non lues d'un utilisateur
     */
    @GetMapping("/utilisateur/{utilisateurId}/non-lues")
    public ResponseEntity<?> obtenirNotificationsNonLues(@PathVariable Long utilisateurId) {
        try {
            List<Notification> notifications = notificationService
                    .obtenirNotificationsNonLues(utilisateurId);

            return ResponseEntity.ok(notifications);

        } catch (Exception e) {
            logger.error("Erreur lors de l'obtention des notifications non lues: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Compte les notifications non lues d'un utilisateur
     */
    @GetMapping("/utilisateur/{utilisateurId}/non-lues/count")
    public ResponseEntity<?> compterNotificationsNonLues(@PathVariable Long utilisateurId) {
        try {
            Long count = notificationService.compterNotificationsNonLues(utilisateurId);

            return ResponseEntity.ok(Map.of("count", count));

        } catch (Exception e) {
            logger.error("Erreur lors du comptage des notifications non lues: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Marque une notification comme lue
     */
    @PutMapping("/{notificationId}/marquer-lue")
    public ResponseEntity<?> marquerCommeLue(
            @PathVariable Long notificationId,
            @RequestParam Long utilisateurId) {
        try {
            notificationService.marquerCommeLue(notificationId, utilisateurId);

            return ResponseEntity.ok(Map.of("message", "Notification marquée comme lue"));

        } catch (Exception e) {
            logger.error("Erreur lors du marquage comme lue: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Supprime une notification
     */
    @DeleteMapping("/{notificationId}")
    public ResponseEntity<?> supprimerNotification(
            @PathVariable Long notificationId,
            @RequestParam Long utilisateurId) {
        try {
            notificationService.supprimerNotification(notificationId, utilisateurId);

            return ResponseEntity.ok(Map.of("message", "Notification supprimée avec succès"));

        } catch (Exception e) {
            logger.error("Erreur lors de la suppression de notification: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Envoie une notification de nouveau message
     */
    @PostMapping("/nouveau-message")
    public ResponseEntity<?> notifierNouveauMessage(@RequestBody NouveauMessageNotificationRequest request) {
        try {
            // Cette méthode sera appelée par le MessageService
            // Ici on peut ajouter une validation supplémentaire si nécessaire
            
            notificationService.creerNotification(
                    request.destinataireId(),
                    TypeNotification.NOUVEAU_MESSAGE,
                    CanalNotification.PUSH,
                    "Nouveau message",
                    request.contenu(),
                    PrioriteNotification.NORMALE,
                    null,
                    Map.of(
                            "conversationId", request.conversationId(),
                            "expediteurId", request.expediteurId()
                    )
            );

            return ResponseEntity.ok(Map.of("message", "Notification envoyée"));

        } catch (Exception e) {
            logger.error("Erreur lors de la notification de nouveau message: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Envoie une notification de nouvelle conversation
     */
    @PostMapping("/nouvelle-conversation")
    public ResponseEntity<?> notifierNouvelleConversation(@RequestBody NouvelleConversationNotificationRequest request) {
        try {
            notificationService.notifierNouvelleConversation(request.conversationId(), request.destinataireId());

            return ResponseEntity.ok(Map.of("message", "Notification envoyée"));

        } catch (Exception e) {
            logger.error("Erreur lors de la notification de nouvelle conversation: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Traite les notifications programmées (endpoint pour tâches planifiées)
     */
    @PostMapping("/traiter-programmees")
    public ResponseEntity<?> traiterNotificationsProgrammees() {
        try {
            notificationService.traiterNotificationsProgrammees();

            return ResponseEntity.ok(Map.of("message", "Notifications programmées traitées"));

        } catch (Exception e) {
            logger.error("Erreur lors du traitement des notifications programmées: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Réessaie les notifications en erreur (endpoint pour tâches planifiées)
     */
    @PostMapping("/reessayer-erreurs")
    public ResponseEntity<?> reessayerNotificationsEnErreur() {
        try {
            notificationService.reessayerNotificationsEnErreur();

            return ResponseEntity.ok(Map.of("message", "Notifications en erreur réessayées"));

        } catch (Exception e) {
            logger.error("Erreur lors du réessai des notifications: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Marque les notifications expirées (endpoint pour tâches planifiées)
     */
    @PostMapping("/marquer-expirees")
    public ResponseEntity<?> marquerNotificationsExpirees() {
        try {
            notificationService.marquerNotificationsExpirees();

            return ResponseEntity.ok(Map.of("message", "Notifications expirées marquées"));

        } catch (Exception e) {
            logger.error("Erreur lors du marquage des notifications expirées: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Envoie une notification de test
     */
    @PostMapping("/test")
    public ResponseEntity<?> envoyerNotificationTest(@RequestBody NotificationTestRequest request) {
        try {
            Notification notification = notificationService.creerNotification(
                    request.utilisateurId(),
                    TypeNotification.SYSTEME,
                    request.canal(),
                    "Notification de test",
                    "Ceci est une notification de test de SprintBot",
                    PrioriteNotification.BASSE,
                    null,
                    Map.of("test", true)
            );

            return ResponseEntity.ok(notification);

        } catch (Exception e) {
            logger.error("Erreur lors de l'envoi de notification de test: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    // Records pour les requêtes
    public record CreerNotificationRequest(
            Long destinataireId,
            TypeNotification type,
            CanalNotification canal,
            String titre,
            String contenu,
            PrioriteNotification priorite,
            LocalDateTime dateProgrammee,
            Map<String, Object> donnees
    ) {}

    public record NouveauMessageNotificationRequest(
            Long destinataireId,
            Long conversationId,
            Long expediteurId,
            String contenu
    ) {}

    public record NouvelleConversationNotificationRequest(
            Long conversationId,
            Long destinataireId
    ) {}

    public record NotificationTestRequest(
            Long utilisateurId,
            CanalNotification canal
    ) {}
}
