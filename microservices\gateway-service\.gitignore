# Gateway Service - SprintBot
# Fichiers à ignorer par Git

# ============================================================================
# FICHIERS DE CONFIGURATION SENSIBLES
# ============================================================================

# Variables d'environnement avec secrets
.env
.env.local
.env.production
.env.staging

# Fichiers de configuration avec mots de passe
application-local.yml
application-secret.yml
config/secrets/

# Clés JWT et certificats
*.key
*.pem
*.p12
*.jks
keystore/
secrets/

# ============================================================================
# FICHIERS MAVEN
# ============================================================================

# Répertoire target
backend/target/
backend/.mvn/wrapper/maven-wrapper.jar

# Fichiers de cache Maven
.m2/
*.versionsBackup

# Fichiers de build
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

# ============================================================================
# FICHIERS IDE
# ============================================================================

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/

# Eclipse
.project
.classpath
.c9/
*.launch
.settings/
.metadata
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.loadpath
.recommenders

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# Visual Studio Code
.vscode/
*.code-workspace

# Sublime Text
*.sublime-workspace
*.sublime-project

# ============================================================================
# FICHIERS SYSTÈME
# ============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ============================================================================
# LOGS ET DONNÉES TEMPORAIRES
# ============================================================================

# Logs
logs/
*.log
*.log.*
*.out
*.err

# Données temporaires
tmp/
temp/
cache/
.cache/

# Fichiers de sauvegarde
*.backup
*.bak
*.orig

# ============================================================================
# DOCKER
# ============================================================================

# Fichiers Docker temporaires
.dockerignore.bak
docker-compose.override.yml
docker-compose.local.yml

# Volumes Docker locaux
volumes/
data/

# ============================================================================
# TESTS
# ============================================================================

# Rapports de tests
test-results/
coverage/
.nyc_output/
junit.xml
*.lcov

# Fichiers de test temporaires
test-output/
test-tmp/

# ============================================================================
# MONITORING ET MÉTRIQUES
# ============================================================================

# Fichiers de métriques
metrics/
prometheus/
grafana/

# Traces
traces/
zipkin/

# ============================================================================
# SÉCURITÉ
# ============================================================================

# Fichiers de sécurité
security/
ssl/
tls/

# Tokens et clés
*.token
*.secret
auth/

# ============================================================================
# DÉVELOPPEMENT
# ============================================================================

# Fichiers de développement local
dev/
local/
sandbox/

# Scripts personnels
scripts/local/
*.local.sh

# Configuration de développement
application-dev-local.yml
logback-dev.xml

# ============================================================================
# PRODUCTION
# ============================================================================

# Fichiers de production
prod/
production/

# Sauvegardes de production
*.prod.backup
*.production.backup

# ============================================================================
# OUTILS DE BUILD
# ============================================================================

# Gradle
.gradle/
build/
gradle-app.setting
!gradle-wrapper.jar
!gradle-wrapper.properties

# SBT
.sbt/
project/target/
target/

# ============================================================================
# AUTRES
# ============================================================================

# Fichiers temporaires de l'éditeur
*.swp
*.swo
*~

# Fichiers de verrouillage
*.lock

# Fichiers de configuration personnalisés
config.local.*
settings.local.*

# Données de test
test-data/
mock-data/

# Documentation générée
docs/generated/
api-docs/

# Fichiers de profiling
*.hprof
*.prof

# ============================================================================
# EXCEPTIONS (FICHIERS À INCLURE)
# ============================================================================

# Inclure les wrappers Maven
!.mvn/wrapper/maven-wrapper.jar
!.mvn/wrapper/maven-wrapper.properties

# Inclure les fichiers d'exemple
!.env.example
!docker-compose.example.yml

# Inclure la documentation
!README.md
!DEPLOYMENT.md
!COMPLETION-SUMMARY.md
