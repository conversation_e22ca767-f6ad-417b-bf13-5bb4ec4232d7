# Configuration principale du Medical-Admin-Service
spring:
  application:
    name: medical-admin-service
  
  profiles:
    active: dev
  
  # Configuration JPA
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
    open-in-view: false
  
  # Configuration Jackson
  jackson:
    serialization:
      write-dates-as-timestamps: false
    time-zone: Europe/Paris
    default-property-inclusion: non_null

# Configuration du serveur
server:
  port: 8083

# Configuration Eureka Client
eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_CLIENT_SERVICE_URL:http://localhost:8761/eureka/}
    register-with-eureka: true
    fetch-registry: true
    registry-fetch-interval-seconds: 30
  instance:
    hostname: ${EUREKA_INSTANCE_HOSTNAME:localhost}
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90
    metadata-map:
      version: "1.0.0"
      description: "Medical Admin Service - Gestion médicale et administrative"
      team: "SprintBot"
  servlet:
    context-path: /
  error:
    include-message: always
    include-binding-errors: always

# Configuration de la sécurité JWT
jwt:
  secret: ${JWT_SECRET:sprintbot-medical-admin-secret-key-2024-very-long-and-secure}
  expiration: 86400000  # 24 heures en millisecondes
  refresh-expiration: 604800000  # 7 jours en millisecondes

# Configuration CORS
cors:
  allowed-origins:
    - http://localhost:4200
    - http://localhost:3000
    - https://sprintbot.com
  allowed-methods:
    - GET
    - POST
    - PUT
    - PATCH
    - DELETE
    - OPTIONS
  allowed-headers:
    - "*"
  allow-credentials: true

# Configuration Actuator
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  info:
    env:
      enabled: true

# Informations de l'application
info:
  app:
    name: Medical-Admin-Service
    description: Microservice pour la gestion médicale et administrative
    version: 1.0.0
    team: SprintBot Development Team
  build:
    artifact: medical-admin-service
    group: com.sprintbot

# Configuration des logs
logging:
  level:
    com.sprintbot.medicaladmin: INFO
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/medical-admin-service.log

---
# Profil de développement
spring:
  config:
    activate:
      on-profile: dev
  
  # Base de données de développement
  datasource:
    url: *****************************************************************************
    username: sprintbot_user
    password: sprintbot_password
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000

  # Configuration JPA pour développement
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: true
    properties:
      hibernate:
        default_schema: medical_admin

  # Configuration Flyway
  flyway:
    enabled: true
    locations: classpath:db/migration
    schemas: medical_admin
    baseline-on-migrate: true

# Configuration des logs pour développement
logging:
  level:
    com.sprintbot.medicaladmin: DEBUG
    org.springframework.web: DEBUG

---
# Profil Docker
spring:
  config:
    activate:
      on-profile: docker
  
  # Base de données Docker
  datasource:
    url: ${SPRING_DATASOURCE_URL:************************************************************************************}
    username: ${SPRING_DATASOURCE_USERNAME:medical_admin_user}
    password: ${SPRING_DATASOURCE_PASSWORD:medical_admin_password}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 10
      idle-timeout: 300000
      connection-timeout: 20000

  # Configuration JPA pour Docker
  jpa:
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        default_schema: medical_admin

# Configuration Eureka pour Docker
eureka:
  client:
    service-url:
      defaultZone: http://discovery-service:8761/eureka/
  instance:
    hostname: medical-admin-service
    prefer-ip-address: true

  # Configuration Flyway pour Docker
  flyway:
    enabled: true
    locations: classpath:db/migration
    schemas: medical_admin
    baseline-on-migrate: true

# Configuration JWT pour Docker
jwt:
  secret: ${JWT_SECRET:sprintbot-medical-admin-secret-key-docker-2024}

---
# Profil de production
spring:
  config:
    activate:
      on-profile: prod
  
  # Base de données de production
  datasource:
    url: ${DATABASE_URL:*****************************************************************************}
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 50
      minimum-idle: 20
      idle-timeout: 600000
      connection-timeout: 30000

  # Configuration JPA pour production
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        default_schema: medical_admin

  # Configuration Flyway pour production
  flyway:
    enabled: true
    locations: classpath:db/migration
    schemas: medical_admin
    baseline-on-migrate: true

# Configuration JWT pour production
jwt:
  secret: ${JWT_SECRET}

# Configuration des logs pour production
logging:
  level:
    com.sprintbot.medicaladmin: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
  file:
    name: /var/log/medical-admin-service/application.log
