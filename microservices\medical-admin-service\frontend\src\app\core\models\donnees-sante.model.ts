export interface DonneesSante {
  id?: number;
  joueurId: number;
  staffMedicalId?: number;
  typeExamen: TypeExamen;
  dateExamen: string;
  resultats?: string;
  recommandations?: string;
  blessures?: string;
  traitements?: string;
  medicaments?: string;
  statut: StatutDonneesSante;
  gravite?: GraviteDonneesSante;
  dateGuerisonPrevue?: string;
  necessiteSuivi: boolean;
  visibleParJoueur: boolean;
  notesConfidentielles?: string;
  createdAt?: string;
  updatedAt?: string;
}

export enum TypeExamen {
  BILAN_GENERAL = 'BILAN_GENERAL',
  BLESSURE = 'BLESSURE',
  SUIVI = 'SUIVI',
  PREVENTION = 'PREVENTION'
}

export enum StatutDonneesSante {
  ACTIF = 'ACTIF',
  GUERI = 'GUERI',
  EN_TRAITEMENT = 'EN_TRAITEMENT',
  SUIVI = 'SUIVI'
}

export enum GraviteDonneesSante {
  FAIBLE = 'FAIBLE',
  MOYENNE = 'MOYENNE',
  ELEVEE = 'ELEVEE',
  CRITIQUE = 'CRITIQUE'
}

export interface DonneesSanteFilters {
  joueurId?: number;
  staffMedicalId?: number;
  typeExamen?: TypeExamen;
  statut?: StatutDonneesSante;
  gravite?: GraviteDonneesSante;
  dateDebut?: string;
  dateFin?: string;
  necessiteSuivi?: boolean;
}

export interface DonneesSanteStats {
  totalExamens: number;
  examensParType: { [key: string]: number };
  examensParStatut: { [key: string]: number };
  examensParGravite: { [key: string]: number };
  tendancesMensuelles: { mois: string; nombre: number }[];
}
