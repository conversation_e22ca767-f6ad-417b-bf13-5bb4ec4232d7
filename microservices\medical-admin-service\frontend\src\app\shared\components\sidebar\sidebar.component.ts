import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MenuItem } from '@core/models/common.model';

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="sidebar">
      <div class="sidebar-header">
        <h6 class="text-muted mb-0">Navigation</h6>
      </div>
      
      <nav class="sidebar-nav">
        <ul class="nav flex-column">
          <li class="nav-item" *ngFor="let item of menuItems">
            <a class="nav-link" 
               [routerLink]="item.route" 
               routerLinkActive="active"
               [class.has-children]="item.children && item.children.length > 0">
              <i [class]="item.icon + ' me-2'"></i>
              {{ item.label }}
              <span *ngIf="item.badge" [class]="'badge ms-auto ' + (item.badgeClass || 'bg-primary')">
                {{ item.badge }}
              </span>
            </a>
            
            <!-- Sous-menu -->
            <ul class="nav flex-column ms-3" *ngIf="item.children && item.children.length > 0">
              <li class="nav-item" *ngFor="let child of item.children">
                <a class="nav-link nav-link-child" 
                   [routerLink]="child.route" 
                   routerLinkActive="active">
                  <i [class]="child.icon + ' me-2'"></i>
                  {{ child.label }}
                  <span *ngIf="child.badge" [class]="'badge ms-auto ' + (child.badgeClass || 'bg-primary')">
                    {{ child.badge }}
                  </span>
                </a>
              </li>
            </ul>
          </li>
        </ul>
      </nav>
    </div>
  `,
  styles: [`
    .sidebar {
      height: 100%;
      background-color: #f8f9fa;
      border-right: 1px solid #dee2e6;
      overflow-y: auto;
    }
    
    .sidebar-header {
      padding: 1rem 1.5rem;
      border-bottom: 1px solid #dee2e6;
      background-color: white;
    }
    
    .sidebar-nav {
      padding: 1rem 0;
    }
    
    .nav-link {
      color: #495057;
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 0;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
    }
    
    .nav-link:hover {
      background-color: #e9ecef;
      color: var(--primary-color);
    }
    
    .nav-link.active {
      background-color: var(--primary-color);
      color: white;
      font-weight: 500;
    }
    
    .nav-link-child {
      font-size: 0.875rem;
      padding: 0.5rem 1.5rem;
    }
    
    .badge {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
    }
    
    .has-children::after {
      content: '\\f107';
      font-family: 'Font Awesome 5 Free';
      font-weight: 900;
      margin-left: auto;
      transition: transform 0.3s ease;
    }
    
    .has-children.active::after {
      transform: rotate(180deg);
    }
    
    @media (max-width: 768px) {
      .sidebar {
        position: fixed;
        top: 0;
        left: -250px;
        width: 250px;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease;
      }
      
      .sidebar.show {
        left: 0;
      }
    }
  `]
})
export class SidebarComponent {
  menuItems: MenuItem[] = [
    {
      label: 'Tableau de bord',
      icon: 'fas fa-tachometer-alt',
      route: '/dashboard'
    },
    {
      label: 'Données de santé',
      icon: 'fas fa-notes-medical',
      route: '/donnees-sante',
      children: [
        {
          label: 'Toutes les données',
          icon: 'fas fa-list',
          route: '/donnees-sante'
        },
        {
          label: 'Nouvelle donnée',
          icon: 'fas fa-plus',
          route: '/donnees-sante/nouveau'
        },
        {
          label: 'Blessures actives',
          icon: 'fas fa-exclamation-triangle',
          route: '/donnees-sante/blessures',
          badge: '3',
          badgeClass: 'bg-warning'
        }
      ]
    },
    {
      label: 'Rendez-vous',
      icon: 'fas fa-calendar-alt',
      route: '/rendez-vous',
      children: [
        {
          label: 'Planning',
          icon: 'fas fa-calendar',
          route: '/rendez-vous'
        },
        {
          label: 'Nouveau RDV',
          icon: 'fas fa-plus',
          route: '/rendez-vous/nouveau'
        },
        {
          label: 'Aujourd\'hui',
          icon: 'fas fa-clock',
          route: '/rendez-vous/aujourd-hui',
          badge: '5',
          badgeClass: 'bg-info'
        }
      ]
    },
    {
      label: 'Demandes admin',
      icon: 'fas fa-file-alt',
      route: '/demandes-administratives',
      children: [
        {
          label: 'Toutes les demandes',
          icon: 'fas fa-list',
          route: '/demandes-administratives'
        },
        {
          label: 'Nouvelle demande',
          icon: 'fas fa-plus',
          route: '/demandes-administratives/nouveau'
        },
        {
          label: 'En attente',
          icon: 'fas fa-hourglass-half',
          route: '/demandes-administratives/en-attente',
          badge: '7',
          badgeClass: 'bg-warning'
        },
        {
          label: 'Urgentes',
          icon: 'fas fa-exclamation',
          route: '/demandes-administratives/urgentes',
          badge: '2',
          badgeClass: 'bg-danger'
        }
      ]
    },
    {
      label: 'Rapports',
      icon: 'fas fa-chart-bar',
      route: '/rapports',
      children: [
        {
          label: 'Statistiques',
          icon: 'fas fa-chart-pie',
          route: '/rapports/statistiques'
        },
        {
          label: 'Export données',
          icon: 'fas fa-download',
          route: '/rapports/export'
        }
      ]
    }
  ];
}
