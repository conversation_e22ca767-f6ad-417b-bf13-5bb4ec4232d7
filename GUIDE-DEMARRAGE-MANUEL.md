# 🏐 Guide de Démarrage Manuel - Club Olympique de Kelibia

## 🎯 Objectif
D<PERSON>rer tous les microservices manuellement pour tester l'intégration complète.

## 📋 Architecture des Services

```
Frontend Angular (4201)
         ↓
Gateway Service (8080)
         ↓
┌─────────────────┬─────────────────┐
│  Auth Service   │ Planning Service │
│    (8081)       │     (8082)      │
└─────────────────┴─────────────────┘
         ↓                 ↓
Discovery Service (8761)
```

## 🚀 Ordre de Démarrage

### 1. Discovery Service (Port 8761)
```bash
cd microservices/discovery-service/backend
mvn spring-boot:run
```
**Vérification :** http://localhost:8761

### 2. Gateway Service (Port 8080)
```bash
cd microservices/gateway-service/backend
mvn spring-boot:run
```
**Vérification :** http://localhost:8080/actuator/health

### 3. Auth User Service (Port 8081)
```bash
cd microservices/auth-user-service/backend
mvn spring-boot:run
```
**Vérification :** http://localhost:8081/actuator/health

### 4. Planning Performance Service (Port 8082)
```bash
cd microservices/planning-performance-service/backend
mvn spring-boot:run
```
**Vérification :** http://localhost:8082/actuator/health

### 5. Frontend Angular (Port 4201)
```bash
cd microservices/auth-user-service/frontend
npm start
```
**Vérification :** http://localhost:4201

## ✅ Tests de Validation

### Test 1 : Services Opérationnels
- [ ] Discovery Service : http://localhost:8761
- [ ] Gateway Service : http://localhost:8080/actuator/health
- [ ] Auth Service : http://localhost:8081/actuator/health
- [ ] Planning Service : http://localhost:8082/actuator/health
- [ ] Frontend : http://localhost:4201

### Test 2 : Enregistrement dans Discovery
Vérifiez sur http://localhost:8761 que tous les services sont enregistrés :
- [ ] GATEWAY-SERVICE
- [ ] AUTH-USER-SERVICE
- [ ] PLANNING-PERFORMANCE-SERVICE

### Test 3 : Authentification
1. Créer un utilisateur via l'API :
```bash
curl -X POST http://localhost:8080/auth-user-service/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "TestPassword123!",
    "nom": "Test",
    "prenom": "User",
    "role": "JOUEUR"
  }'
```

2. Se connecter :
```bash
curl -X POST http://localhost:8080/auth-user-service/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "TestPassword123!"
  }'
```

### Test 4 : API Planning Performance
Avec le token JWT obtenu :
```bash
curl -X GET http://localhost:8080/planning-performance-service/api/entrainements \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Test 5 : Frontend Intégré
1. Accéder à http://localhost:4201
2. Se connecter avec les identifiants créés
3. Naviguer vers le module Planning
4. Vérifier que les données s'affichent

## 🔧 Dépannage

### Problème : Service ne démarre pas
- Vérifiez que le port n'est pas déjà utilisé
- Vérifiez les logs de démarrage
- Assurez-vous que Java 21 est installé

### Problème : Service non enregistré dans Discovery
- Vérifiez que Discovery Service est démarré en premier
- Vérifiez la configuration `eureka.client.service-url.defaultZone`

### Problème : Erreur CORS
- Vérifiez la configuration CORS dans Gateway Service
- Assurez-vous que le frontend utilise le bon port Gateway

### Problème : Authentification échoue
- Vérifiez que la base de données est initialisée
- Vérifiez les logs du service Auth User

## 📊 Commandes de Test Rapide

### PowerShell (Windows)
```powershell
# Test tous les services
.\test-final.ps1
```

### Bash (Linux/Mac)
```bash
# Test Discovery
curl -s http://localhost:8761/actuator/health | jq .

# Test Gateway
curl -s http://localhost:8080/actuator/health | jq .

# Test Auth
curl -s http://localhost:8081/actuator/health | jq .

# Test Planning
curl -s http://localhost:8082/actuator/health | jq .
```

## 🎉 Succès Attendu

Quand tout fonctionne :
- ✅ 5/5 services opérationnels
- ✅ Tous les services enregistrés dans Discovery
- ✅ Authentification fonctionnelle
- ✅ APIs accessibles via Gateway
- ✅ Frontend connecté et fonctionnel

## 🏐 Club Olympique de Kelibia
**Plateforme Intelligente de Gestion d'Équipe de Volley-Ball**

---
*Guide créé pour l'intégration complète des microservices*
