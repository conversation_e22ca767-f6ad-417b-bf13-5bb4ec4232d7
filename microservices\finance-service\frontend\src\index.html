<!doctype html>
<html lang="fr">
<head>
  <meta charset="utf-8">
  <title>SprintBot Finance - Gestion Financière</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Application de gestion financière pour SprintBot - Suivi bud<PERSON>taire, revenus/dépenses, sponsors et salaires">
  <meta name="keywords" content="finance, budget, comptabilité, gestion, volleyball, sport">
  <meta name="author" content="SprintBot Team">
  
  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  
  <!-- Preconnect pour optimiser les performances -->
  <link rel="preconnect" href="https://fonts.gstatic.com">
  
  <!-- Google Fonts - Material Icons et Roboto -->
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  
  <!-- Thème Angular Material -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
  
  <!-- Meta tags pour PWA (optionnel) -->
  <meta name="theme-color" content="#1976d2">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="apple-mobile-web-app-title" content="SprintBot Finance">
  
  <!-- Styles de base pour éviter le FOUC (Flash of Unstyled Content) -->
  <style>
    body {
      margin: 0;
      font-family: Roboto, "Helvetica Neue", sans-serif;
      background-color: #fafafa;
    }
    
    /* Loading initial */
    .initial-loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: #1976d2;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: white;
      z-index: 9999;
    }
    
    .initial-loading .logo {
      font-size: 48px;
      margin-bottom: 20px;
    }
    
    .initial-loading .title {
      font-size: 24px;
      font-weight: 300;
      margin-bottom: 30px;
    }
    
    .initial-loading .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-top: 4px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    /* Cache le loading initial une fois Angular chargé */
    app-root:not(:empty) + .initial-loading {
      display: none;
    }
  </style>
</head>
<body class="mat-typography">
  <!-- Composant racine Angular -->
  <app-root></app-root>
  
  <!-- Loading initial affiché pendant le chargement d'Angular -->
  <div class="initial-loading">
    <div class="logo">💰</div>
    <div class="title">SprintBot Finance</div>
    <div class="spinner"></div>
  </div>
  
  <!-- Script pour masquer le loading initial en cas d'erreur -->
  <script>
    // Masque le loading initial après 10 secondes maximum
    setTimeout(function() {
      const loading = document.querySelector('.initial-loading');
      if (loading) {
        loading.style.display = 'none';
      }
    }, 10000);
  </script>
</body>
</html>
