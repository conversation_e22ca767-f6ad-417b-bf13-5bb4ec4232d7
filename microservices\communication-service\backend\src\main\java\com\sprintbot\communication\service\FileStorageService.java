package com.sprintbot.communication.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * Service pour la gestion du stockage de fichiers
 */
@Service
public class FileStorageService {

    private static final Logger logger = LoggerFactory.getLogger(FileStorageService.class);

    @Value("${app.file.upload-dir:uploads}")
    private String uploadDir;

    @Value("${app.file.max-size:10485760}") // 10MB par défaut
    private long maxFileSize;

    @Value("${app.file.allowed-types:jpg,jpeg,png,gif,pdf,doc,docx,txt,mp4,mp3}")
    private String allowedTypes;

    private static final List<String> ALLOWED_IMAGE_TYPES = Arrays.asList(
            "image/jpeg", "image/jpg", "image/png", "image/gif"
    );

    private static final List<String> ALLOWED_VIDEO_TYPES = Arrays.asList(
            "video/mp4", "video/avi", "video/mov", "video/wmv"
    );

    private static final List<String> ALLOWED_AUDIO_TYPES = Arrays.asList(
            "audio/mp3", "audio/wav", "audio/ogg", "audio/m4a"
    );

    private static final List<String> ALLOWED_DOCUMENT_TYPES = Arrays.asList(
            "application/pdf", "application/msword", 
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "text/plain"
    );

    /**
     * Sauvegarde un fichier uploadé
     */
    public String sauvegarderFichier(MultipartFile fichier) {
        try {
            // Valider le fichier
            validerFichier(fichier);

            // Créer le répertoire s'il n'existe pas
            Path uploadPath = Paths.get(uploadDir);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }

            // Générer un nom unique pour le fichier
            String nomFichier = genererNomFichierUnique(fichier.getOriginalFilename());
            
            // Créer le chemin complet
            Path cheminFichier = uploadPath.resolve(nomFichier);

            // Copier le fichier
            Files.copy(fichier.getInputStream(), cheminFichier, StandardCopyOption.REPLACE_EXISTING);

            logger.info("Fichier sauvegardé: {} (taille: {} bytes)", nomFichier, fichier.getSize());

            // Retourner l'URL relative
            return "/files/" + nomFichier;

        } catch (IOException e) {
            logger.error("Erreur lors de la sauvegarde du fichier: {}", e.getMessage(), e);
            throw new RuntimeException("Erreur lors de la sauvegarde du fichier: " + e.getMessage());
        }
    }

    /**
     * Valide un fichier uploadé
     */
    private void validerFichier(MultipartFile fichier) {
        if (fichier.isEmpty()) {
            throw new RuntimeException("Le fichier est vide");
        }

        // Vérifier la taille
        if (fichier.getSize() > maxFileSize) {
            throw new RuntimeException("Le fichier est trop volumineux. Taille maximum: " + 
                                     (maxFileSize / 1024 / 1024) + "MB");
        }

        // Vérifier le type
        String contentType = fichier.getContentType();
        if (contentType == null || !estTypeAutorise(contentType)) {
            throw new RuntimeException("Type de fichier non autorisé: " + contentType);
        }

        // Vérifier l'extension
        String nomFichier = fichier.getOriginalFilename();
        if (nomFichier == null || !aExtensionAutorisee(nomFichier)) {
            throw new RuntimeException("Extension de fichier non autorisée");
        }
    }

    /**
     * Vérifie si le type MIME est autorisé
     */
    private boolean estTypeAutorise(String contentType) {
        return ALLOWED_IMAGE_TYPES.contains(contentType) ||
               ALLOWED_VIDEO_TYPES.contains(contentType) ||
               ALLOWED_AUDIO_TYPES.contains(contentType) ||
               ALLOWED_DOCUMENT_TYPES.contains(contentType);
    }

    /**
     * Vérifie si l'extension est autorisée
     */
    private boolean aExtensionAutorisee(String nomFichier) {
        String extension = obtenirExtension(nomFichier).toLowerCase();
        List<String> extensionsAutorisees = Arrays.asList(allowedTypes.split(","));
        return extensionsAutorisees.contains(extension);
    }

    /**
     * Génère un nom de fichier unique
     */
    private String genererNomFichierUnique(String nomOriginal) {
        String extension = obtenirExtension(nomOriginal);
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String uuid = UUID.randomUUID().toString().substring(0, 8);
        
        return timestamp + "_" + uuid + "." + extension;
    }

    /**
     * Obtient l'extension d'un fichier
     */
    private String obtenirExtension(String nomFichier) {
        if (nomFichier == null || !nomFichier.contains(".")) {
            return "";
        }
        return nomFichier.substring(nomFichier.lastIndexOf(".") + 1);
    }

    /**
     * Supprime un fichier
     */
    public boolean supprimerFichier(String urlFichier) {
        try {
            if (urlFichier == null || !urlFichier.startsWith("/files/")) {
                return false;
            }

            String nomFichier = urlFichier.substring("/files/".length());
            Path cheminFichier = Paths.get(uploadDir).resolve(nomFichier);

            if (Files.exists(cheminFichier)) {
                Files.delete(cheminFichier);
                logger.info("Fichier supprimé: {}", nomFichier);
                return true;
            }

            return false;

        } catch (IOException e) {
            logger.error("Erreur lors de la suppression du fichier {}: {}", urlFichier, e.getMessage());
            return false;
        }
    }

    /**
     * Vérifie si un fichier existe
     */
    public boolean fichierExiste(String urlFichier) {
        if (urlFichier == null || !urlFichier.startsWith("/files/")) {
            return false;
        }

        String nomFichier = urlFichier.substring("/files/".length());
        Path cheminFichier = Paths.get(uploadDir).resolve(nomFichier);
        
        return Files.exists(cheminFichier);
    }

    /**
     * Obtient la taille d'un fichier
     */
    public long obtenirTailleFichier(String urlFichier) {
        try {
            if (urlFichier == null || !urlFichier.startsWith("/files/")) {
                return 0;
            }

            String nomFichier = urlFichier.substring("/files/".length());
            Path cheminFichier = Paths.get(uploadDir).resolve(nomFichier);

            if (Files.exists(cheminFichier)) {
                return Files.size(cheminFichier);
            }

            return 0;

        } catch (IOException e) {
            logger.error("Erreur lors de l'obtention de la taille du fichier {}: {}", 
                        urlFichier, e.getMessage());
            return 0;
        }
    }

    /**
     * Nettoie les fichiers orphelins (non référencés)
     */
    public void nettoyerFichiersOrphelins() {
        try {
            Path uploadPath = Paths.get(uploadDir);
            if (!Files.exists(uploadPath)) {
                return;
            }

            // TODO: Implémenter la logique de nettoyage
            // - Parcourir tous les fichiers dans le répertoire
            // - Vérifier s'ils sont référencés dans la base de données
            // - Supprimer ceux qui ne le sont pas et qui sont anciens

            logger.info("Nettoyage des fichiers orphelins terminé");

        } catch (Exception e) {
            logger.error("Erreur lors du nettoyage des fichiers orphelins: {}", e.getMessage());
        }
    }

    /**
     * Obtient les statistiques de stockage
     */
    public java.util.Map<String, Object> obtenirStatistiquesStockage() {
        java.util.Map<String, Object> stats = new java.util.HashMap<>();
        
        try {
            Path uploadPath = Paths.get(uploadDir);
            if (!Files.exists(uploadPath)) {
                stats.put("nombreFichiers", 0);
                stats.put("tailleTotal", 0L);
                return stats;
            }

            long nombreFichiers = Files.list(uploadPath).count();
            long tailleTotal = Files.walk(uploadPath)
                    .filter(Files::isRegularFile)
                    .mapToLong(path -> {
                        try {
                            return Files.size(path);
                        } catch (IOException e) {
                            return 0;
                        }
                    })
                    .sum();

            stats.put("nombreFichiers", nombreFichiers);
            stats.put("tailleTotal", tailleTotal);
            stats.put("tailleTotalMB", tailleTotal / 1024.0 / 1024.0);

        } catch (IOException e) {
            logger.error("Erreur lors de l'obtention des statistiques: {}", e.getMessage());
            stats.put("erreur", e.getMessage());
        }

        return stats;
    }
}
