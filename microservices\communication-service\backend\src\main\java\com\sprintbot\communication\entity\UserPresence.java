package com.sprintbot.communication.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * Entité représentant la présence d'un utilisateur (en ligne/hors ligne)
 */
@Entity
@Table(name = "user_presence")
@EntityListeners(AuditingEntityListener.class)
public class UserPresence {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "L'ID de l'utilisateur est obligatoire")
    @Column(name = "utilisateur_id", nullable = false, unique = true)
    private Long utilisateurId;

    @NotNull(message = "Le statut est obligatoire")
    @Enumerated(EnumType.STRING)
    @Column(name = "statut", nullable = false, length = 20)
    private StatutPresence statut = StatutPresence.HORS_LIGNE;

    @Column(name = "message_statut")
    private String messageStatut; // Message personnalisé de statut

    @Column(name = "derniere_activite")
    private LocalDateTime derniereActivite;

    @Column(name = "session_id")
    private String sessionId; // ID de session WebSocket

    @Column(name = "adresse_ip")
    private String adresseIp;

    @Column(name = "user_agent")
    private String userAgent;

    @Column(name = "plateforme")
    private String plateforme; // web, mobile, desktop

    @Column(name = "est_mobile")
    private Boolean estMobile = false;

    @Column(name = "timezone")
    private String timezone;

    @Column(name = "invisible")
    private Boolean invisible = false; // Mode invisible

    @CreatedDate
    @Column(name = "date_creation", nullable = false)
    private LocalDateTime dateCreation;

    @LastModifiedDate
    @Column(name = "date_modification")
    private LocalDateTime dateModification;

    // Constructeurs
    public UserPresence() {}

    public UserPresence(Long utilisateurId) {
        this.utilisateurId = utilisateurId;
        this.statut = StatutPresence.HORS_LIGNE;
        this.derniereActivite = LocalDateTime.now();
    }

    public UserPresence(Long utilisateurId, StatutPresence statut) {
        this.utilisateurId = utilisateurId;
        this.statut = statut;
        this.derniereActivite = LocalDateTime.now();
    }

    // Méthodes métier
    public void marquerEnLigne(String sessionId) {
        this.statut = StatutPresence.EN_LIGNE;
        this.sessionId = sessionId;
        this.derniereActivite = LocalDateTime.now();
    }

    public void marquerHorsLigne() {
        this.statut = StatutPresence.HORS_LIGNE;
        this.sessionId = null;
        this.derniereActivite = LocalDateTime.now();
    }

    public void marquerAbsent() {
        this.statut = StatutPresence.ABSENT;
        this.derniereActivite = LocalDateTime.now();
    }

    public void marquerOccupe() {
        this.statut = StatutPresence.OCCUPE;
        this.derniereActivite = LocalDateTime.now();
    }

    public void mettreAJourActivite() {
        this.derniereActivite = LocalDateTime.now();
    }

    public void mettreAJourStatut(StatutPresence nouveauStatut) {
        this.statut = nouveauStatut;
        this.derniereActivite = LocalDateTime.now();
    }

    public void changerMessageStatut(String message) {
        this.messageStatut = message;
    }

    public void activerModeInvisible() {
        this.invisible = true;
    }

    public void desactiverModeInvisible() {
        this.invisible = false;
    }

    public boolean estEnLigne() {
        return this.statut == StatutPresence.EN_LIGNE;
    }

    public boolean estHorsLigne() {
        return this.statut == StatutPresence.HORS_LIGNE;
    }

    public boolean estAbsent() {
        return this.statut == StatutPresence.ABSENT;
    }

    public boolean estOccupe() {
        return this.statut == StatutPresence.OCCUPE;
    }

    public boolean estVisible() {
        return !this.invisible;
    }

    public boolean estActifRecemment() {
        if (this.derniereActivite == null) return false;
        return this.derniereActivite.isAfter(LocalDateTime.now().minusMinutes(5));
    }

    // Getters et Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUtilisateurId() {
        return utilisateurId;
    }

    public void setUtilisateurId(Long utilisateurId) {
        this.utilisateurId = utilisateurId;
    }

    public StatutPresence getStatut() {
        return statut;
    }

    public void setStatut(StatutPresence statut) {
        this.statut = statut;
    }

    public String getMessageStatut() {
        return messageStatut;
    }

    public void setMessageStatut(String messageStatut) {
        this.messageStatut = messageStatut;
    }

    public LocalDateTime getDerniereActivite() {
        return derniereActivite;
    }

    public void setDerniereActivite(LocalDateTime derniereActivite) {
        this.derniereActivite = derniereActivite;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getAdresseIp() {
        return adresseIp;
    }

    public void setAdresseIp(String adresseIp) {
        this.adresseIp = adresseIp;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getPlateforme() {
        return plateforme;
    }

    public void setPlateforme(String plateforme) {
        this.plateforme = plateforme;
    }

    public Boolean getEstMobile() {
        return estMobile;
    }

    public void setEstMobile(Boolean estMobile) {
        this.estMobile = estMobile;
    }

    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }

    public Boolean getInvisible() {
        return invisible;
    }

    public void setInvisible(Boolean invisible) {
        this.invisible = invisible;
    }

    public LocalDateTime getDateCreation() {
        return dateCreation;
    }

    public void setDateCreation(LocalDateTime dateCreation) {
        this.dateCreation = dateCreation;
    }

    public LocalDateTime getDateModification() {
        return dateModification;
    }

    public void setDateModification(LocalDateTime dateModification) {
        this.dateModification = dateModification;
    }

    @Override
    public String toString() {
        return "UserPresence{" +
                "id=" + id +
                ", utilisateurId=" + utilisateurId +
                ", statut=" + statut +
                ", derniereActivite=" + derniereActivite +
                ", sessionId='" + sessionId + '\'' +
                ", invisible=" + invisible +
                '}';
    }
}
