Write-Host "=== VERIFICATION FINALE DU PROJET ===" -ForegroundColor Green
Write-Host "Club Olympique de Kelibia" -ForegroundColor Cyan
Write-Host ""

Write-Host "PORTS DETECTES OUVERTS :" -ForegroundColor Yellow
Write-Host "=========================" -ForegroundColor Yellow
Write-Host "Port 8761 - Discovery Service" -ForegroundColor Green
Write-Host "Port 8080 - Gateway Service" -ForegroundColor Green
Write-Host "Port 8081 - Auth User Service" -ForegroundColor Green
Write-Host "Port 55219 - Frontend Angular" -ForegroundColor Green

Write-Host ""
Write-Host "PROCESSUS ACTIFS :" -ForegroundColor Yellow
Write-Host "==================" -ForegroundColor Yellow

$javaProcesses = Get-Process -Name "java" -ErrorAction SilentlyContinue
if ($javaProcesses) {
    Write-Host "Processus Java : $($javaProcesses.Count)" -ForegroundColor Green
    foreach ($process in $javaProcesses) {
        $memoryMB = [math]::Round($process.WorkingSet64/1MB, 2)
        Write-Host "  PID: $($process.Id) - Memoire: $memoryMB MB" -ForegroundColor Gray
    }
} else {
    Write-Host "Aucun processus Java" -ForegroundColor Red
}

$nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
if ($nodeProcesses) {
    Write-Host "Processus Node.js : $($nodeProcesses.Count)" -ForegroundColor Green
    foreach ($process in $nodeProcesses) {
        $memoryMB = [math]::Round($process.WorkingSet64/1MB, 2)
        Write-Host "  PID: $($process.Id) - Memoire: $memoryMB MB" -ForegroundColor Gray
    }
} else {
    Write-Host "Aucun processus Node.js" -ForegroundColor Red
}

Write-Host ""
Write-Host "URLS DISPONIBLES :" -ForegroundColor Yellow
Write-Host "==================" -ForegroundColor Yellow
Write-Host "Frontend Principal : http://localhost:55219" -ForegroundColor White
Write-Host "Discovery Console  : http://localhost:8761" -ForegroundColor White
Write-Host "Gateway API        : http://localhost:8080" -ForegroundColor White
Write-Host "Auth API           : http://localhost:8081" -ForegroundColor White

Write-Host ""
Write-Host "COMPTE ADMINISTRATEUR :" -ForegroundColor Yellow
Write-Host "=======================" -ForegroundColor Yellow
Write-Host "Username : admin" -ForegroundColor White
Write-Host "Password : admin123" -ForegroundColor White

Write-Host ""
Write-Host "INSTRUCTIONS :" -ForegroundColor Yellow
Write-Host "==============" -ForegroundColor Yellow
Write-Host "1. Ouvrez http://localhost:55219 dans votre navigateur" -ForegroundColor White
Write-Host "2. Connectez-vous avec admin/admin123" -ForegroundColor White
Write-Host "3. Explorez les modules : Accueil, Planning, Finances, etc." -ForegroundColor White
Write-Host "4. Testez la fonctionnalite Planning pour voir l'integration" -ForegroundColor White

Write-Host ""
Write-Host "STATUT :" -ForegroundColor Yellow
Write-Host "========" -ForegroundColor Yellow

$totalProcesses = 0
if ($javaProcesses) { $totalProcesses += $javaProcesses.Count }
if ($nodeProcesses) { $totalProcesses += $nodeProcesses.Count }

if ($totalProcesses -ge 4) {
    Write-Host "EXCELLENT ! Tous les services sont operationnels !" -ForegroundColor Green
    Write-Host "Le projet Club Olympique de Kelibia est pret !" -ForegroundColor Cyan
} elseif ($totalProcesses -ge 2) {
    Write-Host "BON ! La plupart des services fonctionnent." -ForegroundColor Yellow
    Write-Host "Attendez quelques minutes pour le demarrage complet." -ForegroundColor White
} else {
    Write-Host "ATTENTION ! Peu de services sont actifs." -ForegroundColor Red
    Write-Host "Verifiez les processus et relancez si necessaire." -ForegroundColor White
}

Write-Host ""
Write-Host "=== CLUB OLYMPIQUE DE KELIBIA ===" -ForegroundColor Green
Write-Host "Plateforme Intelligente de Gestion d'Equipe de Volley-Ball" -ForegroundColor White
Write-Host "Projet demarre avec succes ! 🏐" -ForegroundColor Yellow

Read-Host "Appuyez sur Entree pour terminer"
