import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { tap } from 'rxjs/operators';

// Configuration temporaire pour le développement
const environment = {
  production: false,
  apiUrl: 'http://localhost:8084'
};

export interface ChatbotConversation {
  id: number;
  utilisateurId: number;
  sessionId: string;
  contexte: any;
  statut: 'ACTIVE' | 'TERMINEE' | 'ESCALADEE';
  satisfaction?: number;
  dateCreation: string;
  dateFin?: string;
}

export interface ChatbotMessage {
  id: number;
  chatbotConversationId: number;
  contenu: string;
  estUtilisateur: boolean;
  intention?: string;
  scoreConfiance?: number;
  tempsReponseMs?: number;
  dateCreation: string;
}

export interface ChatbotRequest {
  message: string;
  contexte?: any;
}

export interface ChatbotResponse {
  reponse: string;
  intention: string;
  scoreConfiance: number;
  suggestions?: string[];
  escaladeRequise: boolean;
  contexte: any;
}

@Injectable({
  providedIn: 'root'
})
export class ChatbotService {
  private apiUrl = `${environment.apiUrl}/api/chatbot`;
  private conversationActiveSubject = new BehaviorSubject<ChatbotConversation | null>(null);
  private messagesSubject = new BehaviorSubject<ChatbotMessage[]>([]);
  private isTypingSubject = new BehaviorSubject<boolean>(false);

  constructor(private http: HttpClient) {}

  // Observables pour les composants
  getConversationActive(): Observable<ChatbotConversation | null> {
    return this.conversationActiveSubject.asObservable();
  }

  getMessages(): Observable<ChatbotMessage[]> {
    return this.messagesSubject.asObservable();
  }

  getIsTyping(): Observable<boolean> {
    return this.isTypingSubject.asObservable();
  }

  // Gestion des conversations chatbot
  demarrerConversation(utilisateurId: number): Observable<ChatbotConversation> {
    return this.http.post<ChatbotConversation>(`${this.apiUrl}/conversation`, {
      utilisateurId
    }).pipe(
      tap(conversation => {
        this.conversationActiveSubject.next(conversation);
        this.messagesSubject.next([]);
      })
    );
  }

  obtenirConversation(conversationId: number): Observable<ChatbotConversation> {
    return this.http.get<ChatbotConversation>(`${this.apiUrl}/conversation/${conversationId}`).pipe(
      tap(conversation => this.conversationActiveSubject.next(conversation))
    );
  }

  obtenirConversationsUtilisateur(utilisateurId: number, page: number = 0): Observable<any> {
    const params = new HttpParams().set('page', page.toString());
    return this.http.get<any>(`${this.apiUrl}/conversations/utilisateur/${utilisateurId}`, { params });
  }

  terminerConversation(conversationId: number, satisfaction?: number): Observable<void> {
    const body = satisfaction ? { satisfaction } : {};
    return this.http.post<void>(`${this.apiUrl}/conversation/${conversationId}/terminer`, body).pipe(
      tap(() => {
        const conversation = this.conversationActiveSubject.value;
        if (conversation) {
          conversation.statut = 'TERMINEE';
          conversation.satisfaction = satisfaction;
          conversation.dateFin = new Date().toISOString();
          this.conversationActiveSubject.next(conversation);
        }
      })
    );
  }

  escaladerConversation(conversationId: number, motif: string): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/conversation/${conversationId}/escalader`, {
      motif
    }).pipe(
      tap(() => {
        const conversation = this.conversationActiveSubject.value;
        if (conversation) {
          conversation.statut = 'ESCALADEE';
          this.conversationActiveSubject.next(conversation);
        }
      })
    );
  }

  // Gestion des messages
  envoyerMessage(conversationId: number, message: string): Observable<ChatbotResponse> {
    this.isTypingSubject.next(true);
    
    return this.http.post<ChatbotResponse>(`${this.apiUrl}/conversation/${conversationId}/message`, {
      message
    }).pipe(
      tap(response => {
        this.isTypingSubject.next(false);
        this.chargerMessages(conversationId);
      })
    );
  }

  obtenirMessages(conversationId: number, page: number = 0): Observable<any> {
    const params = new HttpParams().set('page', page.toString());
    return this.http.get<any>(`${this.apiUrl}/conversation/${conversationId}/messages`, { params }).pipe(
      tap(response => {
        if (page === 0) {
          this.messagesSubject.next(response.content);
        } else {
          const messages = this.messagesSubject.value;
          this.messagesSubject.next([...response.content, ...messages]);
        }
      })
    );
  }

  private chargerMessages(conversationId: number): void {
    this.obtenirMessages(conversationId).subscribe();
  }

  // Fonctionnalités avancées
  obtenirSuggestions(conversationId: number): Observable<string[]> {
    return this.http.get<string[]>(`${this.apiUrl}/conversation/${conversationId}/suggestions`);
  }

  evaluerReponse(messageId: number, evaluation: 'POSITIVE' | 'NEGATIVE'): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/message/${messageId}/evaluer`, {
      evaluation
    });
  }

  signalerProbleme(messageId: number, probleme: string): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/message/${messageId}/signaler`, {
      probleme
    });
  }

  // Statistiques et analytics
  obtenirStatistiquesConversation(conversationId: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/conversation/${conversationId}/statistiques`);
  }

  obtenirStatistiquesUtilisateur(utilisateurId: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/statistiques/utilisateur/${utilisateurId}`);
  }

  obtenirPerformanceIA(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/statistiques/performance`);
  }

  // Configuration et préférences
  obtenirConfigurationChatbot(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/configuration`);
  }

  mettreAJourPreferences(utilisateurId: number, preferences: any): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/preferences/${utilisateurId}`, preferences);
  }

  // Utilitaires
  detecterIntention(message: string): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/detecter-intention`, { message });
  }

  obtenirContexteSuggere(conversationId: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/conversation/${conversationId}/contexte-suggere`);
  }

  // Gestion des sessions
  restaurerSession(sessionId: string): Observable<ChatbotConversation> {
    return this.http.post<ChatbotConversation>(`${this.apiUrl}/session/restaurer`, {
      sessionId
    }).pipe(
      tap(conversation => {
        this.conversationActiveSubject.next(conversation);
        this.chargerMessages(conversation.id);
      })
    );
  }

  sauvegarderSession(conversationId: number): Observable<string> {
    return this.http.post<string>(`${this.apiUrl}/conversation/${conversationId}/sauvegarder`, {});
  }

  // Formatage et utilitaires
  formatTempsReponse(tempsMs: number): string {
    if (tempsMs < 1000) {
      return `${tempsMs}ms`;
    } else {
      return `${(tempsMs / 1000).toFixed(1)}s`;
    }
  }

  getScoreConfianceColor(score: number): string {
    if (score >= 0.8) return 'success';
    if (score >= 0.6) return 'warning';
    return 'danger';
  }

  getStatutIcon(statut: string): string {
    switch (statut) {
      case 'ACTIVE': return 'chat';
      case 'TERMINEE': return 'check_circle';
      case 'ESCALADEE': return 'support_agent';
      default: return 'help';
    }
  }

  getIntentionIcon(intention: string): string {
    switch (intention) {
      case 'salutation': return 'waving_hand';
      case 'question': return 'help';
      case 'demande_aide': return 'support';
      case 'plainte': return 'report_problem';
      case 'remerciement': return 'thumb_up';
      case 'au_revoir': return 'exit_to_app';
      default: return 'chat';
    }
  }

  // Nettoyage
  clearConversation(): void {
    this.conversationActiveSubject.next(null);
    this.messagesSubject.next([]);
    this.isTypingSubject.next(false);
  }

  // Simulation de frappe pour l'UX
  simulateTyping(duration: number = 2000): void {
    this.isTypingSubject.next(true);
    setTimeout(() => {
      this.isTypingSubject.next(false);
    }, duration);
  }
}
