# 🚀 Guide de déploiement - Finance Service

Ce guide détaille les procédures de déploiement du microservice Finance pour différents environnements.

## 📋 Prérequis

### Outils requis
- **Docker** 20.10+
- **Docker Compose** 2.0+
- **Java** 21+
- **Maven** 3.8+
- **Node.js** 18+
- **npm** 9+

### Vérification des prérequis
```bash
# Vérifier les versions
docker --version
docker-compose --version
java --version
mvn --version
node --version
npm --version
```

## 🏗️ Environnements de déploiement

### 1. Développement local

#### Backend seul
```bash
cd microservices/finance-service/backend
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

#### Frontend seul
```bash
cd microservices/finance-service/frontend
npm install
ng serve --port 4205
```

#### Services complets avec Docker
```bash
cd microservices/finance-service
docker-compose up --build
```

### 2. Environnement de test

#### Démarrage des services
```bash
# Depuis la racine du projet
docker-compose up --build finance-backend finance-frontend finance-db

# Vérification des services
./microservices/finance-service/validate-service.sh
./microservices/finance-service/test-integration.sh
```

#### Variables d'environnement de test
```bash
export SPRING_PROFILES_ACTIVE=test
export SPRING_DATASOURCE_URL=************************************************
export JWT_SECRET=test-secret-key-finance-2024
```

### 3. Production

#### Préparation
```bash
# Build des images de production
docker-compose build --no-cache finance-backend finance-frontend

# Test des images
docker-compose -f docker-compose.prod.yml up -d
```

#### Variables d'environnement de production
```bash
# Base de données
SPRING_DATASOURCE_URL=**********************************************
SPRING_DATASOURCE_USERNAME=finance_prod_user
SPRING_DATASOURCE_PASSWORD=<mot_de_passe_sécurisé>

# Sécurité
JWT_SECRET=<clé_secrète_production_très_longue>
JWT_EXPIRATION=86400

# Email
SMTP_HOST=smtp.production.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=<mot_de_passe_email>

# Monitoring
MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE=health,metrics,info
MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS=when_authorized
```

## 🔧 Configuration par environnement

### Profils Spring Boot

#### Développement (`dev`)
```yaml
spring:
  profiles:
    active: dev
  datasource:
    url: *******************************************
    username: finance_user
    password: finance_password
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: true
  flyway:
    enabled: true
    baseline-on-migrate: true
logging:
  level:
    com.sprintbot.finance: DEBUG
```

#### Test (`test`)
```yaml
spring:
  profiles:
    active: test
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
  jpa:
    hibernate:
      ddl-auto: create-drop
  flyway:
    enabled: false
```

#### Production (`prod`)
```yaml
spring:
  profiles:
    active: prod
  datasource:
    url: ${SPRING_DATASOURCE_URL}
    username: ${SPRING_DATASOURCE_USERNAME}
    password: ${SPRING_DATASOURCE_PASSWORD}
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
  flyway:
    enabled: true
    baseline-on-migrate: false
logging:
  level:
    com.sprintbot.finance: INFO
    org.springframework.security: WARN
```

### Configuration Angular

#### Développement
```typescript
export const environment = {
  production: false,
  apiUrl: 'http://localhost:8085',
  enableDebug: true,
  enableMocks: false
};
```

#### Production
```typescript
export const environment = {
  production: true,
  apiUrl: 'https://api.sprintbot.com/finance',
  enableDebug: false,
  enableMocks: false
};
```

## 🐳 Déploiement Docker

### Images Docker

#### Backend
```dockerfile
# Build
FROM eclipse-temurin:21-jdk-alpine AS builder
WORKDIR /app
COPY pom.xml .
COPY src/ src/
RUN mvn clean package -DskipTests

# Runtime
FROM eclipse-temurin:21-jre-alpine
COPY --from=builder /app/target/*.jar app.jar
EXPOSE 8085
CMD ["java", "-jar", "app.jar"]
```

#### Frontend
```dockerfile
# Build
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build --prod

# Runtime
FROM nginx:alpine
COPY --from=builder /app/dist/finance-frontend /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 8080
```

### Docker Compose

#### Développement
```yaml
version: '3.8'
services:
  finance-db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: finance_db
      POSTGRES_USER: finance_user
      POSTGRES_PASSWORD: finance_password
    ports:
      - "5437:5432"
    volumes:
      - finance_db_data:/var/lib/postgresql/data

  finance-backend:
    build: ./backend
    environment:
      SPRING_PROFILES_ACTIVE: docker
    ports:
      - "8085:8085"
    depends_on:
      - finance-db

  finance-frontend:
    build: ./frontend
    ports:
      - "4205:8080"
    depends_on:
      - finance-backend

volumes:
  finance_db_data:
```

#### Production
```yaml
version: '3.8'
services:
  finance-db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - finance_db_data:/var/lib/postgresql/data
      - ./backup:/backup
    restart: unless-stopped

  finance-backend:
    image: sprintbot/finance-backend:latest
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_DATASOURCE_URL: ${SPRING_DATASOURCE_URL}
      SPRING_DATASOURCE_USERNAME: ${SPRING_DATASOURCE_USERNAME}
      SPRING_DATASOURCE_PASSWORD: ${SPRING_DATASOURCE_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8085/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  finance-frontend:
    image: sprintbot/finance-frontend:latest
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 5s
      retries: 3

volumes:
  finance_db_data:
```

## 🔍 Validation du déploiement

### Scripts de validation
```bash
# Validation complète
./validate-service.sh --full

# Tests d'intégration
./test-integration.sh

# Validation rapide
./validate-service.sh --quick
```

### Vérifications manuelles

#### Backend
```bash
# Santé du service
curl http://localhost:8085/actuator/health

# Métriques
curl http://localhost:8085/actuator/metrics

# Test API
curl http://localhost:8085/api/budgets
```

#### Frontend
```bash
# Accessibilité
curl http://localhost:4205

# Santé
curl http://localhost:4205/health
```

#### Base de données
```bash
# Connexion
psql -h localhost -p 5437 -U finance_user -d finance_db

# Vérification des tables
\dt finance.*

# Vérification des données
SELECT COUNT(*) FROM finance.budget;
```

## 🚨 Dépannage

### Problèmes courants

#### Backend ne démarre pas
```bash
# Vérifier les logs
docker-compose logs finance-backend

# Vérifier la base de données
docker-compose logs finance-db

# Vérifier la connectivité
docker-compose exec finance-backend ping finance-db
```

#### Frontend inaccessible
```bash
# Vérifier les logs
docker-compose logs finance-frontend

# Vérifier la configuration nginx
docker-compose exec finance-frontend cat /etc/nginx/nginx.conf

# Test de connectivité backend
curl http://finance-backend:8085/actuator/health
```

#### Problèmes de base de données
```bash
# Réinitialiser la base
docker-compose down -v
docker-compose up finance-db

# Vérifier les migrations
docker-compose exec finance-backend mvn flyway:info
```

### Logs et monitoring

#### Localisation des logs
```bash
# Logs Docker
docker-compose logs -f finance-backend
docker-compose logs -f finance-frontend

# Logs applicatifs
docker-compose exec finance-backend tail -f /app/logs/application.log
```

#### Métriques de performance
```bash
# Métriques JVM
curl http://localhost:8085/actuator/metrics/jvm.memory.used

# Métriques applicatives
curl http://localhost:8085/actuator/metrics/http.server.requests

# Métriques base de données
curl http://localhost:8085/actuator/metrics/hikaricp.connections
```

## 🔐 Sécurité

### Checklist de sécurité

#### Configuration
- [ ] Mots de passe forts pour la base de données
- [ ] Clé JWT sécurisée et unique
- [ ] Variables d'environnement chiffrées
- [ ] Certificats SSL/TLS configurés
- [ ] Pare-feu configuré

#### Monitoring
- [ ] Logs de sécurité activés
- [ ] Alertes de sécurité configurées
- [ ] Sauvegarde des données critiques
- [ ] Plan de récupération en cas d'incident

### Sauvegarde et récupération

#### Sauvegarde automatique
```bash
# Script de sauvegarde
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker-compose exec -T finance-db pg_dump -U finance_user finance_db > backup_finance_$DATE.sql
```

#### Restauration
```bash
# Restauration depuis sauvegarde
docker-compose exec -T finance-db psql -U finance_user finance_db < backup_finance_20241030_120000.sql
```

## 📊 Monitoring et alertes

### Métriques à surveiller
- Temps de réponse des API
- Utilisation mémoire/CPU
- Connexions base de données
- Erreurs applicatives
- Espace disque

### Configuration des alertes
```yaml
# Exemple avec Prometheus
groups:
  - name: finance-service
    rules:
      - alert: FinanceServiceDown
        expr: up{job="finance-backend"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Finance Service is down"
```

Ce guide couvre tous les aspects du déploiement du microservice Finance. Pour des questions spécifiques, consultez la documentation technique ou contactez l'équipe DevOps.
