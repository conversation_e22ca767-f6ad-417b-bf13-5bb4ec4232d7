package com.sprintbot.communication.repository;

import com.sprintbot.communication.entity.CanalNotification;
import com.sprintbot.communication.entity.Notification;
import com.sprintbot.communication.entity.PrioriteNotification;
import com.sprintbot.communication.entity.StatutNotification;
import com.sprintbot.communication.entity.TypeNotification;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository pour l'entité Notification
 */
@Repository
public interface NotificationRepository extends JpaRepository<Notification, Long> {

    /**
     * Trouve les notifications d'un utilisateur avec pagination
     */
    Page<Notification> findByDestinataireIdOrderByDateCreationDesc(
            Long destinataireId, Pageable pageable);

    /**
     * Trouve les notifications non lues d'un utilisateur
     */
    List<Notification> findByDestinataireIdAndStatutNotOrderByDateCreationDesc(
            Long destinataireId, StatutNotification statut);

    /**
     * Trouve les notifications par statut
     */
    List<Notification> findByStatutOrderByDateCreationAsc(StatutNotification statut);

    /**
     * Trouve les notifications en attente d'envoi
     */
    @Query("SELECT n FROM Notification n " +
           "WHERE n.statut = 'EN_ATTENTE' " +
           "AND (n.dateProgrammee IS NULL OR n.dateProgrammee <= :maintenant) " +
           "ORDER BY n.priorite DESC, n.dateCreation ASC")
    List<Notification> findNotificationsAEnvoyer(@Param("maintenant") LocalDateTime maintenant);

    /**
     * Trouve les notifications programmées
     */
    List<Notification> findByStatutAndDateProgrammeeBeforeOrderByDateProgrammeeAsc(
            StatutNotification statut, LocalDateTime date);

    /**
     * Trouve les notifications par type et destinataire
     */
    List<Notification> findByDestinataireIdAndTypeOrderByDateCreationDesc(
            Long destinataireId, TypeNotification type);

    /**
     * Trouve les notifications par canal
     */
    List<Notification> findByCanalAndStatutOrderByDateCreationAsc(
            CanalNotification canal, StatutNotification statut);

    /**
     * Compte les notifications non lues d'un utilisateur
     */
    Long countByDestinataireIdAndStatutIn(
            Long destinataireId, List<StatutNotification> statuts);

    /**
     * Trouve les notifications urgentes non traitées
     */
    @Query("SELECT n FROM Notification n " +
           "WHERE n.priorite = 'URGENTE' " +
           "AND n.statut IN ('EN_ATTENTE', 'ERREUR') " +
           "ORDER BY n.dateCreation ASC")
    List<Notification> findNotificationsUrgentesNonTraitees();

    /**
     * Trouve les notifications en erreur à réessayer
     */
    @Query("SELECT n FROM Notification n " +
           "WHERE n.statut = 'ERREUR' " +
           "AND n.nombreTentatives < 3 " +
           "ORDER BY n.dateCreation ASC")
    List<Notification> findNotificationsAReessayer();

    /**
     * Trouve les notifications expirées
     */
    @Query("SELECT n FROM Notification n " +
           "WHERE n.dateExpiration IS NOT NULL " +
           "AND n.dateExpiration < :maintenant " +
           "AND n.statut NOT IN ('LU', 'EXPIREE')")
    List<Notification> findNotificationsExpirees(@Param("maintenant") LocalDateTime maintenant);

    /**
     * Trouve les notifications par groupe
     */
    List<Notification> findByGroupeNotificationAndDestinataireIdOrderByDateCreationDesc(
            String groupeNotification, Long destinataireId);

    /**
     * Statistiques des notifications par type
     */
    @Query("SELECT n.type, COUNT(n) FROM Notification n " +
           "WHERE n.destinataireId = :destinataireId " +
           "AND n.dateCreation >= :depuis " +
           "GROUP BY n.type")
    List<Object[]> getStatistiquesParType(
            @Param("destinataireId") Long destinataireId,
            @Param("depuis") LocalDateTime depuis);

    /**
     * Statistiques des notifications par canal
     */
    @Query("SELECT n.canal, COUNT(n), " +
           "SUM(CASE WHEN n.statut = 'ENVOYE' THEN 1 ELSE 0 END) as envoyes, " +
           "SUM(CASE WHEN n.statut = 'LU' THEN 1 ELSE 0 END) as lus " +
           "FROM Notification n " +
           "WHERE n.dateCreation >= :depuis " +
           "GROUP BY n.canal")
    List<Object[]> getStatistiquesParCanal(@Param("depuis") LocalDateTime depuis);

    /**
     * Trouve les notifications récentes d'un utilisateur
     */
    @Query("SELECT n FROM Notification n " +
           "WHERE n.destinataireId = :destinataireId " +
           "AND n.dateCreation >= :depuis " +
           "ORDER BY n.dateCreation DESC")
    List<Notification> findNotificationsRecentes(
            @Param("destinataireId") Long destinataireId,
            @Param("depuis") LocalDateTime depuis);

    /**
     * Supprime les anciennes notifications lues
     */
    @Query("DELETE FROM Notification n " +
           "WHERE n.statut = 'LU' " +
           "AND n.dateLecture < :dateLimit")
    void supprimerAnciennesNotificationsLues(@Param("dateLimit") LocalDateTime dateLimit);

    /**
     * Marque les notifications comme expirées
     */
    @Query("UPDATE Notification n " +
           "SET n.statut = 'EXPIREE' " +
           "WHERE n.dateExpiration < :maintenant " +
           "AND n.statut NOT IN ('LU', 'EXPIREE')")
    int marquerNotificationsCommeExpirees(@Param("maintenant") LocalDateTime maintenant);

    /**
     * Trouve les notifications par priorité
     */
    List<Notification> findByDestinataireIdAndPrioriteOrderByDateCreationDesc(
            Long destinataireId, PrioriteNotification priorite);

    /**
     * Compte les notifications par statut pour un utilisateur
     */
    @Query("SELECT n.statut, COUNT(n) FROM Notification n " +
           "WHERE n.destinataireId = :destinataireId " +
           "GROUP BY n.statut")
    List<Object[]> countNotificationsParStatut(@Param("destinataireId") Long destinataireId);

    /**
     * Trouve les notifications avec template
     */
    List<Notification> findByTemplateIdAndStatutOrderByDateCreationDesc(
            String templateId, StatutNotification statut);

    /**
     * Recherche de notifications par contenu
     */
    @Query("SELECT n FROM Notification n " +
           "WHERE n.destinataireId = :destinataireId " +
           "AND (LOWER(n.titre) LIKE LOWER(CONCAT('%', :recherche, '%')) " +
           "OR LOWER(n.contenu) LIKE LOWER(CONCAT('%', :recherche, '%'))) " +
           "ORDER BY n.dateCreation DESC")
    List<Notification> rechercherNotifications(
            @Param("destinataireId") Long destinataireId,
            @Param("recherche") String recherche);
}
