-- Migration V3: Création des vues et données de test
-- Date: 2024-07-29
-- Description: Ajout des vues pour faciliter les requêtes et insertion de données de test

-- Utilisation du schéma
SET search_path TO medical_admin;

-- Vues pour faciliter les requêtes courantes
CREATE OR REPLACE VIEW v_donnees_sante_actives AS
SELECT 
    ds.*,
    CASE 
        WHEN ds.date_guerison_prevue IS NOT NULL AND ds.date_guerison_prevue < CURRENT_DATE 
        THEN true 
        ELSE false 
    END as guerison_echue
FROM donnees_sante ds
WHERE ds.statut IN ('ACTIF', 'EN_TRAITEMENT', 'SUIVI');

CREATE OR REPLACE VIEW v_rendez_vous_planning AS
SELECT 
    rv.*,
    CASE 
        WHEN rv.date_rendez_vous = CURRENT_DATE THEN 'AUJOURD_HUI'
        WHEN rv.date_rendez_vous = CURRENT_DATE + INTERVAL '1 day' THEN 'DEMAIN'
        WHEN rv.date_rendez_vous BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '7 days' THEN 'CETTE_SEMAINE'
        ELSE 'PLUS_TARD'
    END as periode
FROM rendez_vous_medicaux rv
WHERE rv.statut IN ('PLANIFIE', 'CONFIRME', 'EN_COURS')
ORDER BY rv.date_rendez_vous, rv.heure_debut;

CREATE OR REPLACE VIEW v_demandes_workflow AS
SELECT 
    da.*,
    CASE 
        WHEN da.date_echeance IS NOT NULL AND da.date_echeance < CURRENT_DATE 
        THEN true 
        ELSE false 
    END as echue,
    CASE 
        WHEN da.necessite_validation_coach AND da.validation_coach IS NULL THEN false
        WHEN da.necessite_validation_medical AND da.validation_medical IS NULL THEN false
        WHEN da.necessite_validation_financier AND da.validation_financier IS NULL THEN false
        ELSE true
    END as toutes_validations_obtenues
FROM demandes_administratives da
WHERE da.statut IN ('EN_ATTENTE', 'EN_TRAITEMENT');

-- Commentaires sur les tables
COMMENT ON TABLE donnees_sante IS 'Table des données de santé et examens médicaux des joueurs';
COMMENT ON TABLE rendez_vous_medicaux IS 'Table des rendez-vous et consultations médicales';
COMMENT ON TABLE demandes_administratives IS 'Table des demandes administratives avec workflow de validation';

-- Commentaires sur les colonnes importantes
COMMENT ON COLUMN donnees_sante.type_examen IS 'Type d''examen: BILAN_GENERAL, BLESSURE, SUIVI, PREVENTION';
COMMENT ON COLUMN donnees_sante.statut IS 'Statut: ACTIF, GUERI, EN_TRAITEMENT, SUIVI';
COMMENT ON COLUMN donnees_sante.gravite IS 'Gravité: FAIBLE, MOYENNE, ELEVEE, CRITIQUE';

COMMENT ON COLUMN rendez_vous_medicaux.statut IS 'Statut: PLANIFIE, CONFIRME, EN_COURS, TERMINE, ANNULE, REPORTE';
COMMENT ON COLUMN rendez_vous_medicaux.priorite IS 'Priorité: FAIBLE, NORMALE, ELEVEE, URGENTE';

COMMENT ON COLUMN demandes_administratives.type_demande IS 'Type: CONGE, MATERIEL, FORMATION, REMBOURSEMENT, ACCES, AUTRE';
COMMENT ON COLUMN demandes_administratives.statut IS 'Statut: EN_ATTENTE, EN_TRAITEMENT, VALIDEE, REJETEE, SUSPENDUE';
COMMENT ON COLUMN demandes_administratives.priorite IS 'Priorité: FAIBLE, NORMALE, ELEVEE, URGENTE';

-- Données de test pour le développement
INSERT INTO donnees_sante (joueur_id, staff_medical_id, type_examen, date_examen, resultats, statut, gravite) VALUES
(1, 1, 'BILAN_GENERAL', CURRENT_DATE - INTERVAL '30 days', 'Bilan général satisfaisant. Condition physique excellente.', 'ACTIF', 'FAIBLE'),
(2, 1, 'BLESSURE', CURRENT_DATE - INTERVAL '15 days', 'Entorse cheville droite. Repos recommandé.', 'EN_TRAITEMENT', 'MOYENNE'),
(3, 2, 'SUIVI', CURRENT_DATE - INTERVAL '7 days', 'Suivi post-blessure. Évolution positive.', 'SUIVI', 'FAIBLE'),
(1, 2, 'PREVENTION', CURRENT_DATE - INTERVAL '5 days', 'Séance de prévention. Exercices de renforcement.', 'ACTIF', 'FAIBLE');

INSERT INTO rendez_vous_medicaux (joueur_id, staff_medical_id, type_rendez_vous, date_rendez_vous, heure_debut, heure_fin, lieu, statut, priorite) VALUES
(1, 1, 'CONSULTATION', CURRENT_DATE + INTERVAL '1 day', '09:00', '09:30', 'Cabinet médical', 'PLANIFIE', 'NORMALE'),
(2, 1, 'SUIVI_BLESSURE', CURRENT_DATE + INTERVAL '2 days', '14:00', '14:45', 'Centre médical', 'CONFIRME', 'ELEVEE'),
(3, 2, 'BILAN_ANNUEL', CURRENT_DATE + INTERVAL '7 days', '10:00', '11:00', 'Clinique sportive', 'PLANIFIE', 'NORMALE'),
(1, 2, 'PREVENTION', CURRENT_DATE + INTERVAL '14 days', '16:00', '16:30', 'Centre de rééducation', 'PLANIFIE', 'FAIBLE');

INSERT INTO demandes_administratives (demandeur_id, type_demandeur, type_demande, titre, description, date_soumission, priorite, cout_estime, necessite_validation_coach, necessite_validation_medical) VALUES
(1, 'JOUEUR', 'CONGE', 'Demande de congé médical', 'Demande de congé pour récupération suite à blessure', CURRENT_DATE - INTERVAL '2 days', 'ELEVEE', 0.00, true, true),
(2, 'STAFF', 'MATERIEL', 'Achat équipement médical', 'Demande d''achat de matériel de rééducation', CURRENT_DATE - INTERVAL '5 days', 'NORMALE', 1500.00, false, true),
(3, 'JOUEUR', 'FORMATION', 'Formation premiers secours', 'Participation à une formation premiers secours', CURRENT_DATE - INTERVAL '1 day', 'FAIBLE', 300.00, true, false),
(1, 'STAFF', 'REMBOURSEMENT', 'Remboursement frais médicaux', 'Remboursement consultation spécialisée', CURRENT_DATE, 'NORMALE', 150.00, false, true);
