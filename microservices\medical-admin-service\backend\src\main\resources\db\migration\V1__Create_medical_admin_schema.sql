-- Migration V1: Création du schéma medical_admin
-- Date: 2024-07-29
-- Description: Création initiale des tables pour le microservice medical-admin-service

-- Création du schéma dédié
CREATE SCHEMA IF NOT EXISTS medical_admin;

-- Utilisation du schéma
SET search_path TO medical_admin;

-- Table des données de santé
CREATE TABLE donnees_sante (
    id BIGSERIAL PRIMARY KEY,
    joueur_id BIGINT NOT NULL,
    staff_medical_id BIGINT,
    type_examen VARCHAR(100) NOT NULL,
    date_examen DATE NOT NULL,
    resultats TEXT,
    recommandations TEXT,
    blessures TEXT,
    traitements TEXT,
    medicaments TEXT,
    statut VARCHAR(50) DEFAULT 'ACTIF',
    gravite <PERSON>HAR(50),
    date_guerison_prevue DATE,
    necessite_suivi BOOLEAN DEFAULT FALSE,
    visible_par_joueur BOOLEAN DEFAULT TRUE,
    notes_confidentielles TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des rendez-vous médicaux
CREATE TABLE rendez_vous_medicaux (
    id BIGSERIAL PRIMARY KEY,
    joueur_id BIGINT NOT NULL,
    staff_medical_id BIGINT NOT NULL,
    type_rendez_vous VARCHAR(100) NOT NULL,
    date_rendez_vous DATE NOT NULL,
    heure_debut TIME NOT NULL,
    heure_fin TIME NOT NULL,
    lieu VARCHAR(200),
    description TEXT,
    statut VARCHAR(50) DEFAULT 'PLANIFIE',
    priorite VARCHAR(50) DEFAULT 'NORMALE',
    rappel_envoye BOOLEAN DEFAULT FALSE,
    date_rappel TIMESTAMP,
    compte_rendu TEXT,
    prescriptions TEXT,
    prochaine_visite_prevue DATE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des demandes administratives
CREATE TABLE demandes_administratives (
    id BIGSERIAL PRIMARY KEY,
    demandeur_id BIGINT NOT NULL,
    type_demandeur VARCHAR(50) NOT NULL,
    approbateur_id BIGINT,
    type_demande VARCHAR(100) NOT NULL,
    titre VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    justification TEXT,
    date_soumission DATE NOT NULL,
    date_echeance DATE,
    statut VARCHAR(50) DEFAULT 'EN_ATTENTE',
    priorite VARCHAR(50) DEFAULT 'NORMALE',
    cout_estime DECIMAL(10,2),
    cout_reel DECIMAL(10,2),
    commentaire_approbateur TEXT,
    date_traitement TIMESTAMP,
    date_validation TIMESTAMP,
    necessite_validation_coach BOOLEAN DEFAULT FALSE,
    validation_coach BOOLEAN,
    necessite_validation_medical BOOLEAN DEFAULT FALSE,
    validation_medical BOOLEAN,
    necessite_validation_financier BOOLEAN DEFAULT FALSE,
    validation_financier BOOLEAN,
    documents_joints TEXT,
    historique_statuts TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Fonction pour mettre à jour automatiquement updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';
