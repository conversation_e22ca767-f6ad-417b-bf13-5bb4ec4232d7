package com.sprintbot.medicaladmin.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * Configuration Swagger/OpenAPI pour la documentation de l'API
 * Medical Admin Service
 */
@Configuration
public class SwaggerConfig {

    @Value("${server.port:8083}")
    private String serverPort;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("SprintBot Medical Admin Service API")
                        .description("API REST pour la gestion médicale et administrative de l'équipe de volleyball")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("SprintBot Team")
                                .email("<EMAIL>")
                                .url("https://sprintbot.com"))
                        .license(new License()
                                .name("MIT License")
                                .url("https://opensource.org/licenses/MIT")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort)
                                .description("Serveur de développement local"),
                        new Server()
                                .url("http://medical-admin-service:" + serverPort)
                                .description("Serveur Docker"),
                        new Server()
                                .url("https://api.sprintbot.com/medical-admin")
                                .description("Serveur de production")
                ));
    }
}
