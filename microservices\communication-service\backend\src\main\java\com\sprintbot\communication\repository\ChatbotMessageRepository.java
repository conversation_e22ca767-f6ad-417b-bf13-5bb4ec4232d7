package com.sprintbot.communication.repository;

import com.sprintbot.communication.entity.ChatbotMessage;
import com.sprintbot.communication.entity.TypeMessageChatbot;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository pour l'entité ChatbotMessage
 */
@Repository
public interface ChatbotMessageRepository extends JpaRepository<ChatbotMessage, Long> {

    /**
     * Trouve les messages d'une conversation chatbot
     */
    Page<ChatbotMessage> findByConversationIdOrderByDateCreationAsc(
            Long conversationId, Pageable pageable);

    /**
     * Trouve les messages par type
     */
    List<ChatbotMessage> findByConversationIdAndTypeOrderByDateCreationAsc(
            Long conversationId, TypeMessageChatbot type);

    /**
     * Trouve les messages par type (ordre décroissant)
     */
    List<ChatbotMessage> findByConversationIdAndTypeOrderByDateCreationDesc(
            Long conversationId, TypeMessageChatbot type);

    /**
     * Trouve les messages avec une intention détectée
     */
    List<ChatbotMessage> findByIntentionDetecteeIsNotNullOrderByDateCreationDesc();

    /**
     * Trouve les messages avec un score de confiance élevé
     */
    @Query("SELECT m FROM ChatbotMessage m " +
           "WHERE m.confianceScore >= :scoreMinimum " +
           "ORDER BY m.confianceScore DESC")
    List<ChatbotMessage> findMessagesAvecBonneConfiance(@Param("scoreMinimum") Double scoreMinimum);

    /**
     * Trouve les messages avec un score de confiance faible
     */
    @Query("SELECT m FROM ChatbotMessage m " +
           "WHERE m.confianceScore < :scoreMaximum " +
           "AND m.type = 'BOT' " +
           "ORDER BY m.confianceScore ASC")
    List<ChatbotMessage> findMessagesAvecFaibleConfiance(@Param("scoreMaximum") Double scoreMaximum);

    /**
     * Trouve les messages évalués positivement
     */
    List<ChatbotMessage> findByEstUtileTrueOrderByDateCreationDesc();

    /**
     * Trouve les messages évalués négativement
     */
    List<ChatbotMessage> findByEstUtileFalseOrderByDateCreationDesc();

    /**
     * Trouve les messages avec feedback
     */
    List<ChatbotMessage> findByFeedbackUtilisateurIsNotNullOrderByDateCreationDesc();

    /**
     * Statistiques par intention
     */
    @Query("SELECT m.intentionDetectee, COUNT(m), AVG(m.confianceScore) " +
           "FROM ChatbotMessage m " +
           "WHERE m.intentionDetectee IS NOT NULL " +
           "AND m.dateCreation >= :depuis " +
           "GROUP BY m.intentionDetectee " +
           "ORDER BY COUNT(m) DESC")
    List<Object[]> getStatistiquesParIntention(@Param("depuis") LocalDateTime depuis);

    /**
     * Statistiques de performance
     */
    @Query("SELECT AVG(m.tempsReponse), AVG(m.tokensUtilises), AVG(m.coutEstimation) " +
           "FROM ChatbotMessage m " +
           "WHERE m.type = 'BOT' " +
           "AND m.dateCreation >= :depuis")
    Object[] getStatistiquesPerformance(@Param("depuis") LocalDateTime depuis);

    /**
     * Taux de satisfaction
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN m.estUtile = true THEN 1 END) * 100.0 / " +
           "COUNT(CASE WHEN m.estUtile IS NOT NULL THEN 1 END) " +
           "FROM ChatbotMessage m " +
           "WHERE m.type = 'BOT' " +
           "AND m.dateCreation >= :depuis")
    Double getTauxSatisfaction(@Param("depuis") LocalDateTime depuis);

    /**
     * Messages par modèle utilisé
     */
    @Query("SELECT m.modeleUtilise, COUNT(m), AVG(m.tempsReponse) " +
           "FROM ChatbotMessage m " +
           "WHERE m.modeleUtilise IS NOT NULL " +
           "AND m.dateCreation >= :depuis " +
           "GROUP BY m.modeleUtilise")
    List<Object[]> getStatistiquesParModele(@Param("depuis") LocalDateTime depuis);

    /**
     * Recherche de messages par contenu
     */
    @Query("SELECT m FROM ChatbotMessage m " +
           "WHERE LOWER(m.contenu) LIKE LOWER(CONCAT('%', :recherche, '%')) " +
           "ORDER BY m.dateCreation DESC")
    List<ChatbotMessage> rechercherMessagesParContenu(@Param("recherche") String recherche);

    /**
     * Coût total par période
     */
    @Query("SELECT SUM(m.coutEstimation) FROM ChatbotMessage m " +
           "WHERE m.coutEstimation IS NOT NULL " +
           "AND m.dateCreation >= :depuis")
    Double getCoutTotal(@Param("depuis") LocalDateTime depuis);

    /**
     * Messages les plus lents
     */
    @Query("SELECT m FROM ChatbotMessage m " +
           "WHERE m.tempsReponse IS NOT NULL " +
           "AND m.type = 'BOT' " +
           "ORDER BY m.tempsReponse DESC")
    List<ChatbotMessage> findMessagesLesPlusLents(Pageable pageable);

    /**
     * Supprime les anciens messages
     */
    @Query("DELETE FROM ChatbotMessage m " +
           "WHERE m.dateCreation < :dateLimit " +
           "AND m.estUtile IS NULL")
    void supprimerAnciensMessages(@Param("dateLimit") LocalDateTime dateLimit);

    /**
     * Compte les messages par type et conversation
     */
    @Query("SELECT m.type, COUNT(m) FROM ChatbotMessage m " +
           "WHERE m.conversation.id = :conversationId " +
           "GROUP BY m.type")
    List<Object[]> countMessagesParType(@Param("conversationId") Long conversationId);
}
