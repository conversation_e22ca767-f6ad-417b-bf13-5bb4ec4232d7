# 🏐 Test du Microservice Planning Performance

## Club Olympique de Kelibia - Planning Performance Service

### ✅ STRUCTURE DU MICROSERVICE VÉRIFIÉE

Le microservice Planning Performance est **complètement configuré** et prêt pour les tests !

---

## 📁 STRUCTURE DU PROJET

```
microservices/planning-performance-service/
├── backend/                    # Spring Boot Application
│   ├── src/main/java/         # Code source Java
│   ├── src/main/resources/    # Configuration
│   │   └── application.yml    # Configuration Spring Boot
│   ├── pom.xml               # Dépendances Maven
│   └── Dockerfile            # Image Docker backend
├── frontend/                  # Angular Application
│   ├── src/app/              # Code source Angular
│   ├── package.json          # Dépendances npm
│   └── Dockerfile            # Image Docker frontend
├── database/                 # Configuration base de données
│   └── init.sql             # Script d'initialisation
└── docker-compose.yml       # Orchestration Docker
```

---

## ⚙️ CONFIGURATION VÉRIFIÉE

### Backend Spring Boot
- **Port** : 8082
- **Base de données** : PostgreSQL sur port 5434
- **Profils** : dev, docker, prod
- **Eureka** : Intégration service discovery
- **Actuator** : Monitoring activé

### Frontend Angular
- **Port** : 4202
- **API URL** : http://localhost:8082/api
- **Framework** : Angular 17 avec TypeScript

### Base de Données
- **Type** : PostgreSQL 15
- **Port** : 5434
- **Database** : planning_performance_db
- **User** : planning_user
- **Password** : planning_password

---

## 🚀 MÉTHODES DE TEST

### Option 1 : Test avec Docker (Recommandé)

#### 1. Démarrer la base de données
```bash
cd microservices/planning-performance-service
docker-compose up -d planning-performance-db
```

#### 2. Vérifier la base de données
```bash
docker exec planning-performance-db pg_isready -U planning_user -d planning_performance_db
```

#### 3. Construire et démarrer le backend
```bash
cd backend
docker build -t planning-performance-backend .
docker run -d --name planning-performance-backend \
  --network planning-performance-service_planning-performance-network \
  -p 8082:8082 \
  -e SPRING_PROFILES_ACTIVE=docker \
  planning-performance-backend
```

#### 4. Construire et démarrer le frontend
```bash
cd ../frontend
docker build -t planning-performance-frontend .
docker run -d --name planning-performance-frontend \
  -p 4202:80 \
  planning-performance-frontend
```

### Option 2 : Test en développement local

#### Prérequis
- Java 17+
- Maven 3.8+
- Node.js 18+
- PostgreSQL 15

#### 1. Base de données locale
```bash
# Avec Docker
docker run -d --name planning-db \
  -e POSTGRES_DB=planning_performance_db \
  -e POSTGRES_USER=planning_user \
  -e POSTGRES_PASSWORD=planning_password \
  -p 5434:5432 \
  postgres:15-alpine

# Ou installation locale PostgreSQL
createdb -U postgres planning_performance_db
```

#### 2. Backend Spring Boot
```bash
cd backend
mvn clean compile
mvn spring-boot:run
```

#### 3. Frontend Angular
```bash
cd frontend
npm install
ng serve --port 4202
```

---

## 🧪 TESTS À EFFECTUER

### 1. Vérification des Services

#### Backend Health Check
```bash
curl http://localhost:8082/actuator/health
```

**Réponse attendue :**
```json
{
  "status": "UP",
  "components": {
    "db": {"status": "UP"},
    "diskSpace": {"status": "UP"}
  }
}
```

#### Frontend Accessibility
```bash
curl http://localhost:4202
```

### 2. Test des APIs

#### Entraînements
```bash
# Liste des entraînements
curl http://localhost:8082/api/entrainements

# Créer un entraînement
curl -X POST http://localhost:8082/api/entrainements \
  -H "Content-Type: application/json" \
  -d '{
    "titre": "Entraînement Test",
    "description": "Test du microservice",
    "date": "2024-08-06",
    "heureDebut": "18:00",
    "heureFin": "20:00",
    "lieu": "Gymnase Test",
    "type": "TECHNIQUE",
    "intensite": 7
  }'
```

#### Performances
```bash
# Liste des performances
curl http://localhost:8082/api/performances

# Statistiques
curl http://localhost:8082/api/statistiques
```

#### Participations
```bash
# Liste des participations
curl http://localhost:8082/api/participations
```

#### Objectifs
```bash
# Liste des objectifs
curl http://localhost:8082/api/objectifs
```

### 3. Test de l'Interface Frontend

1. **Accéder à l'interface** : http://localhost:4202
2. **Navigation** : Vérifier les menus et composants
3. **Fonctionnalités** : Tester la création d'entraînements
4. **API Integration** : Vérifier les appels vers le backend

---

## 📊 ENDPOINTS API DISPONIBLES

### 🏃‍♂️ Entraînements
- `GET /api/entrainements` - Liste des entraînements
- `POST /api/entrainements` - Créer un entraînement
- `GET /api/entrainements/{id}` - Détails d'un entraînement
- `PUT /api/entrainements/{id}` - Modifier un entraînement
- `DELETE /api/entrainements/{id}` - Supprimer un entraînement

### 👥 Participations
- `GET /api/participations` - Liste des participations
- `POST /api/participations` - Enregistrer une participation
- `PUT /api/participations/{id}/presence` - Marquer présent/absent

### 📊 Performances
- `GET /api/performances` - Liste des performances
- `POST /api/performances` - Créer une évaluation
- `GET /api/performances/joueur/{id}` - Performances d'un joueur

### 🎯 Objectifs
- `GET /api/objectifs` - Liste des objectifs
- `POST /api/objectifs` - Créer un objectif
- `PUT /api/objectifs/{id}` - Modifier un objectif

### 📈 Statistiques
- `GET /api/statistiques` - Statistiques globales
- `GET /api/statistiques/joueur/{id}` - Statistiques d'un joueur

---

## 🔧 DÉPANNAGE

### Problèmes Docker
Si Docker ne répond pas :
1. Redémarrer Docker Desktop
2. Nettoyer les conteneurs : `docker system prune -f`
3. Utiliser le développement local

### Problèmes de Base de Données
```bash
# Vérifier la connexion
docker exec planning-performance-db psql -U planning_user -d planning_performance_db -c "SELECT 1;"

# Voir les logs
docker logs planning-performance-db
```

### Problèmes Backend
```bash
# Voir les logs
docker logs planning-performance-backend

# Vérifier les ports
netstat -an | findstr 8082
```

### Problèmes Frontend
```bash
# Voir les logs
docker logs planning-performance-frontend

# Vérifier les ports
netstat -an | findstr 4202
```

---

## ✅ RÉSULTATS ATTENDUS

### Services Opérationnels
- ✅ Base de données PostgreSQL sur port 5434
- ✅ Backend Spring Boot sur port 8082
- ✅ Frontend Angular sur port 4202
- ✅ APIs REST fonctionnelles
- ✅ Interface utilisateur accessible

### Intégration Réussie
- ✅ Communication frontend ↔ backend
- ✅ Persistance des données en base
- ✅ Endpoints API répondent correctement
- ✅ Interface utilisateur interactive

---

## 🎯 PROCHAINES ÉTAPES

1. **Lancer le test** avec une des méthodes ci-dessus
2. **Vérifier les services** avec les commandes de test
3. **Tester les APIs** avec les exemples curl
4. **Valider l'interface** frontend
5. **Intégrer avec le microservice auth-user-service**

---

**🚀 Le microservice Planning Performance est prêt pour les tests !**

Choisissez la méthode de test qui vous convient le mieux et suivez les étapes correspondantes.
