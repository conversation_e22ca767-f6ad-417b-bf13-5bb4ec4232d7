# Docker Compose pour le service de communication SprintBot
# Auteur: SprintBot Team
# Version: 1.0.0

version: '3.8'

services:
  # Base de données PostgreSQL dédiée
  communication-db:
    image: postgres:15-alpine
    container_name: sprintbot-communication-db
    environment:
      POSTGRES_DB: sprintbot_communication
      POSTGRES_USER: sprintbot
      POSTGRES_PASSWORD: sprintbot123
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=fr_FR.UTF-8"
    volumes:
      - communication_db_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5434:5432"
    networks:
      - communication-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U sprintbot -d sprintbot_communication"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Redis pour le cache et les sessions WebSocket
  communication-redis:
    image: redis:7-alpine
    container_name: sprintbot-communication-redis
    command: redis-server --appendonly yes --requirepass sprintbot123
    volumes:
      - communication_redis_data:/data
    ports:
      - "6380:6379"
    networks:
      - communication-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend Spring Boot
  communication-backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: sprintbot-communication-backend
    environment:
      SPRING_PROFILES_ACTIVE: docker
      DB_HOST: communication-db
      DB_PORT: 5432
      DB_NAME: sprintbot_communication
      DB_USERNAME: sprintbot
      DB_PASSWORD: sprintbot123
      REDIS_HOST: communication-redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: sprintbot123
      JWT_SECRET: sprintbot-communication-secret-key-2024
      JWT_EXPIRATION: 86400
      OPENAI_API_KEY: ${OPENAI_API_KEY:-}
      OPENAI_MODEL: gpt-3.5-turbo
      VAPID_PUBLIC_KEY: ${VAPID_PUBLIC_KEY:-}
      VAPID_PRIVATE_KEY: ${VAPID_PRIVATE_KEY:-}
      VAPID_SUBJECT: mailto:<EMAIL>
      SMTP_HOST: ${SMTP_HOST:-}
      SMTP_PORT: ${SMTP_PORT:-587}
      SMTP_USERNAME: ${SMTP_USERNAME:-}
      SMTP_PASSWORD: ${SMTP_PASSWORD:-}
      CORS_ALLOWED_ORIGINS: http://localhost:4200,http://localhost:4204,http://communication-frontend
      WAIT_FOR_DEPENDENCIES: "true"
      HEALTH_CHECK_ENABLED: "true"
      ENABLE_MONITORING: "true"
    ports:
      - "8084:8084"
      - "5005:5005" # Port debug (si activé)
    volumes:
      - communication_uploads:/app/uploads
      - communication_logs:/app/logs
    networks:
      - communication-network
      - sprintbot-network
    depends_on:
      communication-db:
        condition: service_healthy
      communication-redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8084/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Frontend Angular avec Nginx
  communication-frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        BUILD_CONFIGURATION: docker
    container_name: sprintbot-communication-frontend
    environment:
      API_URL: http://localhost:8084
      NGINX_PORT: 80
    ports:
      - "4204:80"
    networks:
      - communication-network
      - sprintbot-network
    depends_on:
      communication-backend:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

# Volumes persistants
volumes:
  communication_db_data:
    driver: local
    name: sprintbot_communication_db_data
  communication_redis_data:
    driver: local
    name: sprintbot_communication_redis_data
  communication_uploads:
    driver: local
    name: sprintbot_communication_uploads
  communication_logs:
    driver: local
    name: sprintbot_communication_logs

# Réseaux
networks:
  communication-network:
    driver: bridge
    name: sprintbot-communication-network
  sprintbot-network:
    external: true
    name: sprintbot-network
