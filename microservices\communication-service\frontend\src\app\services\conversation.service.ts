import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, tap } from 'rxjs/operators';

// Configuration temporaire pour le développement
const environment = {
  production: false,
  apiUrl: 'http://localhost:8084'
};

export interface Conversation {
  id: number;
  nom: string;
  description?: string;
  type: 'PRIVE' | 'GROUPE' | 'EQUIPE' | 'CANAL';
  createurId: number;
  estArchive: boolean;
  dateCreation: string;
  dateModification: string;
  participants?: ParticipantConversation[];
  dernierMessage?: Message;
  nombreMessagesNonLus?: number;
}

export interface ParticipantConversation {
  id: number;
  conversationId: number;
  utilisateurId: number;
  role: 'MEMBRE' | 'ADMIN' | 'MODERATEUR';
  dateAjout: string;
  dateDerniereLecture?: string;
  notificationsActives: boolean;
  estEpingle: boolean;
}

export interface Message {
  id: number;
  conversationId: number;
  expediteurId: number;
  contenu: string;
  type: 'TEXTE' | 'IMAGE' | 'VIDEO' | 'AUDIO' | 'FICHIER' | 'SYSTEME';
  urlFichier?: string;
  messageParentId?: number;
  estModifie: boolean;
  estSupprime: boolean;
  estEpingle: boolean;
  dateCreation: string;
  dateModification: string;
  reactions?: ReactionMessage[];
  lectures?: LectureMessage[];
  messageParent?: Message;
}

export interface ReactionMessage {
  id: number;
  messageId: number;
  utilisateurId: number;
  emoji: string;
  dateCreation: string;
}

export interface LectureMessage {
  id: number;
  messageId: number;
  utilisateurId: number;
  dateLecture: string;
}

export interface CreerConversationRequest {
  nom: string;
  type: 'PRIVE' | 'GROUPE' | 'EQUIPE' | 'CANAL';
  createurId: number;
  description?: string;
  participantIds: number[];
}

export interface EnvoyerMessageRequest {
  contenu: string;
  type?: 'TEXTE' | 'IMAGE' | 'VIDEO' | 'AUDIO' | 'FICHIER';
  messageParentId?: number;
}

@Injectable({
  providedIn: 'root'
})
export class ConversationService {
  private apiUrl = `${environment.apiUrl}/api/conversations`;
  private conversationsSubject = new BehaviorSubject<Conversation[]>([]);
  private conversationActiveSubject = new BehaviorSubject<Conversation | null>(null);

  constructor(private http: HttpClient) {}

  // Observables pour les composants
  getConversations(): Observable<Conversation[]> {
    return this.conversationsSubject.asObservable();
  }

  getConversationActive(): Observable<Conversation | null> {
    return this.conversationActiveSubject.asObservable();
  }

  // Gestion des conversations
  creerConversation(request: CreerConversationRequest): Observable<Conversation> {
    return this.http.post<Conversation>(this.apiUrl, request).pipe(
      tap(conversation => {
        const conversations = this.conversationsSubject.value;
        this.conversationsSubject.next([conversation, ...conversations]);
      })
    );
  }

  obtenirConversationsUtilisateur(utilisateurId: number, page: number = 0, size: number = 20): Observable<any> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    return this.http.get<any>(`${this.apiUrl}/utilisateur/${utilisateurId}`, { params }).pipe(
      tap(response => {
        if (page === 0) {
          this.conversationsSubject.next(response.content);
        } else {
          const conversations = this.conversationsSubject.value;
          this.conversationsSubject.next([...conversations, ...response.content]);
        }
      })
    );
  }

  obtenirConversation(conversationId: number): Observable<Conversation> {
    return this.http.get<Conversation>(`${this.apiUrl}/${conversationId}`);
  }

  modifierConversation(conversationId: number, nom: string, description?: string): Observable<Conversation> {
    return this.http.put<Conversation>(`${this.apiUrl}/${conversationId}`, {
      nom,
      description
    }).pipe(
      tap(conversation => {
        const conversations = this.conversationsSubject.value;
        const index = conversations.findIndex(c => c.id === conversationId);
        if (index !== -1) {
          conversations[index] = conversation;
          this.conversationsSubject.next([...conversations]);
        }
      })
    );
  }

  archiverConversation(conversationId: number): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${conversationId}/archiver`, {}).pipe(
      tap(() => {
        const conversations = this.conversationsSubject.value;
        const index = conversations.findIndex(c => c.id === conversationId);
        if (index !== -1) {
          conversations[index].estArchive = true;
          this.conversationsSubject.next([...conversations]);
        }
      })
    );
  }

  supprimerConversation(conversationId: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${conversationId}`).pipe(
      tap(() => {
        const conversations = this.conversationsSubject.value;
        this.conversationsSubject.next(conversations.filter(c => c.id !== conversationId));
      })
    );
  }

  // Gestion des participants
  ajouterParticipants(conversationId: number, utilisateurIds: number[]): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${conversationId}/participants`, {
      utilisateurIds
    });
  }

  supprimerParticipant(conversationId: number, utilisateurId: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${conversationId}/participants/${utilisateurId}`);
  }

  modifierRoleParticipant(conversationId: number, utilisateurId: number, role: string): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${conversationId}/participants/${utilisateurId}/role`, {
      role
    });
  }

  quitterConversation(conversationId: number): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${conversationId}/quitter`, {}).pipe(
      tap(() => {
        const conversations = this.conversationsSubject.value;
        this.conversationsSubject.next(conversations.filter(c => c.id !== conversationId));
      })
    );
  }

  // Gestion des messages
  obtenirMessages(conversationId: number, page: number = 0, size: number = 50): Observable<any> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    return this.http.get<any>(`${this.apiUrl}/${conversationId}/messages`, { params });
  }

  envoyerMessage(conversationId: number, request: EnvoyerMessageRequest): Observable<Message> {
    return this.http.post<Message>(`${this.apiUrl}/${conversationId}/messages`, request);
  }

  modifierMessage(messageId: number, contenu: string): Observable<Message> {
    return this.http.put<Message>(`${this.apiUrl}/messages/${messageId}`, { contenu });
  }

  supprimerMessage(messageId: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/messages/${messageId}`);
  }

  epinglerMessage(messageId: number): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/messages/${messageId}/epingler`, {});
  }

  // Gestion des réactions
  ajouterReaction(messageId: number, emoji: string): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/messages/${messageId}/reactions`, { emoji });
  }

  supprimerReaction(messageId: number, emoji: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/messages/${messageId}/reactions/${emoji}`);
  }

  // Gestion des lectures
  marquerCommeLu(messageId: number): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/messages/${messageId}/lu`, {});
  }

  marquerConversationCommeLue(conversationId: number): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${conversationId}/marquer-lu`, {});
  }

  // Recherche
  rechercherMessages(conversationId: number, query: string, page: number = 0): Observable<any> {
    const params = new HttpParams()
      .set('q', query)
      .set('page', page.toString());

    return this.http.get<any>(`${this.apiUrl}/${conversationId}/rechercher`, { params });
  }

  rechercherConversations(query: string): Observable<Conversation[]> {
    const params = new HttpParams().set('q', query);
    return this.http.get<Conversation[]>(`${this.apiUrl}/rechercher`, { params });
  }

  // Utilitaires
  setConversationActive(conversation: Conversation | null): void {
    this.conversationActiveSubject.next(conversation);
  }

  obtenirStatistiquesConversation(conversationId: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/${conversationId}/statistiques`);
  }

  exporterConversation(conversationId: number): Observable<Blob> {
    return this.http.get(`${this.apiUrl}/${conversationId}/exporter`, {
      responseType: 'blob'
    });
  }

  // Nettoyage
  clearConversations(): void {
    this.conversationsSubject.next([]);
    this.conversationActiveSubject.next(null);
  }
}
