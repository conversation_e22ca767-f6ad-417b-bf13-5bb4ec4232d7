Write-Host "=== Test Microservice Planning Performance ===" -ForegroundColor Green
Write-Host "Club Olympique de Kelibia - Test Planning Performance Service" -ForegroundColor Cyan
Write-Host ""

# Aller dans le repertoire du microservice
Set-Location "microservices\planning-performance-service"

Write-Host "1. Verification des prerequis..." -ForegroundColor Yellow

# Verifier Java
try {
    $javaVersion = java -version 2>&1 | Select-String "version"
    Write-Host "   Java installe : $javaVersion" -ForegroundColor Green
} catch {
    Write-Host "   Java non trouve. Veuillez installer Java 17+" -ForegroundColor Red
    exit 1
}

# Verifier Maven
try {
    $mavenVersion = mvn -version 2>&1 | Select-String "Apache Maven"
    Write-Host "   Maven installe : $mavenVersion" -ForegroundColor Green
} catch {
    Write-Host "   Maven non trouve. Veuillez installer Maven" -ForegroundColor Red
    exit 1
}

# Verifier Node.js
try {
    $nodeVersion = node --version
    Write-Host "   Node.js installe : $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "   Node.js non trouve. Veuillez installer Node.js" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "2. Demarrage de la base de donnees PostgreSQL..." -ForegroundColor Yellow

# Demarrer la base de donnees
Write-Host "   Demarrage du conteneur PostgreSQL..." -ForegroundColor Cyan
docker-compose up -d planning-performance-db

# Attendre que la base soit prete
Write-Host "   Attente du demarrage de PostgreSQL (15 secondes)..." -ForegroundColor Cyan
Start-Sleep -Seconds 15

# Verifier la connexion a la base
try {
    $dbCheck = docker exec planning-performance-db pg_isready -U planning_user -d planning_performance_db
    if ($dbCheck -like "*accepting connections*") {
        Write-Host "   Base de donnees PostgreSQL prete!" -ForegroundColor Green
    } else {
        Write-Host "   Probleme avec la base de donnees" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   Impossible de verifier la base de donnees" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "3. Compilation et demarrage du backend Spring Boot..." -ForegroundColor Yellow

# Aller dans le repertoire backend
Set-Location "backend"

Write-Host "   Compilation du projet Maven..." -ForegroundColor Cyan
mvn clean compile -q

Write-Host "   Demarrage du serveur Spring Boot sur le port 8082..." -ForegroundColor Cyan
Start-Process -FilePath "mvn" -ArgumentList "spring-boot:run" -WindowStyle Normal

# Retourner au repertoire du microservice
Set-Location ".."

Write-Host ""
Write-Host "4. Installation et demarrage du frontend Angular..." -ForegroundColor Yellow

# Aller dans le repertoire frontend
Set-Location "frontend"

Write-Host "   Installation des dependances npm..." -ForegroundColor Cyan
npm install --silent

Write-Host "   Demarrage du serveur Angular sur le port 4202..." -ForegroundColor Cyan
Start-Process -FilePath "ng" -ArgumentList "serve", "--port", "4202", "--host", "0.0.0.0" -WindowStyle Normal

# Retourner au repertoire racine
Set-Location "..\..\.."

Write-Host ""
Write-Host "5. Verification des services..." -ForegroundColor Yellow
Write-Host "   Attente du demarrage complet (30 secondes)..." -ForegroundColor Cyan
Start-Sleep -Seconds 30

# Fonction pour verifier un service
function Test-Service {
    param(
        [string]$ServiceName,
        [string]$Url
    )
    
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "   $ServiceName : OK" -ForegroundColor Green
        } else {
            Write-Host "   $ServiceName : Status $($response.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "   $ServiceName : Non accessible" -ForegroundColor Red
    }
}

# Verifier les services
Write-Host "   Verification des services..." -ForegroundColor Cyan
Test-Service -ServiceName "Backend Spring Boot" -Url "http://localhost:8082/actuator/health"
Test-Service -ServiceName "Frontend Angular" -Url "http://localhost:4202"

Write-Host ""
Write-Host "=== Microservice Planning Performance Demarre ===" -ForegroundColor Green
Write-Host ""
Write-Host "URLs d'acces :" -ForegroundColor Cyan
Write-Host "   Frontend Angular    : http://localhost:4202" -ForegroundColor White
Write-Host "   Backend Spring Boot : http://localhost:8082" -ForegroundColor White
Write-Host "   API Health Check    : http://localhost:8082/actuator/health" -ForegroundColor White
Write-Host "   Base de donnees     : localhost:5434" -ForegroundColor White
Write-Host ""
Write-Host "API Endpoints principaux :" -ForegroundColor Cyan
Write-Host "   GET  /api/entrainements     - Liste des entrainements" -ForegroundColor White
Write-Host "   POST /api/entrainements     - Creer un entrainement" -ForegroundColor White
Write-Host "   GET  /api/participations    - Liste des participations" -ForegroundColor White
Write-Host "   GET  /api/performances      - Liste des performances" -ForegroundColor White
Write-Host "   GET  /api/objectifs         - Liste des objectifs" -ForegroundColor White
Write-Host "   GET  /api/statistiques      - Statistiques globales" -ForegroundColor White
Write-Host ""
Write-Host "Test des APIs :" -ForegroundColor Cyan
Write-Host "   curl http://localhost:8082/api/entrainements" -ForegroundColor White
Write-Host "   curl http://localhost:8082/api/performances" -ForegroundColor White
Write-Host "   curl http://localhost:8082/actuator/health" -ForegroundColor White
Write-Host ""
Write-Host "Pour arreter les services :" -ForegroundColor Yellow
Write-Host "   1. Fermez les fenetres Maven et Angular" -ForegroundColor White
Write-Host "   2. Arretez la base : docker-compose -f microservices/planning-performance-service/docker-compose.yml down" -ForegroundColor White

Read-Host "Appuyez sur Entree pour continuer"
