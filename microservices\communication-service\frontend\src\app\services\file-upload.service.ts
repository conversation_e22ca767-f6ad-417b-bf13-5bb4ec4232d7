import { Injectable } from '@angular/core';
import { HttpClient, HttpEvent, HttpEventType, HttpProgressEvent, HttpResponse } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, tap } from 'rxjs/operators';

// Configuration temporaire pour le développement
const environment = {
  production: false,
  apiUrl: 'http://localhost:8084'
};

export interface FileUploadProgress {
  fileName: string;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
  url?: string;
  error?: string;
}

export interface UploadedFile {
  id: string;
  nom: string;
  nomOriginal: string;
  taille: number;
  type: string;
  url: string;
  dateUpload: string;
}

@Injectable({
  providedIn: 'root'
})
export class FileUploadService {
  private apiUrl = `${environment.apiUrl}/api/fichiers`;
  private uploadsSubject = new BehaviorSubject<Map<string, FileUploadProgress>>(new Map());

  // Types de fichiers autorisés
  private readonly ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  private readonly ALLOWED_VIDEO_TYPES = ['video/mp4', 'video/webm', 'video/ogg'];
  private readonly ALLOWED_AUDIO_TYPES = ['audio/mp3', 'audio/wav', 'audio/ogg', 'audio/m4a'];
  private readonly ALLOWED_DOCUMENT_TYPES = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain'
  ];

  // Tailles maximales (en bytes)
  private readonly MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 10MB
  private readonly MAX_VIDEO_SIZE = 100 * 1024 * 1024; // 100MB
  private readonly MAX_AUDIO_SIZE = 50 * 1024 * 1024; // 50MB
  private readonly MAX_DOCUMENT_SIZE = 25 * 1024 * 1024; // 25MB

  constructor(private http: HttpClient) {}

  // Observable pour suivre les uploads en cours
  getUploads(): Observable<Map<string, FileUploadProgress>> {
    return this.uploadsSubject.asObservable();
  }

  // Validation des fichiers
  validateFile(file: File): { valid: boolean; error?: string } {
    // Vérifier le type de fichier
    const isImage = this.ALLOWED_IMAGE_TYPES.includes(file.type);
    const isVideo = this.ALLOWED_VIDEO_TYPES.includes(file.type);
    const isAudio = this.ALLOWED_AUDIO_TYPES.includes(file.type);
    const isDocument = this.ALLOWED_DOCUMENT_TYPES.includes(file.type);

    if (!isImage && !isVideo && !isAudio && !isDocument) {
      return {
        valid: false,
        error: `Type de fichier non autorisé: ${file.type}`
      };
    }

    // Vérifier la taille
    let maxSize: number;
    if (isImage) maxSize = this.MAX_IMAGE_SIZE;
    else if (isVideo) maxSize = this.MAX_VIDEO_SIZE;
    else if (isAudio) maxSize = this.MAX_AUDIO_SIZE;
    else maxSize = this.MAX_DOCUMENT_SIZE;

    if (file.size > maxSize) {
      return {
        valid: false,
        error: `Fichier trop volumineux. Taille maximale: ${this.formatFileSize(maxSize)}`
      };
    }

    return { valid: true };
  }

  // Upload d'un fichier avec suivi de progression
  uploadFile(file: File, conversationId?: number): Observable<UploadedFile> {
    const validation = this.validateFile(file);
    if (!validation.valid) {
      throw new Error(validation.error);
    }

    const formData = new FormData();
    formData.append('fichier', file);
    if (conversationId) {
      formData.append('conversationId', conversationId.toString());
    }

    const uploadId = this.generateUploadId();
    
    // Initialiser le suivi de progression
    this.updateUploadProgress(uploadId, {
      fileName: file.name,
      progress: 0,
      status: 'uploading'
    });

    return this.http.post<UploadedFile>(`${this.apiUrl}/upload`, formData, {
      reportProgress: true,
      observe: 'events'
    }).pipe(
      map((event: HttpEvent<UploadedFile>) => {
        switch (event.type) {
          case HttpEventType.UploadProgress:
            if (event.total) {
              const progress = Math.round(100 * event.loaded / event.total);
              this.updateUploadProgress(uploadId, {
                fileName: file.name,
                progress,
                status: 'uploading'
              });
            }
            return null;

          case HttpEventType.Response:
            this.updateUploadProgress(uploadId, {
              fileName: file.name,
              progress: 100,
              status: 'completed',
              url: event.body?.url
            });
            return event.body!;

          default:
            return null;
        }
      }),
      tap({
        error: (error) => {
          this.updateUploadProgress(uploadId, {
            fileName: file.name,
            progress: 0,
            status: 'error',
            error: error.message
          });
        }
      })
    ) as Observable<UploadedFile>;
  }

  // Upload multiple de fichiers
  uploadMultipleFiles(files: FileList, conversationId?: number): Observable<UploadedFile[]> {
    const uploads: Observable<UploadedFile>[] = [];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      uploads.push(this.uploadFile(file, conversationId));
    }

    return new Observable(observer => {
      const results: UploadedFile[] = [];
      let completed = 0;

      uploads.forEach((upload, index) => {
        upload.subscribe({
          next: (result) => {
            if (result) {
              results[index] = result;
              completed++;
              
              if (completed === uploads.length) {
                observer.next(results.filter(r => r !== null));
                observer.complete();
              }
            }
          },
          error: (error) => {
            observer.error(error);
          }
        });
      });
    });
  }

  // Gestion des fichiers
  obtenirFichier(ficherId: string): Observable<UploadedFile> {
    return this.http.get<UploadedFile>(`${this.apiUrl}/${ficherId}`);
  }

  supprimerFichier(ficherId: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${ficherId}`);
  }

  obtenirFichiersConversation(conversationId: number, page: number = 0): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/conversation/${conversationId}?page=${page}`);
  }

  // Génération de miniatures
  genererMiniature(ficherId: string, width: number = 200, height: number = 200): Observable<Blob> {
    return this.http.get(`${this.apiUrl}/${ficherId}/miniature?width=${width}&height=${height}`, {
      responseType: 'blob'
    });
  }

  // Téléchargement de fichiers
  telechargerFichier(ficherId: string, nomFichier: string): void {
    this.http.get(`${this.apiUrl}/${ficherId}/download`, {
      responseType: 'blob'
    }).subscribe(blob => {
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = nomFichier;
      link.click();
      window.URL.revokeObjectURL(url);
    });
  }

  // Utilitaires privées
  private generateUploadId(): string {
    return Math.random().toString(36).substring(2, 15);
  }

  private updateUploadProgress(uploadId: string, progress: FileUploadProgress): void {
    const uploads = this.uploadsSubject.value;
    uploads.set(uploadId, progress);
    this.uploadsSubject.next(new Map(uploads));

    // Nettoyer les uploads terminés après 5 secondes
    if (progress.status === 'completed' || progress.status === 'error') {
      setTimeout(() => {
        const currentUploads = this.uploadsSubject.value;
        currentUploads.delete(uploadId);
        this.uploadsSubject.next(new Map(currentUploads));
      }, 5000);
    }
  }

  // Utilitaires publiques
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  getFileIcon(type: string): string {
    if (this.ALLOWED_IMAGE_TYPES.includes(type)) return 'image';
    if (this.ALLOWED_VIDEO_TYPES.includes(type)) return 'videocam';
    if (this.ALLOWED_AUDIO_TYPES.includes(type)) return 'audiotrack';
    if (type === 'application/pdf') return 'picture_as_pdf';
    if (type.includes('word')) return 'description';
    if (type.includes('excel') || type.includes('sheet')) return 'table_chart';
    return 'attach_file';
  }

  isImage(type: string): boolean {
    return this.ALLOWED_IMAGE_TYPES.includes(type);
  }

  isVideo(type: string): boolean {
    return this.ALLOWED_VIDEO_TYPES.includes(type);
  }

  isAudio(type: string): boolean {
    return this.ALLOWED_AUDIO_TYPES.includes(type);
  }

  // Prévisualisation des fichiers
  createFilePreview(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      if (!this.isImage(file.type)) {
        reject('Le fichier n\'est pas une image');
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = () => reject('Erreur lors de la lecture du fichier');
      reader.readAsDataURL(file);
    });
  }

  // Nettoyage
  clearUploads(): void {
    this.uploadsSubject.next(new Map());
  }
}
