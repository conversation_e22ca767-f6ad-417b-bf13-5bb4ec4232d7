import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AuthService } from '@core/services/auth.service';
import { User } from '@core/models/common.model';

@Component({
  selector: 'app-navbar',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
      <div class="container-fluid">
        <!-- Brand -->
        <a class="navbar-brand d-flex align-items-center" routerLink="/dashboard">
          <i class="fas fa-heartbeat me-2 text-primary"></i>
          <span class="fw-bold">Medical Admin</span>
        </a>

        <!-- Mobile toggle -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
          <span class="navbar-toggler-icon"></span>
        </button>

        <!-- Navigation items -->
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav me-auto">
            <li class="nav-item">
              <a class="nav-link" routerLink="/dashboard" routerLinkActive="active">
                <i class="fas fa-tachometer-alt me-1"></i>
                Tableau de bord
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" routerLink="/donnees-sante" routerLinkActive="active">
                <i class="fas fa-notes-medical me-1"></i>
                Données de santé
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" routerLink="/rendez-vous" routerLinkActive="active">
                <i class="fas fa-calendar-alt me-1"></i>
                Rendez-vous
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" routerLink="/demandes-administratives" routerLinkActive="active">
                <i class="fas fa-file-alt me-1"></i>
                Demandes admin
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" routerLink="/rapports" routerLinkActive="active">
                <i class="fas fa-chart-bar me-1"></i>
                Rapports
              </a>
            </li>
          </ul>

          <!-- User menu -->
          <ul class="navbar-nav">
            <li class="nav-item dropdown" *ngIf="currentUser">
              <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" 
                 id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                <i class="fas fa-user-circle me-2"></i>
                {{ currentUser.prenom }} {{ currentUser.nom }}
              </a>
              <ul class="dropdown-menu dropdown-menu-end">
                <li>
                  <a class="dropdown-item" href="#">
                    <i class="fas fa-user me-2"></i>
                    Profil
                  </a>
                </li>
                <li>
                  <a class="dropdown-item" href="#">
                    <i class="fas fa-cog me-2"></i>
                    Paramètres
                  </a>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                  <a class="dropdown-item text-danger" href="#" (click)="logout()">
                    <i class="fas fa-sign-out-alt me-2"></i>
                    Déconnexion
                  </a>
                </li>
              </ul>
            </li>
          </ul>
        </div>
      </div>
    </nav>
  `,
  styles: [`
    .navbar {
      border-bottom: 1px solid #dee2e6;
    }
    
    .navbar-brand {
      font-size: 1.25rem;
    }
    
    .nav-link {
      font-weight: 500;
      transition: color 0.3s ease;
    }
    
    .nav-link:hover {
      color: var(--primary-color) !important;
    }
    
    .nav-link.active {
      color: var(--primary-color) !important;
      font-weight: 600;
    }
    
    .dropdown-menu {
      border: none;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .dropdown-item {
      padding: 0.5rem 1rem;
      transition: background-color 0.3s ease;
    }
    
    .dropdown-item:hover {
      background-color: #f8f9fa;
    }
    
    @media (max-width: 991px) {
      .navbar-nav {
        margin-top: 1rem;
      }
      
      .nav-link {
        padding: 0.5rem 0;
      }
    }
  `]
})
export class NavbarComponent {
  currentUser: User | null = null;

  constructor(private authService: AuthService) {
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
    });
  }

  logout(): void {
    this.authService.logout();
    // Redirection sera gérée par un guard ou dans le composant parent
  }
}
