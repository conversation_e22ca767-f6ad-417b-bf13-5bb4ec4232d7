* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:host {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    display: block;
    height: 100vh;
    overflow: hidden;
}

.app-container {
    display: flex;
    height: 100vh;
    width: 100vw;
}

/* Côté gauche - Formulaire */
.left-side {
    flex: 1;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    min-width: 400px;
}

.login-form-container {
    width: 100%;
    max-width: 400px;
}

.logo-section {
    margin-bottom: 2rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo-icon {
    width: 100px;
    height: 100px;
    object-fit: contain;
}

.logo-text {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1a1a1a;
}

.login-header {
    margin-bottom: 2rem;
}

.login-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 0.5rem;
}

.login-header p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
}

.form-group {
    margin-bottom: 1.5rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    color: #1a1a1a;
    font-weight: 500;
    font-size: 0.9rem;
}

input[type="email"],
input[type="password"] {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.2s, box-shadow 0.2s;
    background: #fff;
}

input[type="email"]:focus,
input[type="password"]:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

input::placeholder {
    color: #999;
}

.password-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    font-size: 1rem;
}

.forgot-password {
    margin-top: 0.5rem;
    text-align: right;
}

.forgot-link {
    color: #4CAF50;
    text-decoration: none;
    font-size: 0.85rem;
}

.forgot-link:hover {
    text-decoration: underline;
}

.btn-login {
    width: 100%;
    padding: 0.875rem;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
    margin-bottom: 1rem;
}

.btn-login:hover:not(:disabled) {
    background: #45a049;
}

.btn-login:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.divider {
    text-align: center;
    margin: 1.5rem 0;
    position: relative;
    color: #666;
    font-size: 0.9rem;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #ddd;
    z-index: 1;
}

.divider span {
    background: white;
    padding: 0 1rem;
    position: relative;
    z-index: 2;
}

.btn-facebook {
    width: 100%;
    padding: 0.875rem;
    background: #1877f2;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: background-color 0.2s;
}

.btn-facebook:hover {
    background: #166fe5;
}

.signup-link {
    text-align: center;
    margin-top: 2rem;
    font-size: 0.9rem;
    color: #666;
}

.create-account {
    color: #4CAF50;
    text-decoration: none;
    font-weight: 500;
}

.create-account:hover {
    text-decoration: underline;
}

/* Côté droit - Design moderne */
.right-side {
    flex: 1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.right-side::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.08)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    z-index: 1;
}

.right-content {
    width: 100%;
    max-width: 500px;
    color: white;
    position: relative;
    z-index: 2;
}

.chat-messages {
    margin-bottom: 3rem;
}

.message {
    display: flex;
    align-items: flex-end;
    margin-bottom: 1rem;
    animation: fadeInUp 0.6s ease-out;
}

.message-1 {
    justify-content: flex-end;
    animation-delay: 0.2s;
}

.message-2 {
    justify-content: flex-start;
    animation-delay: 0.4s;
}

.message-3 {
    justify-content: flex-end;
    animation-delay: 0.6s;
}

.message-bubble {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    padding: 0.75rem 1rem;
    border-radius: 18px;
    max-width: 250px;
    font-size: 0.9rem;
    line-height: 1.4;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 0.5rem;
    font-size: 0.8rem;
}

.feature-section {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    animation: fadeInUp 0.8s ease-out;
}

.feature-badge {
    background: #4CAF50;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.feature-section h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.feature-section p {
    font-size: 0.9rem;
    line-height: 1.5;
    opacity: 0.9;
    margin-bottom: 1.5rem;
}

.pagination-dots {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transition: background-color 0.2s;
}

.dot.active {
    background: white;
}

.help-button {
    position: absolute;
    bottom: 2rem;
    right: 2rem;
}

.btn-help {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s;
}

.btn-help:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Alertes */
.alert {
    padding: 0.75rem;
    border-radius: 6px;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.alert-error {
    background-color: #fee;
    color: #c33;
    border: 1px solid #fcc;
}

.alert-success {
    background-color: #efe;
    color: #363;
    border: 1px solid #cfc;
}

/* Loading */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

/* Overlay utilisateur connecté */
.user-info-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.user-info-content {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    max-width: 400px;
    width: 90%;
    text-align: center;
}

.user-info-content h3 {
    color: #4CAF50;
    margin-bottom: 1rem;
}

.user-info-content p {
    margin-bottom: 0.5rem;
    color: #333;
}

.btn-logout {
    background: #dc3545;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    margin-top: 1rem;
    font-weight: 500;
}

.btn-logout:hover {
    background: #c82333;
}

/* Animations */
@keyframes spin {
    to { transform: rotate(360deg); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .app-container {
        flex-direction: column;
    }

    .right-side {
        display: none;
    }

    .left-side {
        min-width: auto;
    }
}
