import { Routes } from '@angular/router';

export const DONNEES_SANTE_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./donnees-sante-list/donnees-sante-list.component').then(m => m.DonneesSanteListComponent)
  },
  {
    path: 'nouveau',
    loadComponent: () => import('./donnees-sante-form/donnees-sante-form.component').then(m => m.DonneesSanteFormComponent)
  },
  {
    path: 'edit/:id',
    loadComponent: () => import('./donnees-sante-form/donnees-sante-form.component').then(m => m.DonneesSanteFormComponent)
  },
  {
    path: 'detail/:id',
    loadComponent: () => import('./donnees-sante-detail/donnees-sante-detail.component').then(m => m.DonneesSanteDetailComponent)
  },
  {
    path: 'blessures',
    loadComponent: () => import('./donnees-sante-list/donnees-sante-list.component').then(m => m.Don<PERSON>anteListComponent),
    data: { filter: 'blessures' }
  },
  {
    path: 'joueur/:joueurId',
    loadComponent: () => import('./donnees-sante-list/donnees-sante-list.component').then(m => m.DonneesSanteListComponent),
    data: { filter: 'joueur' }
  }
];
