export interface DemandeAdministrative {
  id?: number;
  demandeurId: number;
  typeDemandeur: TypeDemandeur;
  approvateurId?: number;
  typeDemande: TypeDemande;
  titre: string;
  description: string;
  justification?: string;
  dateSoumission: string;
  dateEcheance?: string;
  statut: StatutDemande;
  priorite: PrioriteDemande;
  coutEstime?: number;
  coutReel?: number;
  commentaireApprobateur?: string;
  dateTraitement?: string;
  dateValidation?: string;
  necessiteValidationCoach: boolean;
  validationCoach?: boolean;
  necessiteValidationMedical: boolean;
  validationMedical?: boolean;
  necessiteValidationFinancier: boolean;
  validationFinancier?: boolean;
  documentsJoints?: string;
  historiqueStatuts?: string;
  createdAt?: string;
  updatedAt?: string;
}

export enum TypeDemandeur {
  JOUEUR = 'JOUEUR',
  STAFF = 'STAFF',
  COACH = 'COACH',
  MEDICAL = 'MEDICAL'
}

export enum TypeDemande {
  CONGE = 'CONGE',
  MATERIEL = 'MATERIEL',
  FORMATION = 'FORMATION',
  REMBOURSEMENT = 'REMBOURSEMENT',
  ACCES = 'ACCES',
  AUTRE = 'AUTRE'
}

export enum StatutDemande {
  EN_ATTENTE = 'EN_ATTENTE',
  EN_TRAITEMENT = 'EN_TRAITEMENT',
  VALIDEE = 'VALIDEE',
  REJETEE = 'REJETEE',
  SUSPENDUE = 'SUSPENDUE'
}

export enum PrioriteDemande {
  FAIBLE = 'FAIBLE',
  NORMALE = 'NORMALE',
  ELEVEE = 'ELEVEE',
  URGENTE = 'URGENTE'
}

export interface DemandeAdministrativeFilters {
  demandeurId?: number;
  typeDemandeur?: TypeDemandeur;
  typeDemande?: TypeDemande;
  statut?: StatutDemande;
  priorite?: PrioriteDemande;
  dateDebut?: string;
  dateFin?: string;
  coutMin?: number;
  coutMax?: number;
}

export interface DemandeAdministrativeStats {
  totalDemandes: number;
  demandesParType: { [key: string]: number };
  demandesParStatut: { [key: string]: number };
  demandesParPriorite: { [key: string]: number };
  coutTotal: number;
  delaiMoyenTraitement: number;
  tendancesMensuelles: { mois: string; nombre: number }[];
}

export interface ValidationWorkflow {
  etape: string;
  validateur: string;
  statut: 'EN_ATTENTE' | 'VALIDEE' | 'REJETEE';
  dateValidation?: string;
  commentaire?: string;
}
