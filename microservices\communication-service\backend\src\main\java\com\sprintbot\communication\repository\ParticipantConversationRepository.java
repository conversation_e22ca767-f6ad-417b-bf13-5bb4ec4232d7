package com.sprintbot.communication.repository;

import com.sprintbot.communication.entity.ParticipantConversation;
import com.sprintbot.communication.entity.RoleParticipant;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository pour l'entité ParticipantConversation
 */
@Repository
public interface ParticipantConversationRepository extends JpaRepository<ParticipantConversation, Long> {

    /**
     * Trouve les participants d'une conversation
     */
    List<ParticipantConversation> findByConversationIdOrderByDateAjoutAsc(Long conversationId);

    /**
     * Trouve un participant spécifique
     */
    Optional<ParticipantConversation> findByConversationIdAndUtilisateurId(
            Long conversationId, Long utilisateurId);

    /**
     * Trouve les participants par rôle
     */
    List<ParticipantConversation> findByConversationIdAndRoleOrderByDateAjoutAsc(
            Long conversationId, RoleParticipant role);

    /**
     * Trouve les administrateurs d'une conversation
     */
    List<ParticipantConversation> findByConversationIdAndRoleInOrderByDateAjoutAsc(
            Long conversationId, List<RoleParticipant> roles);

    /**
     * Vérifie si un utilisateur est participant
     */
    boolean existsByConversationIdAndUtilisateurId(Long conversationId, Long utilisateurId);

    /**
     * Compte les participants d'une conversation
     */
    Long countByConversationId(Long conversationId);

    /**
     * Trouve les conversations épinglées d'un utilisateur
     */
    List<ParticipantConversation> findByUtilisateurIdAndEstEpingleeTrueOrderByDateModificationDesc(
            Long utilisateurId);

    /**
     * Trouve les conversations silencieuses d'un utilisateur
     */
    List<ParticipantConversation> findByUtilisateurIdAndEstSilencieuxTrueOrderByDateModificationDesc(
            Long utilisateurId);

    /**
     * Trouve les participants avec notifications activées
     */
    List<ParticipantConversation> findByConversationIdAndNotificationsActiveesTrueOrderByDateAjoutAsc(
            Long conversationId);

    /**
     * Supprime un participant
     */
    void deleteByConversationIdAndUtilisateurId(Long conversationId, Long utilisateurId);

    /**
     * Met à jour la date de dernière lecture
     */
    @Query("UPDATE ParticipantConversation p " +
           "SET p.dateDerniereLecture = :maintenant " +
           "WHERE p.conversation.id = :conversationId " +
           "AND p.utilisateurId = :utilisateurId")
    void mettreAJourDerniereLecture(
            @Param("conversationId") Long conversationId,
            @Param("utilisateurId") Long utilisateurId,
            @Param("maintenant") LocalDateTime maintenant);

    /**
     * Trouve les participants actifs récemment
     */
    @Query("SELECT p FROM ParticipantConversation p " +
           "WHERE p.conversation.id = :conversationId " +
           "AND p.dateDerniereLecture > :depuis " +
           "ORDER BY p.dateDerniereLecture DESC")
    List<ParticipantConversation> findParticipantsActifsRecemment(
            @Param("conversationId") Long conversationId,
            @Param("depuis") LocalDateTime depuis);

    /**
     * Statistiques de participation
     */
    @Query("SELECT p.role, COUNT(p) FROM ParticipantConversation p " +
           "WHERE p.conversation.id = :conversationId " +
           "GROUP BY p.role")
    List<Object[]> getStatistiquesRoles(@Param("conversationId") Long conversationId);
}
