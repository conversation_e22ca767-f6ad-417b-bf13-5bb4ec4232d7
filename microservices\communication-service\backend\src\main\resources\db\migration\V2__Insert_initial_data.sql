-- Migration V2: Insertion des données initiales
-- Auteur: SprintBot Team
-- Date: 2024-01-15

-- Insertion des préférences de notification par défaut pour les nouveaux utilisateurs
-- Ces préférences seront utilisées comme modèle lors de la création d'un nouvel utilisateur

-- Préférences par défaut pour les notifications de nouveaux messages
INSERT INTO communication.preferences_notification (utilisateur_id, type_notification, canal, active) VALUES
(0, 'NOUVEAU_MESSAGE', 'PUSH', true),
(0, 'NOUVEAU_MESSAGE', 'EMAIL', false),
(0, 'NOUVEAU_MESSAGE', 'SMS', false),
(0, 'NOUVEAU_MESSAGE', 'INTERNE', true);

-- Préférences par défaut pour les notifications de nouvelles conversations
INSERT INTO communication.preferences_notification (utilisateur_id, type_notification, canal, active) VALUES
(0, 'NOUVELLE_CONVERSATION', 'PUSH', true),
(0, 'NOUVELLE_CONVERSATION', 'EMAIL', true),
(0, 'NOUVELLE_CONVERSATION', 'SMS', false),
(0, 'NOUVELLE_CONVERSATION', 'INTERNE', true);

-- Préférences par défaut pour les notifications d'ajout à une conversation
INSERT INTO communication.preferences_notification (utilisateur_id, type_notification, canal, active) VALUES
(0, 'AJOUT_CONVERSATION', 'PUSH', true),
(0, 'AJOUT_CONVERSATION', 'EMAIL', true),
(0, 'AJOUT_CONVERSATION', 'SMS', false),
(0, 'AJOUT_CONVERSATION', 'INTERNE', true);

-- Préférences par défaut pour les notifications de suppression de conversation
INSERT INTO communication.preferences_notification (utilisateur_id, type_notification, canal, active) VALUES
(0, 'SUPPRESSION_CONVERSATION', 'PUSH', true),
(0, 'SUPPRESSION_CONVERSATION', 'EMAIL', false),
(0, 'SUPPRESSION_CONVERSATION', 'SMS', false),
(0, 'SUPPRESSION_CONVERSATION', 'INTERNE', true);

-- Préférences par défaut pour les notifications d'escalade chatbot
INSERT INTO communication.preferences_notification (utilisateur_id, type_notification, canal, active) VALUES
(0, 'ESCALADE_CHATBOT', 'PUSH', true),
(0, 'ESCALADE_CHATBOT', 'EMAIL', true),
(0, 'ESCALADE_CHATBOT', 'SMS', false),
(0, 'ESCALADE_CHATBOT', 'INTERNE', true);

-- Préférences par défaut pour les notifications système
INSERT INTO communication.preferences_notification (utilisateur_id, type_notification, canal, active) VALUES
(0, 'SYSTEME', 'PUSH', true),
(0, 'SYSTEME', 'EMAIL', true),
(0, 'SYSTEME', 'SMS', false),
(0, 'SYSTEME', 'INTERNE', true);

-- Commentaire sur les données par défaut
COMMENT ON TABLE communication.preferences_notification IS 'Les préférences avec utilisateur_id = 0 servent de modèle pour les nouveaux utilisateurs';

-- Création d'une fonction pour initialiser les préférences d'un nouvel utilisateur
CREATE OR REPLACE FUNCTION communication.initialiser_preferences_utilisateur(p_utilisateur_id BIGINT)
RETURNS VOID AS $$
BEGIN
    -- Copier les préférences par défaut (utilisateur_id = 0) pour le nouvel utilisateur
    INSERT INTO communication.preferences_notification (utilisateur_id, type_notification, canal, active, date_creation)
    SELECT 
        p_utilisateur_id,
        type_notification,
        canal,
        active,
        CURRENT_TIMESTAMP
    FROM communication.preferences_notification
    WHERE utilisateur_id = 0
    ON CONFLICT (utilisateur_id, type_notification, canal) DO NOTHING;
END;
$$ LANGUAGE plpgsql;

-- Commentaire sur la fonction
COMMENT ON FUNCTION communication.initialiser_preferences_utilisateur(BIGINT) IS 'Initialise les préférences de notification pour un nouvel utilisateur';

-- Création d'une fonction pour nettoyer les anciennes notifications
CREATE OR REPLACE FUNCTION communication.nettoyer_anciennes_notifications()
RETURNS INTEGER AS $$
DECLARE
    notifications_supprimees INTEGER;
BEGIN
    -- Supprimer les notifications lues de plus de 30 jours
    DELETE FROM communication.notifications 
    WHERE statut = 'LU' 
    AND date_lecture < CURRENT_TIMESTAMP - INTERVAL '30 days';
    
    GET DIAGNOSTICS notifications_supprimees = ROW_COUNT;
    
    -- Supprimer les notifications expirées de plus de 7 jours
    DELETE FROM communication.notifications 
    WHERE statut = 'EXPIRE' 
    AND date_creation < CURRENT_TIMESTAMP - INTERVAL '7 days';
    
    GET DIAGNOSTICS notifications_supprimees = notifications_supprimees + ROW_COUNT;
    
    RETURN notifications_supprimees;
END;
$$ LANGUAGE plpgsql;

-- Commentaire sur la fonction de nettoyage
COMMENT ON FUNCTION communication.nettoyer_anciennes_notifications() IS 'Nettoie les anciennes notifications pour optimiser les performances';

-- Création d'une fonction pour obtenir les statistiques de conversation
CREATE OR REPLACE FUNCTION communication.obtenir_statistiques_conversation(p_conversation_id BIGINT)
RETURNS TABLE(
    nombre_participants BIGINT,
    nombre_messages BIGINT,
    derniere_activite TIMESTAMP
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*) FROM communication.participants_conversation WHERE conversation_id = p_conversation_id),
        (SELECT COUNT(*) FROM communication.messages WHERE conversation_id = p_conversation_id AND est_supprime = false),
        (SELECT MAX(date_creation) FROM communication.messages WHERE conversation_id = p_conversation_id);
END;
$$ LANGUAGE plpgsql;

-- Commentaire sur la fonction de statistiques
COMMENT ON FUNCTION communication.obtenir_statistiques_conversation(BIGINT) IS 'Retourne les statistiques d''une conversation';

-- Création d'une vue pour les messages avec informations enrichies
CREATE OR REPLACE VIEW communication.v_messages_enrichis AS
SELECT 
    m.id,
    m.conversation_id,
    m.expediteur_id,
    m.contenu,
    m.type,
    m.url_fichier,
    m.message_parent_id,
    m.est_modifie,
    m.est_supprime,
    m.est_epingle,
    m.date_creation,
    m.date_modification,
    c.nom as nom_conversation,
    c.type as type_conversation,
    -- Nombre de réactions
    (SELECT COUNT(*) FROM communication.reactions_message WHERE message_id = m.id) as nombre_reactions,
    -- Nombre de lectures
    (SELECT COUNT(*) FROM communication.lectures_message WHERE message_id = m.id) as nombre_lectures,
    -- Message parent (pour les réponses)
    mp.contenu as contenu_message_parent,
    mp.expediteur_id as expediteur_message_parent
FROM communication.messages m
JOIN communication.conversations c ON m.conversation_id = c.id
LEFT JOIN communication.messages mp ON m.message_parent_id = mp.id
WHERE m.est_supprime = false;

-- Commentaire sur la vue
COMMENT ON VIEW communication.v_messages_enrichis IS 'Vue enrichie des messages avec informations de conversation et statistiques';

-- Création d'une vue pour les notifications en attente
CREATE OR REPLACE VIEW communication.v_notifications_en_attente AS
SELECT 
    n.*,
    CASE 
        WHEN n.date_programmee IS NULL THEN true
        WHEN n.date_programmee <= CURRENT_TIMESTAMP THEN true
        ELSE false
    END as pret_a_envoyer
FROM communication.notifications n
WHERE n.statut IN ('EN_ATTENTE', 'ERREUR')
AND n.nombre_tentatives < 3;

-- Commentaire sur la vue des notifications
COMMENT ON VIEW communication.v_notifications_en_attente IS 'Vue des notifications prêtes à être envoyées';

-- Création d'une vue pour les statistiques de présence
CREATE OR REPLACE VIEW communication.v_statistiques_presence AS
SELECT 
    statut,
    COUNT(*) as nombre_utilisateurs,
    COUNT(*) * 100.0 / SUM(COUNT(*)) OVER() as pourcentage
FROM communication.presence_utilisateur
WHERE invisible = false
GROUP BY statut;

-- Commentaire sur la vue des statistiques de présence
COMMENT ON VIEW communication.v_statistiques_presence IS 'Vue des statistiques de présence des utilisateurs';

-- Insertion de données de test pour le développement (optionnel)
-- Ces données seront supprimées en production

-- Conversation de test pour l'équipe de développement
INSERT INTO communication.conversations (id, nom, description, type, createur_id) VALUES
(1, 'Équipe SprintBot', 'Conversation de l''équipe de développement SprintBot', 'EQUIPE', 1);

-- Participants de test
INSERT INTO communication.participants_conversation (conversation_id, utilisateur_id, role) VALUES
(1, 1, 'ADMIN'),
(1, 2, 'MEMBRE'),
(1, 3, 'MEMBRE');

-- Message de bienvenue
INSERT INTO communication.messages (conversation_id, expediteur_id, contenu, type) VALUES
(1, 1, 'Bienvenue dans le système de communication SprintBot! 🏐', 'TEXTE');

-- Notification de bienvenue système
INSERT INTO communication.notifications (destinataire_id, type, canal, titre, contenu, priorite) VALUES
(1, 'SYSTEME', 'INTERNE', 'Bienvenue dans SprintBot', 'Votre service de communication est maintenant actif!', 'NORMALE');

-- Conversation chatbot de démonstration
INSERT INTO communication.conversations_chatbot (id, utilisateur_id, session_id, langue, statut) VALUES
(1, 1, 'demo-session-001', 'fr', 'ACTIF');

-- Messages chatbot de démonstration
INSERT INTO communication.messages_chatbot (conversation_id, type, contenu, intention_detectee, confiance_score) VALUES
(1, 'BOT', 'Bonjour ! Je suis votre assistant virtuel SprintBot. Comment puis-je vous aider aujourd''hui ?', 'salutation', 1.0),
(1, 'UTILISATEUR', 'Bonjour, j''aimerais savoir comment planifier un entraînement', 'planning', 0.9),
(1, 'BOT', 'Je peux vous aider avec la planification d''entraînements ! Vous pouvez accéder au module de planification depuis le menu principal. Souhaitez-vous que je vous guide ?', 'planning', 0.95);

-- Présence de test pour les utilisateurs de démonstration
INSERT INTO communication.presence_utilisateur (utilisateur_id, statut, plateforme, est_mobile) VALUES
(1, 'EN_LIGNE', 'web', false),
(2, 'ABSENT', 'mobile', true),
(3, 'HORS_LIGNE', 'web', false);

-- Mise à jour de la séquence pour éviter les conflits d'ID
SELECT setval('communication.conversations_id_seq', 100);
SELECT setval('communication.conversations_chatbot_id_seq', 100);
