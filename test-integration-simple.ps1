Write-Host "=== TEST D'INTÉGRATION MICROSERVICES ===" -ForegroundColor Green
Write-Host "Club Olympique de Kelibia" -ForegroundColor Cyan
Write-Host ""

# Variables
$baseUrl = "http://localhost"
$services = @(
    @{ Name = "Discovery Service"; Port = "8761"; Path = "/actuator/health" },
    @{ Name = "Gateway Service"; Port = "8080"; Path = "/actuator/health" },
    @{ Name = "Auth User Service"; Port = "8081"; Path = "/actuator/health" },
    @{ Name = "Planning Performance Service"; Port = "8082"; Path = "/actuator/health" },
    @{ Name = "Frontend Angular"; Port = "4201"; Path = "/" }
)

Write-Host "🎯 ARCHITECTURE TESTÉE :" -ForegroundColor Yellow
Write-Host "Frontend (4201) → Gateway (8080) → Auth (8081) + Planning (8082)" -ForegroundColor White
Write-Host "                                  ↓" -ForegroundColor White
Write-Host "                            Discovery (8761)" -ForegroundColor White
Write-Host ""

Write-Host "1. TEST DES SERVICES" -ForegroundColor Yellow
Write-Host "====================" -ForegroundColor Yellow

$servicesUp = 0
foreach ($service in $services) {
    $url = "$baseUrl`:$($service.Port)$($service.Path)"
    Write-Host "🔍 Test de $($service.Name)..." -ForegroundColor Cyan
    
    try {
        $response = Invoke-WebRequest -Uri $url -UseBasicParsing -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $($service.Name) opérationnel (Port $($service.Port))" -ForegroundColor Green
            $servicesUp++
        } else {
            Write-Host "⚠️  $($service.Name) répond avec le code $($response.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ $($service.Name) non accessible" -ForegroundColor Red
    }
    Start-Sleep -Seconds 1
}

Write-Host ""
Write-Host "RÉSULTAT : $servicesUp/$($services.Count) services opérationnels" -ForegroundColor $(if ($servicesUp -eq $services.Count) { "Green" } elseif ($servicesUp -ge 3) { "Yellow" } else { "Red" })
Write-Host ""

if ($servicesUp -lt 3) {
    Write-Host "⚠️  Pas assez de services démarrés pour continuer les tests." -ForegroundColor Yellow
    Write-Host "Pour démarrer tous les services :" -ForegroundColor Cyan
    Write-Host "docker-compose -f docker-compose.integration.yml up -d" -ForegroundColor White
    Write-Host ""
    Write-Host "Ou démarrez individuellement avec les IDEs." -ForegroundColor White
    Read-Host "Appuyez sur Entrée pour terminer"
    exit
}

Write-Host "2. TEST D'AUTHENTIFICATION" -ForegroundColor Yellow
Write-Host "===========================" -ForegroundColor Yellow

# Test de création d'utilisateur
Write-Host "🔐 Test de création d'utilisateur..." -ForegroundColor Cyan
$timestamp = Get-Date -Format "HHmmss"
$registerData = @{
    username = "testuser_$timestamp"
    email = "test$<EMAIL>"
    password = "TestPassword123!"
    nom = "Test"
    prenom = "User"
    role = "JOUEUR"
} | ConvertTo-Json

$userCreated = $false
try {
    $registerResponse = Invoke-WebRequest -Uri "$baseUrl`:8080/auth-user-service/api/auth/register" -Method POST -ContentType "application/json" -Body $registerData -UseBasicParsing -TimeoutSec 10
    
    if ($registerResponse.StatusCode -eq 201) {
        Write-Host "✅ Utilisateur créé avec succès" -ForegroundColor Green
        $userCreated = $true
    }
} catch {
    Write-Host "❌ Erreur création utilisateur : $($_.Exception.Message)" -ForegroundColor Red
}

# Test de connexion
$token = $null
if ($userCreated) {
    Write-Host "🔑 Test de connexion..." -ForegroundColor Cyan
    $loginData = @{
        username = "testuser_$timestamp"
        password = "TestPassword123!"
    } | ConvertTo-Json
    
    try {
        $loginResponse = Invoke-WebRequest -Uri "$baseUrl`:8080/auth-user-service/api/auth/login" -Method POST -ContentType "application/json" -Body $loginData -UseBasicParsing -TimeoutSec 10
        
        if ($loginResponse.StatusCode -eq 200) {
            Write-Host "✅ Connexion réussie" -ForegroundColor Green
            $loginResult = $loginResponse.Content | ConvertFrom-Json
            $token = $loginResult.token
            Write-Host "🎫 Token JWT obtenu" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ Erreur connexion : $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "3. TEST DES APIS" -ForegroundColor Yellow
Write-Host "================" -ForegroundColor Yellow

if ($token) {
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    # Test API Entraînements
    Write-Host "📋 Test API Entraînements..." -ForegroundColor Cyan
    try {
        $entrainementsResponse = Invoke-WebRequest -Uri "$baseUrl`:8080/planning-performance-service/api/entrainements" -Headers $headers -UseBasicParsing -TimeoutSec 10
        
        if ($entrainementsResponse.StatusCode -eq 200) {
            Write-Host "✅ API Entraînements accessible" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ Erreur API Entraînements" -ForegroundColor Red
    }
    
    # Test API Utilisateurs
    Write-Host "👥 Test API Utilisateurs..." -ForegroundColor Cyan
    try {
        $usersResponse = Invoke-WebRequest -Uri "$baseUrl`:8080/auth-user-service/api/users" -Headers $headers -UseBasicParsing -TimeoutSec 10
        
        if ($usersResponse.StatusCode -eq 200) {
            Write-Host "✅ API Utilisateurs accessible" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ Erreur API Utilisateurs" -ForegroundColor Red
    }
} else {
    Write-Host "⚠️  Pas de token JWT - Tests APIs ignorés" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "4. TEST DE CRÉATION DE DONNÉES" -ForegroundColor Yellow
Write-Host "===============================" -ForegroundColor Yellow

if ($token) {
    Write-Host "🏐 Création d'un entraînement test..." -ForegroundColor Cyan
    $entrainementData = @{
        titre = "Entraînement Test COK"
        date = (Get-Date).AddDays(1).ToString("yyyy-MM-dd")
        heureDebut = "18:00"
        heureFin = "20:00"
        lieu = "Gymnase Municipal Kelibia"
        type = "TECHNIQUE"
        intensite = 7
        description = "Test d'intégration microservices"
    } | ConvertTo-Json
    
    try {
        $createResponse = Invoke-WebRequest -Uri "$baseUrl`:8080/planning-performance-service/api/entrainements" -Method POST -Headers $headers -Body $entrainementData -UseBasicParsing -TimeoutSec 10
        
        if ($createResponse.StatusCode -eq 201) {
            Write-Host "✅ Entraînement créé avec succès" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ Erreur création entraînement" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== RÉSUMÉ DU TEST ===" -ForegroundColor Green
Write-Host ""

if ($servicesUp -eq $services.Count -and $userCreated -and $token) {
    Write-Host "🎉 SUCCÈS COMPLET !" -ForegroundColor Green
    Write-Host "✅ Tous les microservices opérationnels" -ForegroundColor Green
    Write-Host "✅ Authentification fonctionnelle" -ForegroundColor Green
    Write-Host "✅ APIs accessibles" -ForegroundColor Green
    Write-Host "✅ Intégration réussie" -ForegroundColor Green
} elseif ($servicesUp -ge 3) {
    Write-Host "🎯 SUCCÈS PARTIEL" -ForegroundColor Yellow
    Write-Host "✅ Services principaux opérationnels" -ForegroundColor Green
    Write-Host "⚠️  Quelques fonctionnalités à vérifier" -ForegroundColor Yellow
} else {
    Write-Host "⚠️  INTÉGRATION INCOMPLÈTE" -ForegroundColor Yellow
    Write-Host "❌ Plusieurs services non démarrés" -ForegroundColor Red
}

Write-Host ""
Write-Host "URLS DISPONIBLES :" -ForegroundColor Cyan
Write-Host "🌐 Frontend : http://localhost:4201" -ForegroundColor White
Write-Host "🚪 Gateway : http://localhost:8080" -ForegroundColor White
Write-Host "🔍 Discovery : http://localhost:8761" -ForegroundColor White
Write-Host "🔐 Auth API : http://localhost:8081" -ForegroundColor White
Write-Host "📋 Planning API : http://localhost:8082" -ForegroundColor White
Write-Host ""

Write-Host "COMMANDES UTILES :" -ForegroundColor Cyan
Write-Host "Démarrer tous les services :" -ForegroundColor White
Write-Host "docker-compose -f docker-compose.integration.yml up -d" -ForegroundColor Gray
Write-Host ""
Write-Host "Arrêter tous les services :" -ForegroundColor White
Write-Host "docker-compose -f docker-compose.integration.yml down" -ForegroundColor Gray
Write-Host ""

Write-Host "🏐 Club Olympique de Kelibia - Test d'intégration terminé !" -ForegroundColor Yellow

Read-Host "Appuyez sur Entrée pour continuer"
