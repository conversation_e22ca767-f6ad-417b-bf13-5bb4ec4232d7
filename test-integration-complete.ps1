Write-Host "=== TEST D'INTÉGRATION COMPLET - MICROSERVICES ===" -ForegroundColor Green
Write-Host "Club Olympique de Kelibia - Plateforme Intelligente Volley-Ball" -ForegroundColor Cyan
Write-Host ""

# Variables globales
$baseUrl = "http://localhost"
$gatewayPort = "8080"
$discoveryPort = "8761"
$authPort = "8081"
$planningPort = "8082"
$frontendPort = "4201"

Write-Host "🎯 OBJECTIF : Tester l'intégration complète des microservices" -ForegroundColor Yellow
Write-Host ""

Write-Host "ARCHITECTURE TESTÉE :" -ForegroundColor Yellow
Write-Host "┌─────────────────────────────────────────────────────────────┐" -ForegroundColor White
Write-Host "│                    FRONTEND ANGULAR                         │" -ForegroundColor White
Write-Host "│                  (Port 4201)                                │" -ForegroundColor White
Write-Host "└─────────────────────┬───────────────────────────────────────┘" -ForegroundColor White
Write-Host "                      │" -ForegroundColor White
Write-Host "┌─────────────────────▼───────────────────────────────────────┐" -ForegroundColor White
Write-Host "│                 GATEWAY SERVICE                             │" -ForegroundColor White
Write-Host "│                  (Port 8080)                                │" -ForegroundColor White
Write-Host "└─────────────┬───────────────────────┬─────────────────────────┘" -ForegroundColor White
Write-Host "              │                       │" -ForegroundColor White
Write-Host "┌─────────────▼─────────────┐ ┌───────▼─────────────────────────┐" -ForegroundColor White
Write-Host "│    AUTH-USER-SERVICE      │ │  PLANNING-PERFORMANCE-SERVICE   │" -ForegroundColor White
Write-Host "│       (Port 8081)         │ │         (Port 8082)             │" -ForegroundColor White
Write-Host "└───────────────────────────┘ └─────────────────────────────────┘" -ForegroundColor White
Write-Host "              │                         │" -ForegroundColor White
Write-Host "┌─────────────▼─────────────┐ ┌───────▼─────────────────────────┐" -ForegroundColor White
Write-Host "│     PostgreSQL DB         │ │      PostgreSQL DB              │" -ForegroundColor White
Write-Host "│       (Port 5433)         │ │        (Port 5434)              │" -ForegroundColor White
Write-Host "└───────────────────────────┘ └─────────────────────────────────┘" -ForegroundColor White
Write-Host ""

Write-Host "1. VÉRIFICATION DES SERVICES" -ForegroundColor Yellow
Write-Host "=============================" -ForegroundColor Yellow

# Fonction pour tester un service
function Test-Service {
    param(
        [string]$ServiceName,
        [string]$Url,
        [int]$TimeoutSeconds = 10
    )
    
    Write-Host "🔍 Test de $ServiceName..." -ForegroundColor Cyan
    try {
        $response = Invoke-WebRequest -Uri $Url -UseBasicParsing -TimeoutSec $TimeoutSeconds
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $ServiceName opérationnel" -ForegroundColor Green
            return $true
        } else {
            Write-Host "⚠️  $ServiceName répond avec le code $($response.StatusCode)" -ForegroundColor Yellow
            return $false
        }
    } catch {
        Write-Host "❌ $ServiceName non accessible : $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Test des services principaux
$services = @(
    @{ Name = "Discovery Service"; Url = "$baseUrl`:$discoveryPort/actuator/health" },
    @{ Name = "Gateway Service"; Url = "$baseUrl`:$gatewayPort/actuator/health" },
    @{ Name = "Auth User Service"; Url = "$baseUrl`:$authPort/actuator/health" },
    @{ Name = "Planning Performance Service"; Url = "$baseUrl`:$planningPort/actuator/health" },
    @{ Name = "Frontend Angular"; Url = "$baseUrl`:$frontendPort" }
)

$servicesUp = 0
foreach ($service in $services) {
    if (Test-Service -ServiceName $service.Name -Url $service.Url) {
        $servicesUp++
    }
    Start-Sleep -Seconds 1
}

Write-Host ""
Write-Host "RÉSULTAT : $servicesUp/$($services.Count) services opérationnels" -ForegroundColor $(if ($servicesUp -eq $services.Count) { "Green" } else { "Yellow" })
Write-Host ""

if ($servicesUp -lt $services.Count) {
    Write-Host "⚠️  Certains services ne sont pas démarrés." -ForegroundColor Yellow
    Write-Host "Pour démarrer tous les services :" -ForegroundColor Cyan
    Write-Host "docker-compose -f docker-compose.integration.yml up -d" -ForegroundColor White
    Write-Host ""
}

Write-Host "2. TEST D'AUTHENTIFICATION" -ForegroundColor Yellow
Write-Host "===========================" -ForegroundColor Yellow

# Test de création d'utilisateur
Write-Host "🔐 Test de création d'utilisateur..." -ForegroundColor Cyan
$registerData = @{
    username = "testuser_$(Get-Date -Format 'HHmmss')"
    email = "test$(Get-Date -Format 'HHmmss')@cok.tn"
    password = "TestPassword123!"
    nom = "Test"
    prenom = "User"
    role = "JOUEUR"
} | ConvertTo-Json

try {
    $registerResponse = Invoke-WebRequest -Uri "$baseUrl`:$gatewayPort/auth-user-service/api/auth/register" `
        -Method POST `
        -ContentType "application/json" `
        -Body $registerData `
        -UseBasicParsing `
        -TimeoutSec 10
    
    if ($registerResponse.StatusCode -eq 201) {
        Write-Host "✅ Utilisateur créé avec succès" -ForegroundColor Green
        $userCreated = $true
    } else {
        Write-Host "⚠️  Création d'utilisateur : Code $($registerResponse.StatusCode)" -ForegroundColor Yellow
        $userCreated = $false
    }
} catch {
    Write-Host "❌ Erreur création utilisateur : $($_.Exception.Message)" -ForegroundColor Red
    $userCreated = $false
}

# Test de connexion
if ($userCreated) {
    Write-Host "🔑 Test de connexion..." -ForegroundColor Cyan
    $loginData = @{
        username = ($registerData | ConvertFrom-Json).username
        password = ($registerData | ConvertFrom-Json).password
    } | ConvertTo-Json
    
    try {
        $loginResponse = Invoke-WebRequest -Uri "$baseUrl`:$gatewayPort/auth-user-service/api/auth/login" `
            -Method POST `
            -ContentType "application/json" `
            -Body $loginData `
            -UseBasicParsing `
            -TimeoutSec 10
        
        if ($loginResponse.StatusCode -eq 200) {
            Write-Host "✅ Connexion réussie" -ForegroundColor Green
            $loginResult = $loginResponse.Content | ConvertFrom-Json
            $token = $loginResult.token
            Write-Host "🎫 Token JWT obtenu" -ForegroundColor Green
        } else {
            Write-Host "❌ Échec de connexion : Code $($loginResponse.StatusCode)" -ForegroundColor Red
            $token = $null
        }
    } catch {
        Write-Host "❌ Erreur connexion : $($_.Exception.Message)" -ForegroundColor Red
        $token = $null
    }
}

Write-Host ""
Write-Host "3. TEST DES APIS MÉTIER" -ForegroundColor Yellow
Write-Host "========================" -ForegroundColor Yellow

if ($token) {
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    # Test API Planning Performance
    Write-Host "📋 Test API Entraînements..." -ForegroundColor Cyan
    try {
        $entrainementsResponse = Invoke-WebRequest -Uri "$baseUrl`:$gatewayPort/planning-performance-service/api/entrainements" `
            -Headers $headers `
            -UseBasicParsing `
            -TimeoutSec 10
        
        if ($entrainementsResponse.StatusCode -eq 200) {
            Write-Host "✅ API Entraînements accessible" -ForegroundColor Green
            $entrainements = $entrainementsResponse.Content | ConvertFrom-Json
            Write-Host "📊 Nombre d'entraînements : $($entrainements.Count)" -ForegroundColor Cyan
        }
    } catch {
        Write-Host "❌ Erreur API Entraînements : $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Test API Performances
    Write-Host "📈 Test API Performances..." -ForegroundColor Cyan
    try {
        $performancesResponse = Invoke-WebRequest -Uri "$baseUrl`:$gatewayPort/planning-performance-service/api/performances" `
            -Headers $headers `
            -UseBasicParsing `
            -TimeoutSec 10
        
        if ($performancesResponse.StatusCode -eq 200) {
            Write-Host "✅ API Performances accessible" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ Erreur API Performances : $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Test API Utilisateurs
    Write-Host "👥 Test API Utilisateurs..." -ForegroundColor Cyan
    try {
        $usersResponse = Invoke-WebRequest -Uri "$baseUrl`:$gatewayPort/auth-user-service/api/users" `
            -Headers $headers `
            -UseBasicParsing `
            -TimeoutSec 10
        
        if ($usersResponse.StatusCode -eq 200) {
            Write-Host "✅ API Utilisateurs accessible" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ Erreur API Utilisateurs : $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "⚠️  Pas de token JWT - Tests APIs ignorés" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "4. TEST DE CRÉATION DE DONNÉES" -ForegroundColor Yellow
Write-Host "===============================" -ForegroundColor Yellow

if ($token) {
    # Créer un entraînement test
    Write-Host "🏐 Création d'un entraînement test..." -ForegroundColor Cyan
    $entrainementData = @{
        titre = "Entraînement Test COK"
        date = (Get-Date).AddDays(1).ToString("yyyy-MM-dd")
        heureDebut = "18:00"
        heureFin = "20:00"
        lieu = "Gymnase Municipal Kelibia"
        type = "TECHNIQUE"
        intensite = 7
        description = "Entraînement de test pour l'intégration"
    } | ConvertTo-Json
    
    try {
        $createResponse = Invoke-WebRequest -Uri "$baseUrl`:$gatewayPort/planning-performance-service/api/entrainements" `
            -Method POST `
            -Headers $headers `
            -Body $entrainementData `
            -UseBasicParsing `
            -TimeoutSec 10
        
        if ($createResponse.StatusCode -eq 201) {
            Write-Host "✅ Entraînement créé avec succès" -ForegroundColor Green
            $entrainement = $createResponse.Content | ConvertFrom-Json
            Write-Host "🆔 ID Entraînement : $($entrainement.id)" -ForegroundColor Cyan
        }
    } catch {
        Write-Host "❌ Erreur création entraînement : $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== RÉSUMÉ DU TEST D'INTÉGRATION ===" -ForegroundColor Green
Write-Host ""

if ($servicesUp -eq $services.Count) {
    Write-Host "🎉 SUCCÈS COMPLET !" -ForegroundColor Green
    Write-Host "✅ Tous les microservices sont opérationnels" -ForegroundColor Green
    Write-Host "✅ L'authentification fonctionne" -ForegroundColor Green
    Write-Host "✅ Les APIs métier sont accessibles" -ForegroundColor Green
    Write-Host "✅ La création de données fonctionne" -ForegroundColor Green
} elseif ($servicesUp -ge 3) {
    Write-Host "🎯 SUCCÈS PARTIEL" -ForegroundColor Yellow
    Write-Host "✅ Les services principaux fonctionnent" -ForegroundColor Green
    Write-Host "⚠️  Quelques services à démarrer" -ForegroundColor Yellow
} else {
    Write-Host "⚠️  INTÉGRATION INCOMPLÈTE" -ForegroundColor Yellow
    Write-Host "❌ Plusieurs services ne sont pas démarrés" -ForegroundColor Red
}

Write-Host ""
Write-Host "URLS DE TEST :" -ForegroundColor Cyan
Write-Host "🌐 Frontend : $baseUrl`:$frontendPort" -ForegroundColor White
Write-Host "🚪 Gateway : $baseUrl`:$gatewayPort" -ForegroundColor White
Write-Host "🔍 Discovery : $baseUrl`:$discoveryPort" -ForegroundColor White
Write-Host "🔐 Auth Service : $baseUrl`:$authPort" -ForegroundColor White
Write-Host "📋 Planning Service : $baseUrl`:$planningPort" -ForegroundColor White
Write-Host ""

Write-Host "🏐 Club Olympique de Kelibia - Test d'intégration terminé !" -ForegroundColor Yellow

Read-Host "Appuyez sur Entrée pour continuer"
