package com.sprintbot.communication.repository;

import com.sprintbot.communication.entity.StatutPresence;
import com.sprintbot.communication.entity.UserPresence;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository pour l'entité UserPresence
 */
@Repository
public interface UserPresenceRepository extends JpaRepository<UserPresence, Long> {

    /**
     * Trouve la présence d'un utilisateur
     */
    Optional<UserPresence> findByUtilisateurId(Long utilisateurId);

    /**
     * Trouve les utilisateurs par statut de présence
     */
    List<UserPresence> findByStatutAndInvisibleFalseOrderByDerniereActiviteDesc(StatutPresence statut);

    /**
     * Trouve les utilisateurs en ligne
     */
    List<UserPresence> findByStatutInAndInvisibleFalseOrderByDerniereActiviteDesc(
            List<StatutPresence> statuts);

    /**
     * Trouve les utilisateurs par session ID
     */
    Optional<UserPresence> findBySessionId(String sessionId);

    /**
     * Trouve les utilisateurs actifs récemment
     */
    @Query("SELECT u FROM UserPresence u " +
           "WHERE u.derniereActivite > :depuis " +
           "AND u.invisible = false " +
           "ORDER BY u.derniereActivite DESC")
    List<UserPresence> findUtilisateursActifsRecemment(@Param("depuis") LocalDateTime depuis);

    /**
     * Compte les utilisateurs en ligne
     */
    @Query("SELECT COUNT(u) FROM UserPresence u " +
           "WHERE u.statut = 'EN_LIGNE' " +
           "AND u.invisible = false")
    Long countUtilisateursEnLigne();

    /**
     * Trouve les utilisateurs par plateforme
     */
    List<UserPresence> findByPlateformeAndStatutOrderByDerniereActiviteDesc(
            String plateforme, StatutPresence statut);

    /**
     * Trouve les utilisateurs mobiles en ligne
     */
    List<UserPresence> findByEstMobileTrueAndStatutOrderByDerniereActiviteDesc(StatutPresence statut);

    /**
     * Statistiques de présence par statut
     */
    @Query("SELECT u.statut, COUNT(u) FROM UserPresence u " +
           "WHERE u.invisible = false " +
           "GROUP BY u.statut")
    List<Object[]> getStatistiquesParStatut();

    /**
     * Statistiques par plateforme
     */
    @Query("SELECT u.plateforme, COUNT(u) FROM UserPresence u " +
           "WHERE u.statut = 'EN_LIGNE' " +
           "AND u.invisible = false " +
           "GROUP BY u.plateforme")
    List<Object[]> getStatistiquesParPlateforme();

    /**
     * Nettoie les sessions expirées
     */
    @Query("UPDATE UserPresence u " +
           "SET u.statut = 'HORS_LIGNE', u.sessionId = null " +
           "WHERE u.derniereActivite < :dateExpiration " +
           "AND u.statut != 'HORS_LIGNE'")
    int nettoyerSessionsExpirees(@Param("dateExpiration") LocalDateTime dateExpiration);

    /**
     * Trouve les utilisateurs inactifs
     */
    @Query("SELECT u FROM UserPresence u " +
           "WHERE u.derniereActivite < :dateInactivite " +
           "AND u.statut != 'HORS_LIGNE'")
    List<UserPresence> findUtilisateursInactifs(@Param("dateInactivite") LocalDateTime dateInactivite);

    /**
     * Met à jour l'activité d'un utilisateur
     */
    @Query("UPDATE UserPresence u " +
           "SET u.derniereActivite = :maintenant " +
           "WHERE u.utilisateurId = :utilisateurId")
    void mettreAJourActivite(
            @Param("utilisateurId") Long utilisateurId,
            @Param("maintenant") LocalDateTime maintenant);

    /**
     * Trouve les utilisateurs avec message de statut
     */
    List<UserPresence> findByMessageStatutIsNotNullAndInvisibleFalseOrderByDerniereActiviteDesc();
}
