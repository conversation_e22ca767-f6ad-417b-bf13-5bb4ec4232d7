{"info": {"name": "Finance Service - Tests d'intégration", "description": "Collection de tests pour le microservice Finance de SprintBot", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8085", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}, {"key": "budgetId", "value": "", "type": "string"}, {"key": "transactionId", "value": "", "type": "string"}, {"key": "sponsorId", "value": "", "type": "string"}], "item": [{"name": "Health Check", "item": [{"name": "Service Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/actuator/health", "host": ["{{baseUrl}}"], "path": ["actuator", "health"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Service is healthy', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('UP');", "});"]}}]}]}, {"name": "Budget Management", "item": [{"name": "Create Budget", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"nom\": \"Budget Test Postman\",\n  \"description\": \"Budget créé via tests Postman\",\n  \"montantTotal\": 15000.00,\n  \"periode\": \"ANNUEL\",\n  \"dateDebut\": \"2024-01-01\",\n  \"dateFin\": \"2024-12-31\",\n  \"statut\": \"ACTIF\",\n  \"seuilAlerte\": 80.0,\n  \"responsableId\": 1\n}"}, "url": {"raw": "{{baseUrl}}/api/budgets", "host": ["{{baseUrl}}"], "path": ["api", "budgets"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Budget created successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.id).to.exist;", "    pm.globals.set('budgetId', response.id);", "});"]}}]}, {"name": "Get All Budgets", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/budgets", "host": ["{{baseUrl}}"], "path": ["api", "budgets"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Get budgets successful', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.content).to.be.an('array');", "});"]}}]}, {"name": "Get Budget by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/budgets/{{budgetId}}", "host": ["{{baseUrl}}"], "path": ["api", "budgets", "{{budgetId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Get budget by ID successful', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.id).to.exist;", "    pm.expect(response.nom).to.eql('Budget Test Postman');", "});"]}}]}, {"name": "Update Budget", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"nom\": \"Budget Test Modifié\",\n  \"description\": \"Budget modifié via Postman\",\n  \"seuilAlerte\": 85.0\n}"}, "url": {"raw": "{{baseUrl}}/api/budgets/{{budgetId}}", "host": ["{{baseUrl}}"], "path": ["api", "budgets", "{{budgetId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Budget updated successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.nom).to.eql('Budget Test Modifié');", "});"]}}]}]}, {"name": "Transaction Management", "item": [{"name": "Create Transaction", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reference\": \"TXN-POSTMAN-001\",\n  \"description\": \"Transaction de test Postman\",\n  \"montant\": 500.00,\n  \"typeTransaction\": \"DEPENSE\",\n  \"statut\": \"EN_ATTENTE\",\n  \"modePaiement\": \"VIREMENT\",\n  \"categorieTransactionId\": 1,\n  \"utilisateurId\": 1\n}"}, "url": {"raw": "{{baseUrl}}/api/transactions", "host": ["{{baseUrl}}"], "path": ["api", "transactions"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Transaction created successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.id).to.exist;", "    pm.globals.set('transactionId', response.id);", "});"]}}]}, {"name": "Validate Transaction", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"validation\": true,\n  \"commentaire\": \"Transaction validée via test Postman\"\n}"}, "url": {"raw": "{{baseUrl}}/api/transactions/{{transactionId}}/validation", "host": ["{{baseUrl}}"], "path": ["api", "transactions", "{{transactionId}}", "validation"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Transaction validated successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.statut).to.eql('VALIDEE');", "});"]}}]}]}, {"name": "Sponsor Management", "item": [{"name": "Create Sponsor", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"nom\": \"Sponsor Test Postman\",\n  \"typePartenaire\": \"SPONSOR_PRINCIPAL\",\n  \"secteurActivite\": \"TECHNOLOGIE\",\n  \"montantContrat\": 10000.00,\n  \"dateDebutContrat\": \"2024-01-01\",\n  \"dateFinContrat\": \"2024-12-31\",\n  \"statut\": \"ACTIF\",\n  \"contactPrincipal\": \"<EMAIL>\",\n  \"telephone\": \"+33123456789\",\n  \"adresse\": \"123 Rue Test, 75001 Paris\"\n}"}, "url": {"raw": "{{baseUrl}}/api/sponsors", "host": ["{{baseUrl}}"], "path": ["api", "sponsors"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Sponsor created successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.id).to.exist;", "    pm.globals.set('sponsorId', response.id);", "});"]}}]}, {"name": "Renew Sponsor Contract", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"nouvelleEcheance\": \"2025-12-31\",\n  \"nouveauMontant\": 12000.00\n}"}, "url": {"raw": "{{baseUrl}}/api/sponsors/{{sponsorId}}/renouveler", "host": ["{{baseUrl}}"], "path": ["api", "sponsors", "{{sponsorId}}", "renouveler"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Sponsor contract renewed successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.montantContrat).to.eql(12000.00);", "});"]}}]}]}, {"name": "Reports and Statistics", "item": [{"name": "Dashboard Data", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/rapports/dashboard", "host": ["{{baseUrl}}"], "path": ["api", "rapports", "dashboard"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Dashboard data retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.indicateurs).to.exist;", "    pm.expect(response.graphiques).to.exist;", "});"]}}]}, {"name": "Global Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/rapports/stats-globales", "host": ["{{baseUrl}}"], "path": ["api", "rapports", "stats-globales"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Global statistics retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.totalBudgets).to.exist;", "    pm.expect(response.totalTransactions).to.exist;", "});"]}}]}]}, {"name": "Erro<PERSON>", "item": [{"name": "Get Non-existent Budget", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/budgets/99999", "host": ["{{baseUrl}}"], "path": ["api", "budgets", "99999"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Returns 404 for non-existent budget', function () {", "    pm.response.to.have.status(404);", "});"]}}]}, {"name": "Create Invalid Budget", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"nom\": \"\",\n  \"montantTotal\": -100\n}"}, "url": {"raw": "{{baseUrl}}/api/budgets", "host": ["{{baseUrl}}"], "path": ["api", "budgets"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Returns 400 for invalid budget data', function () {", "    pm.response.to.have.status(400);", "});"]}}]}]}, {"name": "Cleanup", "item": [{"name": "Delete Test Budget", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/budgets/{{budgetId}}", "host": ["{{baseUrl}}"], "path": ["api", "budgets", "{{budgetId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Budget deleted successfully', function () {", "    pm.response.to.have.status(204);", "});"]}}]}]}]}