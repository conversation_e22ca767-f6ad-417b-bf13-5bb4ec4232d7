package com.sprintbot.communication.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * Entité représentant la participation d'un utilisateur à une conversation
 */
@Entity
@Table(name = "participants_conversation", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"conversation_id", "utilisateur_id"}))
@EntityListeners(AuditingEntityListener.class)
public class ParticipantConversation {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "L'ID de l'utilisateur est obligatoire")
    @Column(name = "utilisateur_id", nullable = false)
    private Long utilisateurId;

    @NotNull(message = "Le rôle est obligatoire")
    @Enumerated(EnumType.STRING)
    @Column(name = "role", nullable = false, length = 20)
    private RoleParticipant role = RoleParticipant.MEMBRE;

    @Column(name = "date_derniere_lecture")
    private LocalDateTime dateDerniereLecture;

    @Column(name = "notifications_activees")
    private Boolean notificationsActivees = true;

    @Column(name = "est_epinglee")
    private Boolean estEpinglee = false;

    @Column(name = "est_silencieux")
    private Boolean estSilencieux = false;

    @Column(name = "couleur_personnalisee")
    private String couleurPersonnalisee;

    @Column(name = "surnom_conversation")
    private String surnomConversation;

    @CreatedDate
    @Column(name = "date_ajout", nullable = false)
    private LocalDateTime dateAjout;

    @LastModifiedDate
    @Column(name = "date_modification")
    private LocalDateTime dateModification;

    // Relations
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "conversation_id", nullable = false)
    private Conversation conversation;

    // Constructeurs
    public ParticipantConversation() {}

    public ParticipantConversation(Conversation conversation, Long utilisateurId, RoleParticipant role) {
        this.conversation = conversation;
        this.utilisateurId = utilisateurId;
        this.role = role;
        this.dateDerniereLecture = LocalDateTime.now();
    }

    // Méthodes métier
    public void marquerCommeLu() {
        this.dateDerniereLecture = LocalDateTime.now();
    }

    public void activerNotifications() {
        this.notificationsActivees = true;
    }

    public void desactiverNotifications() {
        this.notificationsActivees = false;
    }

    public void epingler() {
        this.estEpinglee = true;
    }

    public void desepingler() {
        this.estEpinglee = false;
    }

    public void mettreEnSilencieux() {
        this.estSilencieux = true;
    }

    public void enleverSilencieux() {
        this.estSilencieux = false;
    }

    public boolean estAdmin() {
        return this.role == RoleParticipant.ADMIN;
    }

    public boolean estModerateur() {
        return this.role == RoleParticipant.MODERATEUR;
    }

    public boolean peutModererConversation() {
        return this.role == RoleParticipant.ADMIN || this.role == RoleParticipant.MODERATEUR;
    }

    // Getters et Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUtilisateurId() {
        return utilisateurId;
    }

    public void setUtilisateurId(Long utilisateurId) {
        this.utilisateurId = utilisateurId;
    }

    public RoleParticipant getRole() {
        return role;
    }

    public void setRole(RoleParticipant role) {
        this.role = role;
    }

    public LocalDateTime getDateDerniereLecture() {
        return dateDerniereLecture;
    }

    public void setDateDerniereLecture(LocalDateTime dateDerniereLecture) {
        this.dateDerniereLecture = dateDerniereLecture;
    }

    public Boolean getNotificationsActivees() {
        return notificationsActivees;
    }

    public void setNotificationsActivees(Boolean notificationsActivees) {
        this.notificationsActivees = notificationsActivees;
    }

    public Boolean getEstEpinglee() {
        return estEpinglee;
    }

    public void setEstEpinglee(Boolean estEpinglee) {
        this.estEpinglee = estEpinglee;
    }

    public Boolean getEstSilencieux() {
        return estSilencieux;
    }

    public void setEstSilencieux(Boolean estSilencieux) {
        this.estSilencieux = estSilencieux;
    }

    public String getCouleurPersonnalisee() {
        return couleurPersonnalisee;
    }

    public void setCouleurPersonnalisee(String couleurPersonnalisee) {
        this.couleurPersonnalisee = couleurPersonnalisee;
    }

    public String getSurnomConversation() {
        return surnomConversation;
    }

    public void setSurnomConversation(String surnomConversation) {
        this.surnomConversation = surnomConversation;
    }

    public LocalDateTime getDateAjout() {
        return dateAjout;
    }

    public void setDateAjout(LocalDateTime dateAjout) {
        this.dateAjout = dateAjout;
    }

    public LocalDateTime getDateModification() {
        return dateModification;
    }

    public void setDateModification(LocalDateTime dateModification) {
        this.dateModification = dateModification;
    }

    public Conversation getConversation() {
        return conversation;
    }

    public void setConversation(Conversation conversation) {
        this.conversation = conversation;
    }

    @Override
    public String toString() {
        return "ParticipantConversation{" +
                "id=" + id +
                ", utilisateurId=" + utilisateurId +
                ", role=" + role +
                ", notificationsActivees=" + notificationsActivees +
                ", estEpinglee=" + estEpinglee +
                '}';
    }
}
