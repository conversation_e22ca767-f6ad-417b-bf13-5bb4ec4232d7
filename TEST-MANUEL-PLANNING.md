# 🏐 Test Manuel Docker - Microservice Planning Performance

## Club Olympique de Kelibia - Guide Étape par Étape

### 📋 ÉTAPES À SUIVRE MANUELLEMENT

---

## ÉTAPE 1 : Préparation

### 1.1 Ouvrir PowerShell en tant qu'Administrateur
```powershell
# Clic droit sur PowerShell -> "Exécuter en tant qu'administrateur"
```

### 1.2 Naviguer vers le répertoire du microservice
```powershell
cd "C:\Users\<USER>\Desktop\plateforme intelligente pour gérer une équipe de volley-ball\microservices\planning-performance-service"
```

### 1.3 Vérifier Docker
```powershell
docker --version
docker ps
```

---

## ÉTAPE 2 : Démarrer la Base de Données PostgreSQL

### 2.1 Nettoyer les conteneurs existants (si nécessaire)
```powershell
docker stop planning-performance-db 2>$null
docker rm planning-performance-db 2>$null
```

### 2.2 D<PERSON>marrer PostgreSQL
```powershell
docker run -d --name planning-performance-db `
  -e POSTGRES_DB=planning_performance_db `
  -e POSTGRES_USER=planning_user `
  -e POSTGRES_PASSWORD=planning_password `
  -p 5434:5432 `
  postgres:15-alpine
```

### 2.3 Attendre le démarrage (15-20 secondes)
```powershell
Start-Sleep -Seconds 20
```

### 2.4 Vérifier la base de données
```powershell
docker exec planning-performance-db pg_isready -U planning_user -d planning_performance_db
```

**Résultat attendu :** `planning_performance_db:5432 - accepting connections`

---

## ÉTAPE 3 : Construire et Démarrer le Backend

### 3.1 Naviguer vers le répertoire backend
```powershell
cd backend
```

### 3.2 Construire l'image Docker du backend
```powershell
docker build -t planning-performance-backend .
```

### 3.3 Démarrer le conteneur backend
```powershell
docker run -d --name planning-performance-backend `
  -p 8082:8082 `
  -e SPRING_PROFILES_ACTIVE=dev `
  -e SPRING_DATASOURCE_URL="*******************************************************************" `
  -e SPRING_DATASOURCE_USERNAME=planning_user `
  -e SPRING_DATASOURCE_PASSWORD=planning_password `
  planning-performance-backend
```

### 3.4 Vérifier les logs du backend
```powershell
docker logs planning-performance-backend
```

### 3.5 Attendre le démarrage (30 secondes)
```powershell
Start-Sleep -Seconds 30
```

---

## ÉTAPE 4 : Construire et Démarrer le Frontend

### 4.1 Naviguer vers le répertoire frontend
```powershell
cd ../frontend
```

### 4.2 Construire l'image Docker du frontend
```powershell
docker build -t planning-performance-frontend .
```

### 4.3 Démarrer le conteneur frontend
```powershell
docker run -d --name planning-performance-frontend `
  -p 4202:80 `
  planning-performance-frontend
```

### 4.4 Vérifier les logs du frontend
```powershell
docker logs planning-performance-frontend
```

---

## ÉTAPE 5 : Tests de Vérification

### 5.1 Vérifier l'état des conteneurs
```powershell
docker ps
```

**Résultat attendu :** 3 conteneurs en cours d'exécution :
- planning-performance-db (port 5434)
- planning-performance-backend (port 8082)
- planning-performance-frontend (port 4202)

### 5.2 Test du Backend - Health Check
```powershell
curl http://localhost:8082/actuator/health
```

**Résultat attendu :**
```json
{
  "status": "UP",
  "components": {
    "db": {"status": "UP"},
    "diskSpace": {"status": "UP"}
  }
}
```

### 5.3 Test du Frontend
```powershell
curl http://localhost:4202
```

**Résultat attendu :** Code HTML de l'application Angular

### 5.4 Test des APIs
```powershell
# API Entraînements
curl http://localhost:8082/api/entrainements

# API Performances
curl http://localhost:8082/api/performances

# API Statistiques
curl http://localhost:8082/api/statistiques
```

**Résultat attendu :** Réponses JSON (peuvent être des listes vides)

---

## ÉTAPE 6 : Test de Création d'Entraînement

### 6.1 Créer un entraînement de test
```powershell
$body = @{
    titre = "Entraînement Test"
    description = "Test du microservice Planning Performance"
    date = "2024-08-06"
    heureDebut = "18:00"
    heureFin = "20:00"
    lieu = "Gymnase Test"
    type = "TECHNIQUE"
    intensite = 7
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:8082/api/entrainements" `
  -Method POST `
  -Body $body `
  -ContentType "application/json"
```

**Résultat attendu :** Objet JSON de l'entraînement créé avec un ID

### 6.2 Vérifier la création
```powershell
curl http://localhost:8082/api/entrainements
```

**Résultat attendu :** Liste contenant l'entraînement créé

---

## ÉTAPE 7 : Test de l'Interface Web

### 7.1 Ouvrir le navigateur
```powershell
Start-Process "http://localhost:4202"
```

### 7.2 Vérifications dans le navigateur
- ✅ L'interface Angular se charge
- ✅ Les menus et composants sont visibles
- ✅ Pas d'erreurs dans la console (F12)

### 7.3 Ouvrir l'interface backend (optionnel)
```powershell
Start-Process "http://localhost:8082/actuator/health"
```

---

## ÉTAPE 8 : Nettoyage (Optionnel)

### 8.1 Arrêter les conteneurs
```powershell
docker stop planning-performance-frontend planning-performance-backend planning-performance-db
```

### 8.2 Supprimer les conteneurs
```powershell
docker rm planning-performance-frontend planning-performance-backend planning-performance-db
```

### 8.3 Supprimer les images (optionnel)
```powershell
docker rmi planning-performance-frontend planning-performance-backend postgres:15-alpine
```

---

## 🔧 DÉPANNAGE

### Problème : Conteneur ne démarre pas
```powershell
# Voir les logs détaillés
docker logs [nom-conteneur]

# Vérifier les ports utilisés
netstat -an | findstr "8082\|4202\|5434"
```

### Problème : Erreur de connexion base de données
```powershell
# Vérifier la connexion à PostgreSQL
docker exec planning-performance-db psql -U planning_user -d planning_performance_db -c "SELECT 1;"
```

### Problème : API ne répond pas
```powershell
# Vérifier les logs du backend
docker logs planning-performance-backend

# Tester la connectivité
telnet localhost 8082
```

---

## ✅ RÉSULTATS ATTENDUS

### Services Opérationnels
- ✅ Base de données PostgreSQL sur port 5434
- ✅ Backend Spring Boot sur port 8082
- ✅ Frontend Angular sur port 4202

### APIs Fonctionnelles
- ✅ Health check : `{"status":"UP"}`
- ✅ GET /api/entrainements : Liste JSON
- ✅ POST /api/entrainements : Création réussie
- ✅ GET /api/performances : Liste JSON

### Interface Web
- ✅ Frontend accessible sur http://localhost:4202
- ✅ Interface Angular chargée correctement
- ✅ Pas d'erreurs JavaScript

---

## 🎯 PROCHAINES ÉTAPES

Après avoir validé le microservice Planning Performance :

1. **Intégrer avec auth-user-service** via le Gateway
2. **Tester la communication inter-services**
3. **Valider l'interface intégrée** dans le frontend principal
4. **Effectuer des tests de charge** si nécessaire

---

**🚀 Suivez ces étapes dans l'ordre pour tester complètement le microservice Planning Performance !**
