Write-Host "=== DÉMARRAGE DE TOUS LES MICROSERVICES ===" -ForegroundColor Green
Write-Host "Club Olympique de Kelibia" -ForegroundColor Cyan
Write-Host ""

Write-Host "🚀 Démarrage de l'écosystème complet des microservices..." -ForegroundColor Yellow
Write-Host ""

# Vérifier Docker
Write-Host "1. VÉRIFICATION DOCKER" -ForegroundColor Yellow
Write-Host "=======================" -ForegroundColor Yellow

try {
    $dockerVersion = docker --version 2>&1
    if ($dockerVersion) {
        Write-Host "✅ Docker disponible : $dockerVersion" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Docker non disponible" -ForegroundColor Red
    Write-Host "Veuillez installer Docker Desktop et réessayer." -ForegroundColor Yellow
    Read-Host "Appuyez sur Entrée pour continuer"
    exit 1
}

# Nettoyer les conteneurs existants
Write-Host ""
Write-Host "2. NETTOYAGE DES CONTENEURS EXISTANTS" -ForegroundColor Yellow
Write-Host "======================================" -ForegroundColor Yellow

Write-Host "🧹 Arrêt des conteneurs existants..." -ForegroundColor Cyan
try {
    docker-compose -f docker-compose.integration.yml down 2>&1 | Out-Null
    Write-Host "✅ Conteneurs arrêtés" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Aucun conteneur à arrêter" -ForegroundColor Yellow
}

# Nettoyer les volumes si nécessaire
Write-Host "🗑️  Nettoyage des volumes (optionnel)..." -ForegroundColor Cyan
try {
    docker volume prune -f 2>&1 | Out-Null
    Write-Host "✅ Volumes nettoyés" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Nettoyage des volumes ignoré" -ForegroundColor Yellow
}

# Démarrer les services
Write-Host ""
Write-Host "3. DÉMARRAGE DES SERVICES" -ForegroundColor Yellow
Write-Host "=========================" -ForegroundColor Yellow

Write-Host "🔄 Démarrage avec Docker Compose..." -ForegroundColor Cyan
Write-Host "Configuration : docker-compose.integration.yml" -ForegroundColor White
Write-Host ""

try {
    # Démarrer en mode détaché
    Write-Host "📦 Création et démarrage des conteneurs..." -ForegroundColor Cyan
    $composeResult = docker-compose -f docker-compose.integration.yml up -d --build 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Services démarrés avec succès" -ForegroundColor Green
    } else {
        Write-Host "❌ Erreur lors du démarrage" -ForegroundColor Red
        Write-Host $composeResult
        Write-Host ""
        Write-Host "SOLUTION ALTERNATIVE :" -ForegroundColor Yellow
        Write-Host "Démarrez les services individuellement avec les IDEs" -ForegroundColor White
        Read-Host "Appuyez sur Entrée pour continuer"
        exit 1
    }
} catch {
    Write-Host "❌ Erreur Docker Compose : $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "SOLUTION ALTERNATIVE :" -ForegroundColor Yellow
    Write-Host "Démarrez les services individuellement :" -ForegroundColor White
    Write-Host "1. Discovery Service (port 8761)" -ForegroundColor Gray
    Write-Host "2. Gateway Service (port 8080)" -ForegroundColor Gray
    Write-Host "3. Auth User Service (port 8081)" -ForegroundColor Gray
    Write-Host "4. Planning Performance Service (port 8082)" -ForegroundColor Gray
    Write-Host "5. Frontend Angular (port 4201)" -ForegroundColor Gray
    Read-Host "Appuyez sur Entrée pour continuer"
    exit 1
}

# Attendre que les services démarrent
Write-Host ""
Write-Host "4. ATTENTE DU DÉMARRAGE" -ForegroundColor Yellow
Write-Host "=======================" -ForegroundColor Yellow

Write-Host "⏳ Attente du démarrage des services (60 secondes)..." -ForegroundColor Cyan
Start-Sleep -Seconds 60

# Vérifier l'état des conteneurs
Write-Host ""
Write-Host "5. VÉRIFICATION DES CONTENEURS" -ForegroundColor Yellow
Write-Host "===============================" -ForegroundColor Yellow

try {
    $containers = docker-compose -f docker-compose.integration.yml ps 2>&1
    Write-Host "État des conteneurs :" -ForegroundColor Cyan
    Write-Host $containers -ForegroundColor White
} catch {
    Write-Host "❌ Impossible de vérifier l'état des conteneurs" -ForegroundColor Red
}

# Fonction pour tester un service
function Test-ServiceHealth {
    param(
        [string]$ServiceName,
        [string]$Url,
        [int]$MaxRetries = 5
    )
    
    for ($i = 1; $i -le $MaxRetries; $i++) {
        try {
            $response = Invoke-WebRequest -Uri $Url -UseBasicParsing -TimeoutSec 5
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ $ServiceName opérationnel" -ForegroundColor Green
                return $true
            }
        } catch {
            if ($i -eq $MaxRetries) {
                Write-Host "❌ $ServiceName non accessible après $MaxRetries tentatives" -ForegroundColor Red
                return $false
            }
            Write-Host "⏳ $ServiceName - Tentative $i/$MaxRetries..." -ForegroundColor Yellow
            Start-Sleep -Seconds 10
        }
    }
    return $false
}

# Tester les services
Write-Host ""
Write-Host "6. TEST DES SERVICES" -ForegroundColor Yellow
Write-Host "====================" -ForegroundColor Yellow

$services = @(
    @{ Name = "Discovery Service"; Url = "http://localhost:8761/actuator/health" },
    @{ Name = "Gateway Service"; Url = "http://localhost:8080/actuator/health" },
    @{ Name = "Auth User Service"; Url = "http://localhost:8081/actuator/health" },
    @{ Name = "Planning Performance Service"; Url = "http://localhost:8082/actuator/health" }
)

$healthyServices = 0
foreach ($service in $services) {
    if (Test-ServiceHealth -ServiceName $service.Name -Url $service.Url) {
        $healthyServices++
    }
}

Write-Host ""
Write-Host "RÉSULTAT : $healthyServices/$($services.Count) services opérationnels" -ForegroundColor $(if ($healthyServices -eq $services.Count) { "Green" } else { "Yellow" })

# Afficher les URLs
Write-Host ""
Write-Host "7. URLS DISPONIBLES" -ForegroundColor Yellow
Write-Host "===================" -ForegroundColor Yellow

Write-Host "🌐 INTERFACES UTILISATEUR :" -ForegroundColor Cyan
Write-Host "Frontend Principal : http://localhost:4201" -ForegroundColor White
Write-Host "Discovery Console  : http://localhost:8761" -ForegroundColor White
Write-Host ""

Write-Host "🔧 APIS ET SERVICES :" -ForegroundColor Cyan
Write-Host "Gateway API        : http://localhost:8080" -ForegroundColor White
Write-Host "Auth User API      : http://localhost:8081" -ForegroundColor White
Write-Host "Planning Perf API  : http://localhost:8082" -ForegroundColor White
Write-Host ""

Write-Host "📊 HEALTH CHECKS :" -ForegroundColor Cyan
Write-Host "Gateway Health     : http://localhost:8080/actuator/health" -ForegroundColor White
Write-Host "Auth Health        : http://localhost:8081/actuator/health" -ForegroundColor White
Write-Host "Planning Health    : http://localhost:8082/actuator/health" -ForegroundColor White
Write-Host ""

Write-Host "🗄️  BASES DE DONNÉES :" -ForegroundColor Cyan
Write-Host "Auth DB (PostgreSQL)     : localhost:5433" -ForegroundColor White
Write-Host "Planning DB (PostgreSQL) : localhost:5434" -ForegroundColor White
Write-Host ""

if ($healthyServices -eq $services.Count) {
    Write-Host "🎉 TOUS LES SERVICES SONT OPÉRATIONNELS !" -ForegroundColor Green
    Write-Host ""
    Write-Host "Vous pouvez maintenant :" -ForegroundColor Cyan
    Write-Host "1. Accéder au frontend : http://localhost:4201" -ForegroundColor White
    Write-Host "2. Lancer le test d'intégration : .\test-integration-complete.ps1" -ForegroundColor White
    Write-Host "3. Tester les APIs avec Postman ou curl" -ForegroundColor White
} else {
    Write-Host "⚠️  CERTAINS SERVICES NE SONT PAS ENCORE PRÊTS" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Attendez quelques minutes et relancez le test d'intégration." -ForegroundColor Cyan
}

Write-Host ""
Write-Host "🏐 Club Olympique de Kelibia - Écosystème microservices démarré !" -ForegroundColor Yellow

Read-Host "Appuyez sur Entrée pour continuer"
