package com.sprintbot.communication.repository;

import com.sprintbot.communication.entity.LectureMessage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository pour l'entité LectureMessage
 */
@Repository
public interface LectureMessageRepository extends JpaRepository<LectureMessage, Long> {

    /**
     * Trouve les lectures d'un message
     */
    List<LectureMessage> findByMessageIdOrderByDateLectureAsc(Long messageId);

    /**
     * Trouve une lecture spécifique
     */
    Optional<LectureMessage> findByMessageIdAndUtilisateurId(Long messageId, Long utilisateurId);

    /**
     * Vérifie si un utilisateur a lu un message
     */
    boolean existsByMessageIdAndUtilisateurId(Long messageId, Long utilisateurId);

    /**
     * Compte les lectures d'un message
     */
    Long countByMessageId(Long messageId);

    /**
     * Trouve les utilisateurs qui ont lu un message
     */
    @Query("SELECT l.utilisateurId FROM LectureMessage l " +
           "WHERE l.message.id = :messageId " +
           "ORDER BY l.dateLecture ASC")
    List<Long> findUtilisateursAyantLuMessage(@Param("messageId") Long messageId);

    /**
     * Trouve les lectures d'un utilisateur
     */
    List<LectureMessage> findByUtilisateurIdOrderByDateLectureDesc(Long utilisateurId);

    /**
     * Trouve les lectures récentes d'un utilisateur
     */
    @Query("SELECT l FROM LectureMessage l " +
           "WHERE l.utilisateurId = :utilisateurId " +
           "AND l.dateLecture >= :depuis " +
           "ORDER BY l.dateLecture DESC")
    List<LectureMessage> findLecturesRecentes(
            @Param("utilisateurId") Long utilisateurId,
            @Param("depuis") LocalDateTime depuis);

    /**
     * Statistiques de lecture par message
     */
    @Query("SELECT l.message.id, COUNT(l), MIN(l.dateLecture), MAX(l.dateLecture) " +
           "FROM LectureMessage l " +
           "WHERE l.message.conversation.id = :conversationId " +
           "GROUP BY l.message.id " +
           "ORDER BY l.message.dateEnvoi DESC")
    List<Object[]> getStatistiquesLectureParMessage(@Param("conversationId") Long conversationId);

    /**
     * Taux de lecture d'une conversation
     */
    @Query("SELECT " +
           "COUNT(DISTINCT l.message.id) * 100.0 / COUNT(DISTINCT m.id) " +
           "FROM Message m " +
           "LEFT JOIN LectureMessage l ON l.message.id = m.id AND l.utilisateurId = :utilisateurId " +
           "WHERE m.conversation.id = :conversationId " +
           "AND m.expediteurId != :utilisateurId")
    Double getTauxLecture(
            @Param("conversationId") Long conversationId,
            @Param("utilisateurId") Long utilisateurId);

    /**
     * Supprime les anciennes lectures
     */
    @Query("DELETE FROM LectureMessage l " +
           "WHERE l.dateLecture < :dateLimit")
    void supprimerAnciennesLectures(@Param("dateLimit") LocalDateTime dateLimit);
}
