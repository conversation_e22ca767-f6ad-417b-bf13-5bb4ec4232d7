.budget-list-container {
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;

  .header-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;

    .title-section {
      h2 {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0 0 0.5rem 0;
        color: #1976d2;
        font-weight: 500;

        mat-icon {
          font-size: 2rem;
          width: 2rem;
          height: 2rem;
        }
      }

      .subtitle {
        margin: 0;
        color: #666;
        font-size: 0.95rem;
      }
    }

    .actions-section {
      display: flex;
      gap: 1rem;
      align-items: center;

      button {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }
    }
  }

  .filters-card {
    margin-bottom: 1.5rem;

    .filters-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      align-items: end;

      .clear-filters-btn {
        height: fit-content;
        
        mat-icon {
          margin-right: 0.5rem;
        }
      }
    }
  }

  .table-card {
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 3rem;
      gap: 1rem;

      p {
        margin: 0;
        color: #666;
      }
    }

    .table-container {
      .budget-table {
        width: 100%;
        
        .budget-name {
          strong {
            display: block;
            font-weight: 500;
            color: #333;
          }

          small {
            display: block;
            color: #666;
            font-size: 0.85rem;
            margin-top: 0.25rem;
          }
        }

        .progress-container {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          min-width: 120px;

          mat-progress-bar {
            flex: 1;
            height: 8px;
            border-radius: 4px;
          }

          .progress-text {
            font-size: 0.85rem;
            font-weight: 500;
            min-width: 40px;
            text-align: right;
          }
        }

        .actions-buttons {
          display: flex;
          gap: 0.25rem;
          align-items: center;

          .delete-action {
            color: #f44336;
          }
        }

        // Responsive table
        @media (max-width: 768px) {
          .mat-column-description,
          .mat-column-dateDebut,
          .mat-column-dateFin {
            display: none;
          }
        }

        @media (max-width: 600px) {
          .mat-column-periode,
          .mat-column-montantUtilise {
            display: none;
          }
        }
      }

      .no-data {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 4rem 2rem;
        text-align: center;

        mat-icon {
          font-size: 4rem;
          width: 4rem;
          height: 4rem;
          color: #ccc;
          margin-bottom: 1rem;
        }

        h3 {
          margin: 0 0 0.5rem 0;
          color: #666;
          font-weight: 400;
        }

        p {
          margin: 0 0 2rem 0;
          color: #999;
        }

        button {
          mat-icon {
            font-size: 1.25rem;
            width: 1.25rem;
            height: 1.25rem;
            margin-right: 0.5rem;
          }
        }
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: 1rem;

    .header-section {
      flex-direction: column;
      align-items: stretch;

      .actions-section {
        justify-content: center;
      }
    }

    .filters-card .filters-grid {
      grid-template-columns: 1fr;
    }
  }

  @media (max-width: 600px) {
    padding: 0.75rem;

    .header-section .title-section h2 {
      font-size: 1.5rem;

      mat-icon {
        font-size: 1.5rem;
        width: 1.5rem;
        height: 1.5rem;
      }
    }
  }
}

// Thème sombre
.dark-theme {
  .budget-list-container {
    .header-section .title-section h2 {
      color: #90caf9;
    }

    .budget-table .budget-name {
      strong {
        color: #fff;
      }

      small {
        color: #aaa;
      }
    }

    .no-data {
      h3 {
        color: #aaa;
      }

      p {
        color: #777;
      }

      mat-icon {
        color: #555;
      }
    }
  }
}

// Animations
.budget-table {
  tr {
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.04);
    }
  }
}

.dark-theme .budget-table tr:hover {
  background-color: rgba(255, 255, 255, 0.04);
}

// Couleurs des barres de progression
::ng-deep {
  .mat-progress-bar-fill::after {
    transition: transform 0.3s ease;
  }

  .mat-progress-bar.mat-primary .mat-progress-bar-fill::after {
    background-color: #4caf50;
  }

  .mat-progress-bar.mat-accent .mat-progress-bar-fill::after {
    background-color: #ff9800;
  }

  .mat-progress-bar.mat-warn .mat-progress-bar-fill::after {
    background-color: #f44336;
  }
}
