services:
  # ============================================================================
  # SERVICES D'INFRASTRUCTURE
  # ============================================================================

  # Discovery Service (Eureka Server)
  discovery-service:
    build:
      context: ./microservices/discovery-service/backend
      dockerfile: Dockerfile
    container_name: sprintbot-discovery-service
    hostname: discovery-service
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SERVER_PORT: 8761
      EUREKA_INSTANCE_HOSTNAME: discovery-service
      EUREKA_DASHBOARD_USERNAME: admin
      EUREKA_DASHBOARD_PASSWORD: admin123
      JAVA_OPTS: "-Xmx512m -Xms256m -XX:+UseG1GC"
    ports:
      - "8761:8761"
    networks:
      - sprintbot-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8761/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    labels:
      - "com.sprintbot.service=discovery-service"
      - "com.sprintbot.version=1.0.0"
      - "com.sprintbot.team=infrastructure"

  # Redis pour Gateway Service (Rate Limiting & Cache)
  redis:
    image: redis:7-alpine
    container_name: sprintbot-redis
    command: redis-server --appendonly yes --requirepass sprintbot123
    environment:
      REDIS_PASSWORD: sprintbot123
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - sprintbot-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Gateway Service (API Gateway)
  gateway-service:
    build:
      context: ./microservices/gateway-service/backend
      dockerfile: Dockerfile
    container_name: sprintbot-gateway-service
    hostname: gateway-service
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SERVER_PORT: 8080
      EUREKA_CLIENT_SERVICE_URL: http://discovery-service:8761/eureka/
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: sprintbot123
      JWT_SECRET_KEY: SprintBot-Gateway-Secret-Key-2024-Very-Long-And-Secure-Change-In-Production
      JWT_EXPIRATION_TIME: 86400000
      RATE_LIMIT_REQUESTS_PER_SECOND: 100
      RATE_LIMIT_BURST_CAPACITY: 200
      CORS_ALLOWED_ORIGINS: http://localhost:4200,http://localhost:4201,http://localhost:4202,http://localhost:4203,http://localhost:4204,http://localhost:4205
      JAVA_OPTS: "-Xmx1024m -Xms512m -XX:+UseG1GC"
    ports:
      - "8081:8080"
    depends_on:
      discovery-service:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - sprintbot-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 90s
    restart: unless-stopped
    labels:
      - "com.sprintbot.service=gateway-service"
      - "com.sprintbot.version=1.0.0"
      - "com.sprintbot.team=infrastructure"

  # ============================================================================
  # BASES DE DONNÉES
  # ============================================================================

  # Base de données PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: sprintbot-postgres
    environment:
      POSTGRES_DB: sprintbot_db
      POSTGRES_USER: sprintbot_user
      POSTGRES_PASSWORD: sprintbot_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - sprintbot-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U sprintbot_user -d sprintbot_db"]
      interval: 30s
      timeout: 10s
      retries: 3



  # Medical Admin Service - Base de données
  medical-admin-db:
    image: postgres:15-alpine
    container_name: medical-admin-db
    environment:
      POSTGRES_DB: medical_admin_db
      POSTGRES_USER: medical_admin_user
      POSTGRES_PASSWORD: medical_admin_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - medical_admin_db_data:/var/lib/postgresql/data
      - ./microservices/medical-admin-service/backend/src/main/resources/db/init:/docker-entrypoint-initdb.d
    ports:
      - "5435:5432"
    networks:
      - sprintbot-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U medical_admin_user -d medical_admin_db"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Medical Admin Service - Backend
  medical-admin-backend:
    build:
      context: ./microservices/medical-admin-service/backend
      dockerfile: Dockerfile
    container_name: medical-admin-backend
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ************************************************************************************
      SPRING_DATASOURCE_USERNAME: medical_admin_user
      SPRING_DATASOURCE_PASSWORD: medical_admin_password
      SPRING_JPA_HIBERNATE_DDL_AUTO: validate
      SPRING_FLYWAY_ENABLED: true
      SPRING_FLYWAY_BASELINE_ON_MIGRATE: true
      JAVA_OPTS: "-Xmx512m -Xms256m -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
    ports:
      - "8083:8083"
    depends_on:
      medical-admin-db:
        condition: service_healthy
    networks:
      - sprintbot-network
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8083/actuator/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    restart: unless-stopped

  # Medical Admin Service - Frontend
  medical-admin-frontend:
    build:
      context: ./microservices/medical-admin-service/frontend
      dockerfile: Dockerfile
    container_name: medical-admin-frontend
    environment:
      API_URL: http://medical-admin-backend:8083
    ports:
      - "4203:8080"
    depends_on:
      medical-admin-backend:
        condition: service_healthy
    networks:
      - sprintbot-network
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 60s
    restart: unless-stopped

  # Communication Service - Redis
  communication-redis:
    image: redis:7-alpine
    container_name: communication-redis
    command: redis-server --appendonly yes --requirepass sprintbot123
    volumes:
      - communication_redis_data:/data
    ports:
      - "6380:6379"
    networks:
      - sprintbot-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Communication Service - Base de données
  communication-db:
    image: postgres:15-alpine
    container_name: communication-db
    environment:
      POSTGRES_DB: communication_db
      POSTGRES_USER: communication_user
      POSTGRES_PASSWORD: communication_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - communication_db_data:/var/lib/postgresql/data
      - ./microservices/communication-service/backend/src/main/resources/db/init:/docker-entrypoint-initdb.d
    ports:
      - "5436:5432"
    networks:
      - sprintbot-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U communication_user -d communication_db"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Communication Service - Backend
  communication-backend:
    build:
      context: ./microservices/communication-service/backend
      dockerfile: Dockerfile
    container_name: communication-backend
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ************************************************************************************
      SPRING_DATASOURCE_USERNAME: communication_user
      SPRING_DATASOURCE_PASSWORD: communication_password
      SPRING_JPA_HIBERNATE_DDL_AUTO: validate
      SPRING_FLYWAY_ENABLED: true
      SPRING_FLYWAY_BASELINE_ON_MIGRATE: true
      REDIS_HOST: communication-redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: sprintbot123
      JWT_SECRET: sprintbot-communication-secret-key-2024
      JWT_EXPIRATION: 86400
      OPENAI_API_KEY: ${OPENAI_API_KEY:-}
      OPENAI_MODEL: gpt-3.5-turbo
      VAPID_PUBLIC_KEY: ${VAPID_PUBLIC_KEY:-}
      VAPID_PRIVATE_KEY: ${VAPID_PRIVATE_KEY:-}
      VAPID_SUBJECT: mailto:<EMAIL>
      SMTP_HOST: ${SMTP_HOST:-}
      SMTP_PORT: ${SMTP_PORT:-587}
      SMTP_USERNAME: ${SMTP_USERNAME:-}
      SMTP_PASSWORD: ${SMTP_PASSWORD:-}
      CORS_ALLOWED_ORIGINS: http://localhost:4200,http://localhost:4204
      JAVA_OPTS: "-Xmx512m -Xms256m -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
    ports:
      - "8084:8084"
    depends_on:
      communication-db:
        condition: service_healthy
      communication-redis:
        condition: service_healthy
    networks:
      - sprintbot-network
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8084/actuator/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    restart: unless-stopped

  # Communication Service - Frontend
  communication-frontend:
    build:
      context: ./microservices/communication-service/frontend
      dockerfile: Dockerfile
    container_name: communication-frontend
    environment:
      API_URL: http://communication-backend:8084
    ports:
      - "4204:8080"
    depends_on:
      communication-backend:
        condition: service_healthy
    networks:
      - sprintbot-network
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 60s
    restart: unless-stopped

  # Planning Performance Service - Base de données
  planning-performance-db:
    image: postgres:15-alpine
    container_name: planning-performance-db
    environment:
      POSTGRES_DB: planning_performance_db
      POSTGRES_USER: planning_performance_user
      POSTGRES_PASSWORD: planning_performance_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - planning_performance_db_data:/var/lib/postgresql/data
      - ./microservices/planning-performance-service/backend/src/main/resources/db/init:/docker-entrypoint-initdb.d
    ports:
      - "5434:5432"
    networks:
      - sprintbot-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U planning_performance_user -d planning_performance_db"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Planning Performance Service - Backend
  planning-performance-backend:
    build:
      context: ./microservices/planning-performance-service/backend
      dockerfile: Dockerfile
    container_name: planning-performance-backend
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: *********************************************************************************************************
      SPRING_DATASOURCE_USERNAME: planning_performance_user
      SPRING_DATASOURCE_PASSWORD: planning_performance_password
      SPRING_JPA_HIBERNATE_DDL_AUTO: validate
      SPRING_FLYWAY_ENABLED: true
      SPRING_FLYWAY_BASELINE_ON_MIGRATE: true
      JAVA_OPTS: "-Xmx512m -Xms256m -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
    ports:
      - "8082:8082"
    depends_on:
      planning-performance-db:
        condition: service_healthy
    networks:
      - sprintbot-network
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8082/actuator/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    restart: unless-stopped

  # Planning Performance Service - Frontend
  planning-performance-frontend:
    build:
      context: ./microservices/planning-performance-service/frontend
      dockerfile: Dockerfile
    container_name: planning-performance-frontend
    environment:
      API_URL: http://planning-performance-backend:8082
    ports:
      - "4202:8080"
    depends_on:
      planning-performance-backend:
        condition: service_healthy
    networks:
      - sprintbot-network
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 60s
    restart: unless-stopped

  # Auth User Service - Base de données
  auth-user-db:
    image: postgres:15-alpine
    container_name: auth-user-db
    environment:
      POSTGRES_DB: auth_user_db
      POSTGRES_USER: auth_user_user
      POSTGRES_PASSWORD: auth_user_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - auth_user_db_data:/var/lib/postgresql/data
      - ./microservices/auth-user-service/backend/src/main/resources/db/init:/docker-entrypoint-initdb.d
    ports:
      - "5433:5432"
    networks:
      - sprintbot-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U auth_user_user -d auth_user_db"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Auth User Service - Backend
  auth-user-backend:
    build:
      context: ./microservices/auth-user-service/backend
      dockerfile: Dockerfile
    container_name: auth-user-backend
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ************************************************
      SPRING_DATASOURCE_USERNAME: auth_user_user
      SPRING_DATASOURCE_PASSWORD: auth_user_password
      SPRING_JPA_HIBERNATE_DDL_AUTO: update
      SPRING_FLYWAY_ENABLED: true
      SPRING_FLYWAY_BASELINE_ON_MIGRATE: true
      JAVA_OPTS: "-Xmx512m -Xms256m -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
    ports:
      - "8091:8081"
    depends_on:
      auth-user-db:
        condition: service_healthy
    networks:
      - sprintbot-network
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8081/actuator/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    restart: unless-stopped

  # Auth User Service - Frontend
  auth-user-frontend:
    build:
      context: ./microservices/auth-user-service/frontend
      dockerfile: Dockerfile
    container_name: auth-user-frontend
    environment:
      API_URL: http://auth-user-backend:8081
    ports:
      - "4201:8080"
    depends_on:
      auth-user-backend:
        condition: service_healthy
    networks:
      - sprintbot-network
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 60s
    restart: unless-stopped

  # Finance Service - Base de données
  finance-db:
    image: postgres:15-alpine
    container_name: finance-db
    environment:
      POSTGRES_DB: finance_db
      POSTGRES_USER: finance_user
      POSTGRES_PASSWORD: finance_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - finance_db_data:/var/lib/postgresql/data
      - ./microservices/finance-service/backend/src/main/resources/db/init:/docker-entrypoint-initdb.d
    ports:
      - "5437:5432"
    networks:
      - sprintbot-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U finance_user -d finance_db"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Finance Service - Backend
  finance-backend:
    build:
      context: ./microservices/finance-service/backend
      dockerfile: Dockerfile
    container_name: finance-backend
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ******************************************************************
      SPRING_DATASOURCE_USERNAME: finance_user
      SPRING_DATASOURCE_PASSWORD: finance_password
      SPRING_JPA_HIBERNATE_DDL_AUTO: validate
      SPRING_FLYWAY_ENABLED: true
      SPRING_FLYWAY_BASELINE_ON_MIGRATE: true
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: sprintbot123
      JWT_SECRET: sprintbot-finance-secret-key-2024
      JWT_EXPIRATION: 86400
      SMTP_HOST: ${SMTP_HOST:-}
      SMTP_PORT: ${SMTP_PORT:-587}
      SMTP_USERNAME: ${SMTP_USERNAME:-}
      SMTP_PASSWORD: ${SMTP_PASSWORD:-}
      CORS_ALLOWED_ORIGINS: http://localhost:4200,http://localhost:4205
      JAVA_OPTS: "-Xmx512m -Xms256m -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
    ports:
      - "8085:8085"
    depends_on:
      finance-db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - sprintbot-network
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8085/actuator/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    restart: unless-stopped

  # Finance Service - Frontend
  finance-frontend:
    build:
      context: ./microservices/finance-service/frontend
      dockerfile: Dockerfile
    container_name: finance-frontend
    environment:
      API_URL: http://finance-backend:8085
    ports:
      - "4205:8080"
    depends_on:
      finance-backend:
        condition: service_healthy
    networks:
      - sprintbot-network
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 60s
    restart: unless-stopped

  # Nginx Reverse Proxy (optionnel)
  nginx:
    image: nginx:alpine
    container_name: sprintbot-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - auth-user-frontend
      - planning-performance-frontend
      - medical-admin-frontend
      - communication-frontend
      - finance-frontend
    networks:
      - sprintbot-network

volumes:
  postgres_data:
  redis_data:
  auth_user_db_data:
  planning_performance_db_data:
  medical_admin_db_data:
  communication_db_data:
  communication_redis_data:
  finance_db_data:

networks:
  sprintbot-network:
    driver: bridge
