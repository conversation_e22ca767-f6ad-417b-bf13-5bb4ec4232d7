version: '3.8'

services:
  # Base de données PostgreSQL pour Medical Admin Service
  medical-admin-db:
    image: postgres:15-alpine
    container_name: medical-admin-db
    environment:
      POSTGRES_DB: medical_admin_db
      POSTGRES_USER: medical_admin_user
      POSTGRES_PASSWORD: medical_admin_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - medical_admin_db_data:/var/lib/postgresql/data
      - ./backend/src/main/resources/db/init:/docker-entrypoint-initdb.d
    ports:
      - "5435:5432"
    networks:
      - medical-admin-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U medical_admin_user -d medical_admin_db"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Backend Spring Boot
  medical-admin-backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: medical-admin-backend
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ************************************************************************************
      SPRING_DATASOURCE_USERNAME: medical_admin_user
      SPRING_DATASOURCE_PASSWORD: medical_admin_password
      SPRING_JPA_HIBERNATE_DDL_AUTO: validate
      SPRING_FLYWAY_ENABLED: true
      SPRING_FLYWAY_BASELINE_ON_MIGRATE: true
      JAVA_OPTS: "-Xmx512m -Xms256m -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
    ports:
      - "8083:8083"
    depends_on:
      medical-admin-db:
        condition: service_healthy
    networks:
      - medical-admin-network
      - sprintbot-network
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8083/actuator/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    restart: unless-stopped
    volumes:
      - medical_admin_logs:/app/logs

  # Frontend Angular
  medical-admin-frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: medical-admin-frontend
    environment:
      API_URL: http://medical-admin-backend:8083
    ports:
      - "4203:8080"
    depends_on:
      medical-admin-backend:
        condition: service_healthy
    networks:
      - medical-admin-network
      - sprintbot-network
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 60s
    restart: unless-stopped

  # Redis pour le cache (optionnel)
  medical-admin-redis:
    image: redis:7-alpine
    container_name: medical-admin-redis
    command: redis-server --appendonly yes --requirepass medical_admin_redis_password
    environment:
      REDIS_PASSWORD: medical_admin_redis_password
    ports:
      - "6382:6379"
    volumes:
      - medical_admin_redis_data:/data
    networks:
      - medical-admin-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 3s
      retries: 3
    restart: unless-stopped

volumes:
  medical_admin_db_data:
    driver: local
  medical_admin_logs:
    driver: local
  medical_admin_redis_data:
    driver: local

networks:
  medical-admin-network:
    driver: bridge
    name: medical-admin-network
  sprintbot-network:
    external: true
    name: sprintbot-network
