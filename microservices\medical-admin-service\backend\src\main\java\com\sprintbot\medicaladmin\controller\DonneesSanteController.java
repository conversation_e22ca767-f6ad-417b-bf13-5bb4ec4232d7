package com.sprintbot.medicaladmin.controller;

import com.sprintbot.medicaladmin.entity.DonneesSante;
import com.sprintbot.medicaladmin.service.DonneesSanteService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Contrôleur REST pour la gestion des données de santé
 * Expose les APIs pour le suivi médical des joueurs
 */
@RestController
@RequestMapping("/api/donnees-sante")
@Tag(name = "Données de Santé", description = "APIs pour la gestion des données de santé des joueurs")
@CrossOrigin(origins = "*")
public class DonneesSanteController {

    private static final Logger logger = LoggerFactory.getLogger(DonneesSanteController.class);

    @Autowired
    private DonneesSanteService donneesSanteService;

    // CRUD Operations
    @PostMapping
    @Operation(summary = "Créer de nouvelles données de santé", description = "Crée un nouveau dossier de santé pour un joueur")
    public ResponseEntity<DonneesSante> creerDonneesSante(@Valid @RequestBody DonneesSante donneesSante) {
        logger.info("Création de nouvelles données de santé pour le joueur ID: {}", donneesSante.getJoueurId());
        
        try {
            DonneesSante created = donneesSanteService.creerDonneesSante(donneesSante);
            return ResponseEntity.status(HttpStatus.CREATED).body(created);
        } catch (Exception e) {
            logger.error("Erreur lors de la création des données de santé: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "Obtenir les données de santé par ID", description = "Récupère les détails d'un dossier de santé spécifique")
    public ResponseEntity<DonneesSante> obtenirDonneesSante(
            @Parameter(description = "ID des données de santé") @PathVariable Long id) {
        
        Optional<DonneesSante> donnees = donneesSanteService.obtenirDonneesSante(id);
        return donnees.map(ResponseEntity::ok)
                     .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping
    @Operation(summary = "Lister toutes les données de santé", description = "Récupère la liste paginée de toutes les données de santé")
    public ResponseEntity<Page<DonneesSante>> obtenirToutesDonneesSante(
            @Parameter(description = "Numéro de page (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Taille de la page") @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<DonneesSante> donnees = donneesSanteService.obtenirDonneesSantePaginees(pageable);
        return ResponseEntity.ok(donnees);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Mettre à jour les données de santé", description = "Met à jour un dossier de santé existant")
    public ResponseEntity<DonneesSante> mettreAJourDonneesSante(
            @Parameter(description = "ID des données de santé") @PathVariable Long id,
            @Valid @RequestBody DonneesSante donneesSante) {
        
        try {
            donneesSante.setId(id);
            DonneesSante updated = donneesSanteService.mettreAJourDonneesSante(donneesSante);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            logger.error("Erreur lors de la mise à jour des données de santé ID {}: {}", id, e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Supprimer les données de santé", description = "Supprime un dossier de santé")
    public ResponseEntity<Void> supprimerDonneesSante(
            @Parameter(description = "ID des données de santé") @PathVariable Long id) {
        
        try {
            donneesSanteService.supprimerDonneesSante(id);
            return ResponseEntity.noContent().build();
        } catch (RuntimeException e) {
            logger.error("Erreur lors de la suppression des données de santé ID {}: {}", id, e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    // Recherches par joueur
    @GetMapping("/joueur/{joueurId}")
    @Operation(summary = "Obtenir les données de santé d'un joueur", description = "Récupère l'historique médical d'un joueur")
    public ResponseEntity<List<DonneesSante>> obtenirDonneesSanteParJoueur(
            @Parameter(description = "ID du joueur") @PathVariable Long joueurId) {
        
        List<DonneesSante> donnees = donneesSanteService.obtenirDonneesSanteParJoueur(joueurId);
        return ResponseEntity.ok(donnees);
    }

    @GetMapping("/joueur/{joueurId}/visibles")
    @Operation(summary = "Obtenir les données visibles d'un joueur", description = "Récupère les données de santé visibles par le joueur")
    public ResponseEntity<List<DonneesSante>> obtenirDonneesSanteVisiblesParJoueur(
            @Parameter(description = "ID du joueur") @PathVariable Long joueurId) {
        
        List<DonneesSante> donnees = donneesSanteService.obtenirDonneesSanteVisiblesParJoueur(joueurId);
        return ResponseEntity.ok(donnees);
    }

    @GetMapping("/joueur/{joueurId}/dernieres")
    @Operation(summary = "Obtenir les dernières données d'un joueur", description = "Récupère le dernier examen médical d'un joueur")
    public ResponseEntity<DonneesSante> obtenirDernieresDonneesSante(
            @Parameter(description = "ID du joueur") @PathVariable Long joueurId) {
        
        Optional<DonneesSante> donnees = donneesSanteService.obtenirDernieresDonneesSante(joueurId);
        return donnees.map(ResponseEntity::ok)
                     .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/joueur/{joueurId}/blessures")
    @Operation(summary = "Obtenir les blessures d'un joueur", description = "Récupère l'historique des blessures d'un joueur")
    public ResponseEntity<List<DonneesSante>> obtenirBlessuresParJoueur(
            @Parameter(description = "ID du joueur") @PathVariable Long joueurId) {
        
        List<DonneesSante> blessures = donneesSanteService.obtenirBlessuresParJoueur(joueurId);
        return ResponseEntity.ok(blessures);
    }

    // Recherches par staff médical
    @GetMapping("/staff/{staffMedicalId}")
    @Operation(summary = "Obtenir les données par staff médical", description = "Récupère les données de santé gérées par un staff médical")
    public ResponseEntity<List<DonneesSante>> obtenirDonneesSanteParStaffMedical(
            @Parameter(description = "ID du staff médical") @PathVariable Long staffMedicalId) {
        
        List<DonneesSante> donnees = donneesSanteService.obtenirDonneesSanteParStaffMedical(staffMedicalId);
        return ResponseEntity.ok(donnees);
    }

    // Recherches par type et statut
    @GetMapping("/type/{typeExamen}")
    @Operation(summary = "Obtenir les données par type d'examen", description = "Récupère les données de santé par type d'examen")
    public ResponseEntity<List<DonneesSante>> obtenirDonneesSanteParType(
            @Parameter(description = "Type d'examen") @PathVariable String typeExamen) {
        
        List<DonneesSante> donnees = donneesSanteService.obtenirDonneesSanteParType(typeExamen);
        return ResponseEntity.ok(donnees);
    }

    @GetMapping("/statut/{statut}")
    @Operation(summary = "Obtenir les données par statut", description = "Récupère les données de santé par statut")
    public ResponseEntity<List<DonneesSante>> obtenirDonneesSanteParStatut(
            @Parameter(description = "Statut") @PathVariable String statut) {
        
        List<DonneesSante> donnees = donneesSanteService.obtenirDonneesSanteParStatut(statut);
        return ResponseEntity.ok(donnees);
    }

    @GetMapping("/suivi")
    @Operation(summary = "Obtenir les données nécessitant un suivi", description = "Récupère les données de santé nécessitant un suivi médical")
    public ResponseEntity<List<DonneesSante>> obtenirDonneesNecessitantSuivi() {
        List<DonneesSante> donnees = donneesSanteService.obtenirDonneesNecessitantSuivi();
        return ResponseEntity.ok(donnees);
    }

    @GetMapping("/blessures")
    @Operation(summary = "Obtenir toutes les données avec blessures", description = "Récupère toutes les données de santé contenant des blessures")
    public ResponseEntity<List<DonneesSante>> obtenirDonneesAvecBlessures() {
        List<DonneesSante> donnees = donneesSanteService.obtenirDonneesAvecBlessures();
        return ResponseEntity.ok(donnees);
    }

    @GetMapping("/graves")
    @Operation(summary = "Obtenir les données graves", description = "Récupère les données de santé avec gravité élevée")
    public ResponseEntity<List<DonneesSante>> obtenirDonneesGraves() {
        List<DonneesSante> donnees = donneesSanteService.obtenirDonneesGraves();
        return ResponseEntity.ok(donnees);
    }

    // Actions sur les données
    @PostMapping("/{id}/blessure")
    @Operation(summary = "Ajouter une blessure", description = "Ajoute une nouvelle blessure aux données de santé")
    public ResponseEntity<DonneesSante> ajouterBlessure(
            @Parameter(description = "ID des données de santé") @PathVariable Long id,
            @Parameter(description = "Description de la blessure") @RequestBody String nouvelleBlessure) {
        
        try {
            DonneesSante updated = donneesSanteService.ajouterBlessure(id, nouvelleBlessure);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            logger.error("Erreur lors de l'ajout de blessure aux données ID {}: {}", id, e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    @PutMapping("/{id}/gueri")
    @Operation(summary = "Marquer comme guéri", description = "Marque les données de santé comme guéries")
    public ResponseEntity<DonneesSante> marquerCommeGueri(
            @Parameter(description = "ID des données de santé") @PathVariable Long id) {
        
        try {
            DonneesSante updated = donneesSanteService.marquerCommeGueri(id);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            logger.error("Erreur lors du marquage comme guéri des données ID {}: {}", id, e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    @PutMapping("/{id}/traitement")
    @Operation(summary = "Commencer le traitement", description = "Met les données de santé en traitement")
    public ResponseEntity<DonneesSante> commencerTraitement(
            @Parameter(description = "ID des données de santé") @PathVariable Long id) {
        
        try {
            DonneesSante updated = donneesSanteService.commencerTraitement(id);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            logger.error("Erreur lors du début de traitement des données ID {}: {}", id, e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    @PutMapping("/{id}/suivi")
    @Operation(summary = "Mettre en suivi", description = "Met les données de santé en suivi")
    public ResponseEntity<DonneesSante> mettreEnSuivi(
            @Parameter(description = "ID des données de santé") @PathVariable Long id) {
        
        try {
            DonneesSante updated = donneesSanteService.mettreEnSuivi(id);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            logger.error("Erreur lors de la mise en suivi des données ID {}: {}", id, e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    // Recherches par période
    @GetMapping("/periode")
    @Operation(summary = "Obtenir les données par période", description = "Récupère les données de santé sur une période donnée")
    public ResponseEntity<List<DonneesSante>> obtenirDonneesSanteParPeriode(
            @Parameter(description = "Date de début") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateDebut,
            @Parameter(description = "Date de fin") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateFin) {
        
        List<DonneesSante> donnees = donneesSanteService.obtenirDonneesSanteParPeriode(dateDebut, dateFin);
        return ResponseEntity.ok(donnees);
    }

    @GetMapping("/recentes")
    @Operation(summary = "Obtenir les données récentes", description = "Récupère les données de santé récentes")
    public ResponseEntity<List<DonneesSante>> obtenirDonneesRecentes(
            @Parameter(description = "Nombre de jours") @RequestParam(defaultValue = "30") int nombreJours) {
        
        List<DonneesSante> donnees = donneesSanteService.obtenirDonneesRecentes(nombreJours);
        return ResponseEntity.ok(donnees);
    }

    // Alertes
    @GetMapping("/alertes/guerisons-echues")
    @Operation(summary = "Obtenir les guérisons échues", description = "Récupère les données avec guérisons prévues échues")
    public ResponseEntity<List<DonneesSante>> obtenirGuerisonsPrevuesEchues() {
        List<DonneesSante> donnees = donneesSanteService.obtenirGuerisonsPrevuesEchues();
        return ResponseEntity.ok(donnees);
    }

    @GetMapping("/alertes/guerisons-prochaines")
    @Operation(summary = "Obtenir les guérisons prochaines", description = "Récupère les données avec guérisons prévues prochaines")
    public ResponseEntity<List<DonneesSante>> obtenirGuerisonsPrevuesProchaines(
            @Parameter(description = "Nombre de jours") @RequestParam(defaultValue = "7") int nombreJours) {
        
        List<DonneesSante> donnees = donneesSanteService.obtenirGuerisonsPrevuesProchaines(nombreJours);
        return ResponseEntity.ok(donnees);
    }

    // Statistiques
    @GetMapping("/statistiques/joueur/{joueurId}/count")
    @Operation(summary = "Compter les données par joueur", description = "Compte le nombre de données de santé d'un joueur")
    public ResponseEntity<Long> compterDonneesSanteParJoueur(
            @Parameter(description = "ID du joueur") @PathVariable Long joueurId) {
        
        long count = donneesSanteService.compterDonneesSanteParJoueur(joueurId);
        return ResponseEntity.ok(count);
    }

    @GetMapping("/statistiques/examens-aujourd-hui")
    @Operation(summary = "Compter les examens d'aujourd'hui", description = "Compte le nombre d'examens effectués aujourd'hui")
    public ResponseEntity<Long> compterExamensAujourdhui() {
        long count = donneesSanteService.compterExamensAujourdhui();
        return ResponseEntity.ok(count);
    }

    // Recherche avec filtres
    @GetMapping("/recherche")
    @Operation(summary = "Rechercher avec filtres", description = "Recherche les données de santé avec des filtres multiples")
    public ResponseEntity<Page<DonneesSante>> rechercherAvecFiltres(
            @RequestParam(required = false) Long joueurId,
            @RequestParam(required = false) Long staffMedicalId,
            @RequestParam(required = false) String typeExamen,
            @RequestParam(required = false) String statut,
            @RequestParam(required = false) String gravite,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateDebut,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateFin,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<DonneesSante> donnees = donneesSanteService.rechercherAvecFiltres(
                joueurId, staffMedicalId, typeExamen, statut, gravite, dateDebut, dateFin, pageable);
        return ResponseEntity.ok(donnees);
    }

    @GetMapping("/recherche/texte")
    @Operation(summary = "Recherche textuelle", description = "Recherche les données de santé par texte libre")
    public ResponseEntity<List<DonneesSante>> rechercherParTexte(
            @Parameter(description = "Terme de recherche") @RequestParam String searchTerm) {
        
        List<DonneesSante> donnees = donneesSanteService.rechercherParTexte(searchTerm);
        return ResponseEntity.ok(donnees);
    }
}
