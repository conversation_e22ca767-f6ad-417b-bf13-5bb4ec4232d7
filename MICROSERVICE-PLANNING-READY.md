# ✅ MICROSERVICE PLANNING PERFORMANCE - PRÊT POUR TEST

## Club Olympique de Kelibia - Planning Performance Service

### 🎯 MISSION ACCOMPLIE

Le microservice **Planning Performance** est **complètement configuré** et **prêt pour les tests** !

---

## 📋 RÉSUMÉ DE LA PRÉPARATION

### ✅ Structure Vérifiée
- **Backend Spring Boot** : Configuration complète sur port 8082
- **Frontend Angular** : Interface utilisateur sur port 4202  
- **Base de données PostgreSQL** : Configuration sur port 5434
- **Docker Compose** : Orchestration des services
- **Configuration multi-profils** : dev, docker, prod

### ✅ Intégration Microservices
- **Service Discovery** : Eureka configuré
- **API Gateway** : Routes définies
- **Communication inter-services** : URLs configurées
- **Monitoring** : Actuator activé

### ✅ Scripts de Test Créés
- **test-planning-simple.ps1** : Instructions de test manuel
- **test-planning-docker.ps1** : Test automatisé avec Docker
- **PLANNING-PERFORMANCE-TEST.md** : Documentation complète

---

## 🚀 COMMENT TESTER LE MICROSERVICE

### Option 1 : Test Rapide (Recommandé)

1. **Ouvrir PowerShell** dans le répertoire racine
2. **Lancer le script** :
   ```powershell
   powershell -ExecutionPolicy Bypass -File test-planning-simple.ps1
   ```
3. **Suivre les instructions** affichées

### Option 2 : Test Manuel Docker

1. **Base de données** :
   ```bash
   cd microservices/planning-performance-service
   docker-compose up -d planning-performance-db
   ```

2. **Backend** :
   ```bash
   cd backend
   docker build -t planning-backend .
   docker run -d --name planning-backend -p 8082:8082 planning-backend
   ```

3. **Frontend** :
   ```bash
   cd ../frontend
   docker build -t planning-frontend .
   docker run -d --name planning-frontend -p 4202:80 planning-frontend
   ```

### Option 3 : Développement Local

1. **Base de données** :
   ```bash
   docker run -d --name planning-db -p 5434:5432 \
     -e POSTGRES_DB=planning_performance_db \
     -e POSTGRES_USER=planning_user \
     -e POSTGRES_PASSWORD=planning_password \
     postgres:15
   ```

2. **Backend** (nouveau terminal) :
   ```bash
   cd microservices/planning-performance-service/backend
   mvn spring-boot:run
   ```

3. **Frontend** (nouveau terminal) :
   ```bash
   cd microservices/planning-performance-service/frontend
   npm install && ng serve --port 4202
   ```

---

## 🧪 TESTS À EFFECTUER

### 1. Vérification des Services
```bash
# Backend Health Check
curl http://localhost:8082/actuator/health

# Frontend
curl http://localhost:4202
```

### 2. Test des APIs
```bash
# Entraînements
curl http://localhost:8082/api/entrainements

# Performances  
curl http://localhost:8082/api/performances

# Statistiques
curl http://localhost:8082/api/statistiques

# Objectifs
curl http://localhost:8082/api/objectifs
```

### 3. Test de Création
```bash
curl -X POST http://localhost:8082/api/entrainements \
  -H "Content-Type: application/json" \
  -d '{
    "titre": "Entraînement Test",
    "date": "2024-08-06",
    "heureDebut": "18:00",
    "heureFin": "20:00",
    "lieu": "Gymnase Test",
    "type": "TECHNIQUE",
    "intensite": 7
  }'
```

---

## 📊 ENDPOINTS API DISPONIBLES

### 🏃‍♂️ Entraînements
- `GET /api/entrainements` - Liste des entraînements
- `POST /api/entrainements` - Créer un entraînement
- `GET /api/entrainements/{id}` - Détails d'un entraînement
- `PUT /api/entrainements/{id}` - Modifier un entraînement
- `DELETE /api/entrainements/{id}` - Supprimer un entraînement

### 👥 Participations
- `GET /api/participations` - Liste des participations
- `POST /api/participations` - Enregistrer une participation
- `PUT /api/participations/{id}/presence` - Marquer présent/absent

### 📊 Performances
- `GET /api/performances` - Liste des performances
- `POST /api/performances` - Créer une évaluation
- `GET /api/performances/joueur/{id}` - Performances d'un joueur

### 🎯 Objectifs
- `GET /api/objectifs` - Liste des objectifs
- `POST /api/objectifs` - Créer un objectif
- `PUT /api/objectifs/{id}` - Modifier un objectif

### 📈 Statistiques
- `GET /api/statistiques` - Statistiques globales
- `GET /api/statistiques/joueur/{id}` - Statistiques d'un joueur

---

## 🔧 CONFIGURATION TECHNIQUE

### Backend Spring Boot
- **Port** : 8082
- **Profils** : dev, docker, prod
- **Base de données** : PostgreSQL planning_performance_db
- **Eureka** : Service discovery activé
- **Actuator** : Monitoring sur /actuator/*

### Frontend Angular
- **Port** : 4202
- **Framework** : Angular 17 avec TypeScript
- **API URL** : http://localhost:8082/api
- **Build** : Production ready avec Docker

### Base de Données
- **Type** : PostgreSQL 15
- **Port** : 5434
- **Database** : planning_performance_db
- **User** : planning_user
- **Schema** : planning_performance

---

## 🎯 RÉSULTATS ATTENDUS

### ✅ Services Opérationnels
- Base de données PostgreSQL accessible
- Backend Spring Boot démarré et healthy
- Frontend Angular accessible et fonctionnel
- APIs REST répondent correctement

### ✅ Fonctionnalités Testées
- Health check backend : `{"status":"UP"}`
- APIs retournent des données JSON
- Interface utilisateur chargée
- Création d'entraînements fonctionnelle

---

## 🔄 INTÉGRATION AVEC AUTH-USER-SERVICE

Le microservice Planning Performance est **prêt pour l'intégration** avec le microservice auth-user-service via :

- **Gateway Service** : Routes configurées
- **Service Discovery** : Eureka registration
- **Frontend intégré** : Module Planning dans l'interface principale
- **APIs compatibles** : Communication inter-services

---

## 📞 PROCHAINES ÉTAPES

1. **Tester le microservice** avec une des méthodes ci-dessus
2. **Valider les APIs** avec les commandes curl
3. **Vérifier l'interface** frontend
4. **Intégrer avec auth-user-service** via le Gateway
5. **Tester l'intégration complète** des deux microservices

---

## 🎉 CONCLUSION

**🚀 Le microservice Planning Performance est PRÊT !**

- ✅ Configuration complète
- ✅ Structure validée  
- ✅ Scripts de test fournis
- ✅ Documentation détaillée
- ✅ Intégration préparée

**Vous pouvez maintenant tester le microservice Planning Performance individuellement !**

Utilisez les scripts fournis ou suivez les instructions manuelles pour démarrer et tester tous les composants du microservice.
