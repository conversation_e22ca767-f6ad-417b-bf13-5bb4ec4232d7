package com.sprintbot.medicaladmin.service;

import com.sprintbot.medicaladmin.entity.DemandeAdministrative;
import com.sprintbot.medicaladmin.repository.DemandeAdministrativeRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Service pour la gestion des demandes administratives
 * Implémente la logique métier pour le workflow de validation des demandes
 */
@Service
@Transactional
public class DemandeAdministrativeService {

    private static final Logger logger = LoggerFactory.getLogger(DemandeAdministrativeService.class);

    @Autowired
    private DemandeAdministrativeRepository demandeRepository;

    // CRUD Operations
    public DemandeAdministrative creerDemande(DemandeAdministrative demande) {
        logger.info("Création d'une nouvelle demande administrative par le demandeur ID: {}", demande.getDemandeurId());
        
        // Validation métier et initialisation
        if (demande.getDateSoumission() == null) {
            demande.setDateSoumission(LocalDate.now());
        }
        
        if (demande.getStatut() == null) {
            demande.setStatut("EN_ATTENTE");
        }
        
        if (demande.getPriorite() == null) {
            demande.setPriorite("NORMALE");
        }
        
        // Définir les validations nécessaires selon le type de demande
        demande.definirValidationsNecessaires(demande.getTypeDemande());
        
        // Définir l'échéance par défaut selon la priorité
        if (demande.getDateEcheance() == null) {
            demande.setDateEcheance(calculerDateEcheanceParDefaut(demande.getPriorite()));
        }
        
        DemandeAdministrative saved = demandeRepository.save(demande);
        logger.info("Demande administrative créée avec l'ID: {}", saved.getId());
        return saved;
    }

    @Transactional(readOnly = true)
    public Optional<DemandeAdministrative> obtenirDemande(Long id) {
        return demandeRepository.findById(id);
    }

    @Transactional(readOnly = true)
    public List<DemandeAdministrative> obtenirToutesDemandes() {
        return demandeRepository.findAll();
    }

    @Transactional(readOnly = true)
    public Page<DemandeAdministrative> obtenirDemandesPaginees(Pageable pageable) {
        return demandeRepository.findAll(pageable);
    }

    public DemandeAdministrative mettreAJourDemande(DemandeAdministrative demande) {
        logger.info("Mise à jour de la demande administrative ID: {}", demande.getId());
        
        if (!demandeRepository.existsById(demande.getId())) {
            throw new RuntimeException("Demande non trouvée avec l'ID: " + demande.getId());
        }
        
        return demandeRepository.save(demande);
    }

    public void supprimerDemande(Long id) {
        logger.info("Suppression de la demande administrative ID: {}", id);
        
        if (!demandeRepository.existsById(id)) {
            throw new RuntimeException("Demande non trouvée avec l'ID: " + id);
        }
        
        demandeRepository.deleteById(id);
    }

    // Gestion du workflow
    public DemandeAdministrative soumettreNouveauDemande(DemandeAdministrative demande) {
        logger.info("Soumission d'une nouvelle demande par le demandeur ID: {}", demande.getDemandeurId());
        
        demande.soumettre();
        return demandeRepository.save(demande);
    }

    public DemandeAdministrative commencerTraitement(Long demandeId, Long approvateurId) {
        logger.info("Début de traitement de la demande ID: {} par l'approbateur ID: {}", demandeId, approvateurId);
        
        Optional<DemandeAdministrative> optionalDemande = demandeRepository.findById(demandeId);
        if (optionalDemande.isEmpty()) {
            throw new RuntimeException("Demande non trouvée avec l'ID: " + demandeId);
        }
        
        DemandeAdministrative demande = optionalDemande.get();
        
        if (!"EN_ATTENTE".equals(demande.getStatut())) {
            throw new RuntimeException("La demande n'est pas en attente de traitement");
        }
        
        demande.commencerTraitement(approvateurId);
        return demandeRepository.save(demande);
    }

    public DemandeAdministrative validerDemande(Long demandeId, String commentaire) {
        logger.info("Validation de la demande ID: {}", demandeId);
        
        Optional<DemandeAdministrative> optionalDemande = demandeRepository.findById(demandeId);
        if (optionalDemande.isEmpty()) {
            throw new RuntimeException("Demande non trouvée avec l'ID: " + demandeId);
        }
        
        DemandeAdministrative demande = optionalDemande.get();
        
        if (!demande.estEnTraitement() && !demande.estEnAttente()) {
            throw new RuntimeException("La demande ne peut pas être validée dans son état actuel");
        }
        
        // Vérifier que toutes les validations nécessaires sont obtenues
        if (!demande.toutesValidationsObtenues()) {
            throw new RuntimeException("Toutes les validations nécessaires ne sont pas encore obtenues");
        }
        
        demande.valider(commentaire);
        return demandeRepository.save(demande);
    }

    public DemandeAdministrative rejeterDemande(Long demandeId, String commentaire) {
        logger.info("Rejet de la demande ID: {}", demandeId);
        
        Optional<DemandeAdministrative> optionalDemande = demandeRepository.findById(demandeId);
        if (optionalDemande.isEmpty()) {
            throw new RuntimeException("Demande non trouvée avec l'ID: " + demandeId);
        }
        
        DemandeAdministrative demande = optionalDemande.get();
        
        if (!demande.estEnTraitement() && !demande.estEnAttente()) {
            throw new RuntimeException("La demande ne peut pas être rejetée dans son état actuel");
        }
        
        demande.rejeter(commentaire);
        return demandeRepository.save(demande);
    }

    public DemandeAdministrative suspendreDemande(Long demandeId, String commentaire) {
        logger.info("Suspension de la demande ID: {}", demandeId);
        
        Optional<DemandeAdministrative> optionalDemande = demandeRepository.findById(demandeId);
        if (optionalDemande.isEmpty()) {
            throw new RuntimeException("Demande non trouvée avec l'ID: " + demandeId);
        }
        
        DemandeAdministrative demande = optionalDemande.get();
        demande.suspendre(commentaire);
        
        return demandeRepository.save(demande);
    }

    // Gestion des validations multiples
    public DemandeAdministrative donnerValidationCoach(Long demandeId, boolean validation) {
        logger.info("Validation coach pour la demande ID: {} - {}", demandeId, validation ? "APPROUVEE" : "REJETEE");
        
        Optional<DemandeAdministrative> optionalDemande = demandeRepository.findById(demandeId);
        if (optionalDemande.isEmpty()) {
            throw new RuntimeException("Demande non trouvée avec l'ID: " + demandeId);
        }
        
        DemandeAdministrative demande = optionalDemande.get();
        
        if (!demande.getNecessiteValidationCoach()) {
            throw new RuntimeException("Cette demande ne nécessite pas de validation coach");
        }
        
        demande.setValidationCoach(validation);
        return demandeRepository.save(demande);
    }

    public DemandeAdministrative donnerValidationMedical(Long demandeId, boolean validation) {
        logger.info("Validation médicale pour la demande ID: {} - {}", demandeId, validation ? "APPROUVEE" : "REJETEE");
        
        Optional<DemandeAdministrative> optionalDemande = demandeRepository.findById(demandeId);
        if (optionalDemande.isEmpty()) {
            throw new RuntimeException("Demande non trouvée avec l'ID: " + demandeId);
        }
        
        DemandeAdministrative demande = optionalDemande.get();
        
        if (!demande.getNecessiteValidationMedical()) {
            throw new RuntimeException("Cette demande ne nécessite pas de validation médicale");
        }
        
        demande.setValidationMedical(validation);
        return demandeRepository.save(demande);
    }

    public DemandeAdministrative donnerValidationFinancier(Long demandeId, boolean validation) {
        logger.info("Validation financière pour la demande ID: {} - {}", demandeId, validation ? "APPROUVEE" : "REJETEE");
        
        Optional<DemandeAdministrative> optionalDemande = demandeRepository.findById(demandeId);
        if (optionalDemande.isEmpty()) {
            throw new RuntimeException("Demande non trouvée avec l'ID: " + demandeId);
        }
        
        DemandeAdministrative demande = optionalDemande.get();
        
        if (!demande.getNecessiteValidationFinancier()) {
            throw new RuntimeException("Cette demande ne nécessite pas de validation financière");
        }
        
        demande.setValidationFinancier(validation);
        return demandeRepository.save(demande);
    }

    // Recherches par demandeur
    @Transactional(readOnly = true)
    public List<DemandeAdministrative> obtenirDemandesParDemandeur(Long demandeurId) {
        return demandeRepository.findByDemandeurIdOrderByDateSoumissionDesc(demandeurId);
    }

    @Transactional(readOnly = true)
    public Page<DemandeAdministrative> obtenirDemandesParDemandeurPaginees(Long demandeurId, Pageable pageable) {
        return demandeRepository.findByDemandeurIdOrderByDateSoumissionDesc(demandeurId, pageable);
    }

    // Recherches par approbateur
    @Transactional(readOnly = true)
    public List<DemandeAdministrative> obtenirDemandesParApprobateur(Long approvateurId) {
        return demandeRepository.findByApprovateurIdOrderByDateSoumissionDesc(approvateurId);
    }

    @Transactional(readOnly = true)
    public Page<DemandeAdministrative> obtenirDemandesParApprovateurPaginees(Long approvateurId, Pageable pageable) {
        return demandeRepository.findByApprovateurIdOrderByDateSoumissionDesc(approvateurId, pageable);
    }

    // Recherches par statut
    @Transactional(readOnly = true)
    public List<DemandeAdministrative> obtenirDemandesEnAttente() {
        return demandeRepository.findDemandesEnAttente();
    }

    @Transactional(readOnly = true)
    public List<DemandeAdministrative> obtenirDemandesEnCours() {
        return demandeRepository.findDemandesEnCours();
    }

    @Transactional(readOnly = true)
    public List<DemandeAdministrative> obtenirDemandesUrgentes() {
        return demandeRepository.findDemandesUrgentes();
    }

    @Transactional(readOnly = true)
    public List<DemandeAdministrative> obtenirDemandesUrgentesEnCours() {
        return demandeRepository.findDemandesUrgentesEnCours();
    }

    // Recherches pour validations
    @Transactional(readOnly = true)
    public List<DemandeAdministrative> obtenirDemandesEnAttenteValidationCoach() {
        return demandeRepository.findDemandesEnAttenteValidationCoach();
    }

    @Transactional(readOnly = true)
    public List<DemandeAdministrative> obtenirDemandesEnAttenteValidationMedical() {
        return demandeRepository.findDemandesEnAttenteValidationMedical();
    }

    @Transactional(readOnly = true)
    public List<DemandeAdministrative> obtenirDemandesEnAttenteValidationFinancier() {
        return demandeRepository.findDemandesEnAttenteValidationFinancier();
    }

    @Transactional(readOnly = true)
    public List<DemandeAdministrative> obtenirDemandesPretesValidationFinale() {
        return demandeRepository.findDemandesPretesValidationFinale();
    }

    // Alertes et échéances
    @Transactional(readOnly = true)
    public List<DemandeAdministrative> obtenirDemandesEchues() {
        return demandeRepository.findDemandesEchues();
    }

    @Transactional(readOnly = true)
    public List<DemandeAdministrative> obtenirDemandesEcheanceProche() {
        return demandeRepository.findDemandesEcheanceProche();
    }

    @Transactional(readOnly = true)
    public List<DemandeAdministrative> obtenirDemandesAnciennesEnAttente() {
        return demandeRepository.findDemandesAnciennesEnAttente();
    }

    @Transactional(readOnly = true)
    public List<DemandeAdministrative> obtenirDemandesAnciennesEnTraitement() {
        return demandeRepository.findDemandesAnciennesEnTraitement();
    }

    // Recherches par type et période
    @Transactional(readOnly = true)
    public List<DemandeAdministrative> obtenirDemandesParType(String typeDemande) {
        return demandeRepository.findByTypeDemandeOrderByDateSoumissionDesc(typeDemande);
    }

    @Transactional(readOnly = true)
    public List<DemandeAdministrative> obtenirDemandesParPeriode(LocalDate dateDebut, LocalDate dateFin) {
        return demandeRepository.findByDateSoumissionBetweenOrderByDateSoumissionDesc(dateDebut, dateFin);
    }

    // Statistiques
    @Transactional(readOnly = true)
    public long compterDemandesParDemandeur(Long demandeurId) {
        return demandeRepository.countByDemandeurId(demandeurId);
    }

    @Transactional(readOnly = true)
    public long compterDemandesEnAttente() {
        return demandeRepository.countDemandesEnAttente();
    }

    @Transactional(readOnly = true)
    public long compterDemandesEnTraitement() {
        return demandeRepository.countDemandesEnTraitement();
    }

    @Transactional(readOnly = true)
    public long compterDemandesAujourdhui() {
        return demandeRepository.countDemandesAujourdhui();
    }

    // Recherche avec filtres
    @Transactional(readOnly = true)
    public Page<DemandeAdministrative> rechercherAvecFiltres(Long demandeurId, String typeDemandeur, Long approvateurId,
                                                           String typeDemande, String statut, String priorite,
                                                           LocalDate dateDebut, LocalDate dateFin, Pageable pageable) {
        return demandeRepository.findWithFilters(demandeurId, typeDemandeur, approvateurId, typeDemande, statut,
                priorite, dateDebut, dateFin, pageable);
    }

    @Transactional(readOnly = true)
    public List<DemandeAdministrative> rechercherParTexte(String searchTerm) {
        return demandeRepository.searchByText(searchTerm);
    }

    // Gestion des coûts
    @Transactional(readOnly = true)
    public List<DemandeAdministrative> obtenirDemandesAvecCoutSuperieur(Double montant) {
        return demandeRepository.findDemandesAvecCoutSuperieur(montant);
    }

    @Transactional(readOnly = true)
    public Double calculerCoutEstimeValidees(LocalDate dateDebut, LocalDate dateFin) {
        return demandeRepository.sumCoutEstimeValideesBetweenDates(dateDebut, dateFin);
    }

    @Transactional(readOnly = true)
    public Double calculerCoutReel(LocalDate dateDebut, LocalDate dateFin) {
        return demandeRepository.sumCoutReelBetweenDates(dateDebut, dateFin);
    }

    // Rapports
    @Transactional(readOnly = true)
    public List<Object[]> obtenirStatistiquesParDemandeur(LocalDate dateDebut, LocalDate dateFin) {
        return demandeRepository.getStatistiquesParDemandeur(dateDebut, dateFin);
    }

    @Transactional(readOnly = true)
    public List<Object[]> obtenirStatistiquesParApprobateur(LocalDate dateDebut, LocalDate dateFin) {
        return demandeRepository.getStatistiquesParApprobateur(dateDebut, dateFin);
    }

    // Méthodes utilitaires privées
    private LocalDate calculerDateEcheanceParDefaut(String priorite) {
        LocalDate maintenant = LocalDate.now();
        
        switch (priorite.toUpperCase()) {
            case "URGENTE":
                return maintenant.plusDays(1);
            case "ELEVEE":
                return maintenant.plusDays(3);
            case "NORMALE":
                return maintenant.plusDays(7);
            case "FAIBLE":
                return maintenant.plusDays(14);
            default:
                return maintenant.plusDays(7);
        }
    }
}
