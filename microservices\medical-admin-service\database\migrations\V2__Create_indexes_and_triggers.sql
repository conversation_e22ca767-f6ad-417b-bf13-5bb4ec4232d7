-- Migration V2: Création des index et triggers
-- Date: 2024-07-29
-- Description: Ajout des index pour optimiser les performances et des triggers pour l'audit

-- Utilisation du schéma
SET search_path TO medical_admin;

-- Index pour la table donnees_sante
CREATE INDEX idx_donnees_sante_joueur_id ON donnees_sante(joueur_id);
CREATE INDEX idx_donnees_sante_staff_medical_id ON donnees_sante(staff_medical_id);
CREATE INDEX idx_donnees_sante_type_examen ON donnees_sante(type_examen);
CREATE INDEX idx_donnees_sante_date_examen ON donnees_sante(date_examen);
CREATE INDEX idx_donnees_sante_statut ON donnees_sante(statut);
CREATE INDEX idx_donnees_sante_gravite ON donnees_sante(gravite);

-- Index pour la table rendez_vous_medicaux
CREATE INDEX idx_rendez_vous_joueur_id ON rendez_vous_medicaux(joueur_id);
CREATE INDEX idx_rendez_vous_staff_medical_id ON rendez_vous_medicaux(staff_medical_id);
CREATE INDEX idx_rendez_vous_date ON rendez_vous_medicaux(date_rendez_vous);
CREATE INDEX idx_rendez_vous_statut ON rendez_vous_medicaux(statut);
CREATE INDEX idx_rendez_vous_type ON rendez_vous_medicaux(type_rendez_vous);
CREATE INDEX idx_rendez_vous_priorite ON rendez_vous_medicaux(priorite);

-- Index pour la table demandes_administratives
CREATE INDEX idx_demandes_demandeur_id ON demandes_administratives(demandeur_id);
CREATE INDEX idx_demandes_approbateur_id ON demandes_administratives(approbateur_id);
CREATE INDEX idx_demandes_type_demande ON demandes_administratives(type_demande);
CREATE INDEX idx_demandes_statut ON demandes_administratives(statut);
CREATE INDEX idx_demandes_priorite ON demandes_administratives(priorite);
CREATE INDEX idx_demandes_date_soumission ON demandes_administratives(date_soumission);
CREATE INDEX idx_demandes_date_echeance ON demandes_administratives(date_echeance);

-- Triggers pour mettre à jour automatiquement updated_at
CREATE TRIGGER update_donnees_sante_updated_at 
    BEFORE UPDATE ON donnees_sante 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_rendez_vous_updated_at 
    BEFORE UPDATE ON rendez_vous_medicaux 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_demandes_updated_at 
    BEFORE UPDATE ON demandes_administratives 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
