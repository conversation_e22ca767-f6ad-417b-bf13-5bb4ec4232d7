package com.sprintbot.communication.repository;

import com.sprintbot.communication.entity.Message;
import com.sprintbot.communication.entity.StatutMessage;
import com.sprintbot.communication.entity.TypeMessage;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository pour l'entité Message
 */
@Repository
public interface MessageRepository extends JpaRepository<Message, Long> {

    /**
     * Trouve les messages d'une conversation avec pagination
     */
    Page<Message> findByConversationIdOrderByDateEnvoiDesc(Long conversationId, Pageable pageable);

    /**
     * Trouve les messages d'une conversation après une date
     */
    List<Message> findByConversationIdAndDateEnvoiAfterOrderByDateEnvoiAsc(
            Long conversationId, LocalDateTime depuis);

    /**
     * Trouve les messages d'un expéditeur dans une conversation
     */
    List<Message> findByConversationIdAndExpediteurIdOrderByDateEnvoiDesc(
            Long conversationId, Long expediteurId);

    /**
     * Recherche de messages par contenu
     */
    @Query("SELECT m FROM Message m " +
           "WHERE m.conversation.id = :conversationId " +
           "AND LOWER(m.contenu) LIKE LOWER(CONCAT('%', :recherche, '%')) " +
           "ORDER BY m.dateEnvoi DESC")
    List<Message> rechercherMessagesParContenu(
            @Param("conversationId") Long conversationId,
            @Param("recherche") String recherche);

    /**
     * Trouve les messages épinglés d'une conversation
     */
    List<Message> findByConversationIdAndEstEpingleTrueOrderByDateEnvoiDesc(Long conversationId);

    /**
     * Trouve les messages par type
     */
    List<Message> findByConversationIdAndTypeOrderByDateEnvoiDesc(
            Long conversationId, TypeMessage type);

    /**
     * Trouve les messages modifiés
     */
    List<Message> findByConversationIdAndEstModifieTrueOrderByDateModificationContenuDesc(
            Long conversationId);

    /**
     * Compte les messages non lus d'un utilisateur dans une conversation
     */
    @Query("SELECT COUNT(m) FROM Message m " +
           "LEFT JOIN m.lectures l ON l.utilisateurId = :utilisateurId " +
           "WHERE m.conversation.id = :conversationId " +
           "AND m.expediteurId != :utilisateurId " +
           "AND l.id IS NULL")
    Long countMessagesNonLus(
            @Param("conversationId") Long conversationId,
            @Param("utilisateurId") Long utilisateurId);

    /**
     * Trouve les messages avec des réactions
     */
    @Query("SELECT DISTINCT m FROM Message m " +
           "JOIN m.reactions r " +
           "WHERE m.conversation.id = :conversationId " +
           "ORDER BY m.dateEnvoi DESC")
    List<Message> findMessagesAvecReactions(@Param("conversationId") Long conversationId);

    /**
     * Trouve les messages d'un utilisateur avec le plus de réactions
     */
    @Query("SELECT m FROM Message m " +
           "WHERE m.expediteurId = :utilisateurId " +
           "AND m.nombreReactions > 0 " +
           "ORDER BY m.nombreReactions DESC")
    List<Message> findMessagesLesPlusReagis(
            @Param("utilisateurId") Long utilisateurId,
            Pageable pageable);

    /**
     * Trouve les réponses à un message
     */
    List<Message> findByReponseAIdOrderByDateEnvoiAsc(Long messageId);

    /**
     * Trouve les messages par statut
     */
    List<Message> findByConversationIdAndStatutOrderByDateEnvoiDesc(
            Long conversationId, StatutMessage statut);

    /**
     * Trouve les derniers messages de chaque conversation d'un utilisateur
     */
    @Query("SELECT m FROM Message m " +
           "WHERE m.id IN (" +
           "    SELECT MAX(m2.id) FROM Message m2 " +
           "    JOIN m2.conversation c " +
           "    JOIN c.participants p " +
           "    WHERE p.utilisateurId = :utilisateurId " +
           "    AND c.statut = 'ACTIF' " +
           "    GROUP BY c.id" +
           ") " +
           "ORDER BY m.dateEnvoi DESC")
    List<Message> findDerniersMessagesParConversation(@Param("utilisateurId") Long utilisateurId);

    /**
     * Trouve les messages envoyés dans une période
     */
    @Query("SELECT m FROM Message m " +
           "WHERE m.conversation.id = :conversationId " +
           "AND m.dateEnvoi BETWEEN :debut AND :fin " +
           "ORDER BY m.dateEnvoi ASC")
    List<Message> findMessagesParPeriode(
            @Param("conversationId") Long conversationId,
            @Param("debut") LocalDateTime debut,
            @Param("fin") LocalDateTime fin);

    /**
     * Compte les messages par type dans une conversation
     */
    @Query("SELECT m.type, COUNT(m) FROM Message m " +
           "WHERE m.conversation.id = :conversationId " +
           "GROUP BY m.type")
    List<Object[]> countMessagesParType(@Param("conversationId") Long conversationId);

    /**
     * Trouve les messages avec des fichiers
     */
    @Query("SELECT m FROM Message m " +
           "WHERE m.conversation.id = :conversationId " +
           "AND m.fichierUrl IS NOT NULL " +
           "ORDER BY m.dateEnvoi DESC")
    List<Message> findMessagesAvecFichiers(@Param("conversationId") Long conversationId);

    /**
     * Supprime les anciens messages
     */
    @Query("DELETE FROM Message m " +
           "WHERE m.dateEnvoi < :dateLimit " +
           "AND m.estEpingle = false")
    void supprimerAncienMessages(@Param("dateLimit") LocalDateTime dateLimit);

    /**
     * Trouve les messages les plus récents globalement
     */
    @Query("SELECT m FROM Message m " +
           "JOIN m.conversation c " +
           "JOIN c.participants p " +
           "WHERE p.utilisateurId = :utilisateurId " +
           "AND c.statut = 'ACTIF' " +
           "ORDER BY m.dateEnvoi DESC")
    List<Message> findMessagesRecents(
            @Param("utilisateurId") Long utilisateurId,
            Pageable pageable);

    /**
     * Statistiques des messages par utilisateur
     */
    @Query("SELECT m.expediteurId, COUNT(m), MAX(m.dateEnvoi) " +
           "FROM Message m " +
           "WHERE m.conversation.id = :conversationId " +
           "GROUP BY m.expediteurId " +
           "ORDER BY COUNT(m) DESC")
    List<Object[]> getStatistiquesMessagesParUtilisateur(@Param("conversationId") Long conversationId);
}
