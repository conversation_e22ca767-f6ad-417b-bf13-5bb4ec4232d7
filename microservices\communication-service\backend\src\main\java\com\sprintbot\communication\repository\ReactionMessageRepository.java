package com.sprintbot.communication.repository;

import com.sprintbot.communication.entity.ReactionMessage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository pour l'entité ReactionMessage
 */
@Repository
public interface ReactionMessageRepository extends JpaRepository<ReactionMessage, Long> {

    /**
     * Trouve les réactions d'un message
     */
    List<ReactionMessage> findByMessageIdOrderByDateReactionAsc(Long messageId);

    /**
     * Trouve une réaction spécifique
     */
    Optional<ReactionMessage> findByMessageIdAndUtilisateurIdAndEmoji(
            Long messageId, Long utilisateurId, String emoji);

    /**
     * Trouve les réactions d'un utilisateur sur un message
     */
    List<ReactionMessage> findByMessageIdAndUtilisateurIdOrderByDateReactionAsc(
            Long messageId, Long utilisateurId);

    /**
     * Compte les réactions par emoji pour un message
     */
    @Query("SELECT r.emoji, COUNT(r) FROM ReactionMessage r " +
           "WHERE r.message.id = :messageId " +
           "GROUP BY r.emoji " +
           "ORDER BY COUNT(r) DESC")
    List<Object[]> countReactionsParEmoji(@Param("messageId") Long messageId);

    /**
     * Trouve les emojis les plus utilisés
     */
    @Query("SELECT r.emoji, COUNT(r) FROM ReactionMessage r " +
           "GROUP BY r.emoji " +
           "ORDER BY COUNT(r) DESC")
    List<Object[]> findEmojisLesPlusUtilises();

    /**
     * Trouve les réactions d'un utilisateur
     */
    List<ReactionMessage> findByUtilisateurIdOrderByDateReactionDesc(Long utilisateurId);

    /**
     * Vérifie si un utilisateur a réagi à un message
     */
    boolean existsByMessageIdAndUtilisateurId(Long messageId, Long utilisateurId);

    /**
     * Compte les réactions d'un message
     */
    Long countByMessageId(Long messageId);

    /**
     * Supprime une réaction spécifique
     */
    void deleteByMessageIdAndUtilisateurIdAndEmoji(Long messageId, Long utilisateurId, String emoji);

    /**
     * Supprime toutes les réactions d'un utilisateur sur un message
     */
    void deleteByMessageIdAndUtilisateurId(Long messageId, Long utilisateurId);

    /**
     * Trouve les messages les plus réagis
     */
    @Query("SELECT r.message.id, COUNT(r) FROM ReactionMessage r " +
           "GROUP BY r.message.id " +
           "ORDER BY COUNT(r) DESC")
    List<Object[]> findMessagesLesPlusReagis();

    /**
     * Statistiques des réactions par utilisateur
     */
    @Query("SELECT r.utilisateurId, COUNT(r), COUNT(DISTINCT r.emoji) " +
           "FROM ReactionMessage r " +
           "GROUP BY r.utilisateurId " +
           "ORDER BY COUNT(r) DESC")
    List<Object[]> getStatistiquesParUtilisateur();
}
