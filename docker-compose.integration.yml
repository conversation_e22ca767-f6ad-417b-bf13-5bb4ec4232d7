# Docker Compose pour l'intégration des microservices SprintBot
# Configuration simplifiée pour tester l'intégration auth-user-service et planning-performance-service

version: '3.8'

services:
  # Base de données pour auth-user-service
  auth-user-db:
    image: postgres:15-alpine
    container_name: auth-user-db
    environment:
      POSTGRES_DB: auth_user_db
      POSTGRES_USER: auth_user
      POSTGRES_PASSWORD: auth_password
    ports:
      - "5433:5432"
    volumes:
      - auth_user_data:/var/lib/postgresql/data
      - ./microservices/auth-user-service/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - sprintbot-network
    restart: unless-stopped

  # Base de données pour planning-performance-service
  planning-performance-db:
    image: postgres:15-alpine
    container_name: planning-performance-db
    environment:
      POSTGRES_DB: planning_performance_db
      POSTGRES_USER: planning_user
      POSTGRES_PASSWORD: planning_password
    ports:
      - "5434:5432"
    volumes:
      - planning_performance_data:/var/lib/postgresql/data
      - ./microservices/planning-performance-service/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - sprintbot-network
    restart: unless-stopped

  # Discovery Service (Eureka)
  discovery-service:
    build: ./microservices/discovery-service/backend
    container_name: discovery-service
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SERVER_PORT: 8761
    ports:
      - "8761:8761"
    networks:
      - sprintbot-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8761/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis pour le Gateway
  redis:
    image: redis:7.2-alpine
    container_name: redis
    ports:
      - "6379:6379"
    networks:
      - sprintbot-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Gateway Service
  gateway-service:
    build: ./microservices/gateway-service/backend
    container_name: gateway-service
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SERVER_PORT: 8080
      EUREKA_CLIENT_SERVICE_URL: http://discovery-service:8761/eureka/
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET_KEY: SprintBot-Gateway-Secret-Key-2024-Very-Long-And-Secure
    ports:
      - "8080:8080"
    depends_on:
      - discovery-service
      - redis
    networks:
      - sprintbot-network
    restart: unless-stopped

  # Auth User Service
  auth-user-service:
    build: ./microservices/auth-user-service/backend
    container_name: auth-user-service
    environment:
      SPRING_PROFILES_ACTIVE: docker
      DB_HOST: auth-user-db
      DB_PORT: 5432
      DB_NAME: auth_user_db
      DB_USER: auth_user
      DB_PASSWORD: auth_password
      JWT_SECRET: sprintbot-auth-secret-key-2024
      EUREKA_CLIENT_SERVICE_URL: http://discovery-service:8761/eureka/
    ports:
      - "8081:8081"
    depends_on:
      - auth-user-db
      - discovery-service
    networks:
      - sprintbot-network
    restart: unless-stopped

  # Planning Performance Service
  planning-performance-service:
    build: ./microservices/planning-performance-service/backend
    container_name: planning-performance-service
    environment:
      SPRING_PROFILES_ACTIVE: docker
      DB_HOST: planning-performance-db
      DB_PORT: 5432
      DB_NAME: planning_performance_db
      DB_USER: planning_user
      DB_PASSWORD: planning_password
      JWT_SECRET: sprintbot-planning-secret-key-2024
      EUREKA_CLIENT_SERVICE_URL: http://discovery-service:8761/eureka/
    ports:
      - "8082:8082"
    depends_on:
      - planning-performance-db
      - discovery-service
    networks:
      - sprintbot-network
    restart: unless-stopped

  # Frontend Auth User Service
  auth-user-frontend:
    build: ./microservices/auth-user-service/frontend
    container_name: auth-user-frontend
    ports:
      - "4201:80"
    depends_on:
      - gateway-service
    networks:
      - sprintbot-network
    restart: unless-stopped

networks:
  sprintbot-network:
    name: sprintbot-network
    driver: bridge

volumes:
  auth_user_data:
    name: auth_user_data
  planning_performance_data:
    name: planning_performance_data
