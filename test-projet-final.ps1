Write-Host "=== TEST FINAL DU PROJET ===" -ForegroundColor Green
Write-Host "Club Olympique de Kelibia" -ForegroundColor Cyan
Write-Host ""

Write-Host "Attente de 30 secondes..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

Write-Host "Test des services..." -ForegroundColor Cyan

$servicesUp = 0

# Test Discovery Service
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8761" -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "OK - Discovery Service operationnel" -ForegroundColor Green
        $servicesUp++
    }
} catch {
    Write-Host "ERREUR - Discovery Service non accessible" -ForegroundColor Red
}

# Test Gateway Service
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/actuator/health" -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "OK - Gateway Service operationnel" -ForegroundColor Green
        $servicesUp++
    }
} catch {
    Write-Host "ERREUR - Gateway Service non accessible" -ForegroundColor Red
}

# Test Auth Service
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8081/actuator/health" -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "OK - Auth Service operationnel" -ForegroundColor Green
        $servicesUp++
    }
} catch {
    Write-Host "ERREUR - Auth Service non accessible" -ForegroundColor Red
}

# Test Planning Service
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8082/actuator/health" -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "OK - Planning Service operationnel" -ForegroundColor Green
        $servicesUp++
    }
} catch {
    Write-Host "ERREUR - Planning Service non accessible" -ForegroundColor Red
}

# Test Frontend
try {
    $response = Invoke-WebRequest -Uri "http://localhost:4201" -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "OK - Frontend Angular operationnel" -ForegroundColor Green
        $servicesUp++
    }
} catch {
    Write-Host "ERREUR - Frontend Angular non accessible" -ForegroundColor Red
}

Write-Host ""
Write-Host "RESULTAT : $servicesUp/5 services operationnels" -ForegroundColor Yellow

if ($servicesUp -ge 4) {
    Write-Host ""
    Write-Host "EXCELLENT ! Le projet est operationnel !" -ForegroundColor Green
    Write-Host ""
    Write-Host "URLS DISPONIBLES :" -ForegroundColor Cyan
    Write-Host "Frontend Principal : http://localhost:4201" -ForegroundColor White
    Write-Host "Discovery Console  : http://localhost:8761" -ForegroundColor White
    Write-Host "Gateway API        : http://localhost:8080" -ForegroundColor White
    Write-Host ""
    Write-Host "COMPTE ADMINISTRATEUR :" -ForegroundColor Yellow
    Write-Host "Username : admin" -ForegroundColor White
    Write-Host "Password : admin123" -ForegroundColor White
    Write-Host ""
    Write-Host "VOUS POUVEZ MAINTENANT UTILISER LA PLATEFORME !" -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "ATTENTION : Certains services ne sont pas encore prets." -ForegroundColor Yellow
    Write-Host "Attendez quelques minutes et testez les URLs." -ForegroundColor White
}

Write-Host ""
Write-Host "Club Olympique de Kelibia - Test termine !" -ForegroundColor Yellow

Read-Host "Appuyez sur Entree pour continuer"
