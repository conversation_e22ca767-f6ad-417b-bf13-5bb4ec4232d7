# 🏐 SprintBot - Communication Service

Microservice de communication avec messagerie interne, notifications système et chatbot intelligent.

## 🚀 Vue d'ensemble

Le **Communication Service** est le 4ème microservice de l'écosystème SprintBot, spécialisé dans :

### 🎯 **Fonctionnalités principales**

#### 💬 **Messagerie interne (Chat)**
- Chat en temps réel avec WebSocket
- Conversations privées et de groupe
- Historique des messages
- Statuts de lecture et de livraison
- Partage de fichiers et médias
- Recherche dans les conversations

#### 🔔 **Notifications système**
- Notifications push en temps réel
- Notifications par email
- Templates personnalisables
- Gestion des préférences utilisateur
- Historique des notifications
- Notifications programmées

#### 🤖 **Chatbot intelligent**
- Assistant IA pour réponses automatiques
- Base de connaissances SprintBot
- Intégration avec les autres microservices
- Apprentissage des conversations
- Commandes prédéfinies
- Support multilingue

## 🏗️ **Architecture technique**

### **Backend Spring Boot (Port 8084)**
- **WebSocket** pour communication temps réel
- **REST API** pour gestion des données
- **PostgreSQL** avec schéma dédié `communication`
- **Redis** pour cache et sessions WebSocket
- **RabbitMQ** pour notifications asynchrones
- **JWT** pour authentification
- **Swagger** pour documentation API

### **Frontend Angular 17 (Port 4204)**
- **WebSocket client** pour chat temps réel
- **PWA** avec notifications push
- **Interface responsive** avec Angular Material
- **State management** avec RxJS
- **Lazy loading** des modules
- **Service Worker** pour offline

### **Base de données**
- **PostgreSQL** : Messages, conversations, notifications
- **Redis** : Cache, sessions WebSocket, présence utilisateurs
- **Stockage fichiers** : MinIO ou AWS S3

## 📊 **Entités principales**

### 💬 **Conversation**
```typescript
{
  id: number,
  nom: string,
  type: 'PRIVE' | 'GROUPE' | 'EQUIPE',
  participants: Utilisateur[],
  createur: Utilisateur,
  dateCreation: Date,
  dernierMessage: Message,
  statut: 'ACTIF' | 'ARCHIVE'
}
```

### 📝 **Message**
```typescript
{
  id: number,
  conversationId: number,
  expediteur: Utilisateur,
  contenu: string,
  type: 'TEXTE' | 'FICHIER' | 'IMAGE' | 'SYSTEME',
  dateEnvoi: Date,
  statut: 'ENVOYE' | 'LIVRE' | 'LU',
  reponseA?: Message,
  fichierUrl?: string
}
```

### 🔔 **Notification**
```typescript
{
  id: number,
  destinataire: Utilisateur,
  titre: string,
  contenu: string,
  type: 'INFO' | 'ALERTE' | 'RAPPEL' | 'SYSTEME',
  canal: 'PUSH' | 'EMAIL' | 'SMS',
  dateCreation: Date,
  dateLecture?: Date,
  statut: 'EN_ATTENTE' | 'ENVOYE' | 'LU' | 'ERREUR'
}
```

### 🤖 **ChatbotConversation**
```typescript
{
  id: number,
  utilisateur: Utilisateur,
  sessionId: string,
  contexte: object,
  dateDebut: Date,
  dateFin?: Date,
  statut: 'ACTIF' | 'TERMINE'
}
```

## 🔌 **APIs REST**

### **Conversations**
- `GET /api/conversations` - Liste des conversations
- `POST /api/conversations` - Créer une conversation
- `GET /api/conversations/{id}/messages` - Messages d'une conversation
- `POST /api/conversations/{id}/messages` - Envoyer un message
- `PUT /api/conversations/{id}/participants` - Gérer les participants

### **Notifications**
- `GET /api/notifications` - Notifications utilisateur
- `POST /api/notifications` - Créer une notification
- `PUT /api/notifications/{id}/read` - Marquer comme lue
- `GET /api/notifications/preferences` - Préférences utilisateur

### **Chatbot**
- `POST /api/chatbot/message` - Envoyer un message au bot
- `GET /api/chatbot/session` - Informations de session
- `POST /api/chatbot/feedback` - Feedback sur les réponses

## 🌐 **WebSocket Events**

### **Chat temps réel**
```typescript
// Événements entrants
'message.send' -> Envoyer un message
'message.read' -> Marquer comme lu
'typing.start' -> Commencer à taper
'typing.stop' -> Arrêter de taper

// Événements sortants
'message.received' -> Nouveau message reçu
'message.status' -> Statut du message mis à jour
'user.typing' -> Utilisateur en train de taper
'user.online' -> Utilisateur en ligne
'user.offline' -> Utilisateur hors ligne
```

### **Notifications temps réel**
```typescript
'notification.new' -> Nouvelle notification
'notification.read' -> Notification lue
'notification.count' -> Nombre de notifications non lues
```

## 🚀 **Démarrage rapide**

### **Développement**
```bash
# Démarrer tous les services
docker-compose up -d

# Ou démarrage sélectif
docker-compose up -d communication-db communication-redis communication-backend communication-frontend
```

### **URLs d'accès**
- **Frontend** : http://localhost:4204
- **Backend API** : http://localhost:8084
- **Swagger** : http://localhost:8084/swagger-ui.html
- **WebSocket** : ws://localhost:8084/ws

### **Base de données**
- **PostgreSQL** : localhost:5436
- **Redis** : localhost:6380

## 🔧 **Configuration**

### **Variables d'environnement**
```env
# Base de données
POSTGRES_DB=communication_db
POSTGRES_USER=communication_user
POSTGRES_PASSWORD=communication_password

# Redis
REDIS_HOST=communication-redis
REDIS_PORT=6379

# JWT
JWT_SECRET=communication_jwt_secret
JWT_EXPIRATION=86400

# Notifications
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=email_password

# Chatbot
OPENAI_API_KEY=your_openai_key
CHATBOT_MODEL=gpt-3.5-turbo
```

## 📱 **Fonctionnalités avancées**

### **Chat**
- ✅ Messages en temps réel
- ✅ Indicateurs de frappe
- ✅ Statuts de lecture
- ✅ Conversations de groupe
- ✅ Recherche de messages
- ✅ Partage de fichiers
- ✅ Émojis et réactions
- ✅ Messages épinglés

### **Notifications**
- ✅ Push notifications PWA
- ✅ Notifications par email
- ✅ Templates personnalisables
- ✅ Planification de notifications
- ✅ Préférences utilisateur
- ✅ Historique complet
- ✅ Notifications de groupe

### **Chatbot**
- ✅ Réponses intelligentes
- ✅ Intégration avec les données SprintBot
- ✅ Commandes prédéfinies
- ✅ Apprentissage contextuel
- ✅ Support multilingue
- ✅ Escalade vers humain
- ✅ Analytics des conversations

## 🔗 **Intégration avec autres microservices**

### **Auth User Service**
- Authentification JWT
- Informations utilisateurs
- Gestion des rôles

### **Planning Performance Service**
- Notifications d'entraînements
- Rappels de performances
- Alertes de planning

### **Medical Admin Service**
- Notifications médicales
- Rappels de rendez-vous
- Alertes de santé

## 📊 **Monitoring et métriques**

### **Health Checks**
- `/actuator/health` - État général
- `/actuator/health/db` - Base de données
- `/actuator/health/redis` - Cache Redis
- `/actuator/health/websocket` - Connexions WebSocket

### **Métriques**
- Nombre de messages envoyés
- Utilisateurs connectés en temps réel
- Notifications délivrées
- Performance du chatbot
- Temps de réponse API

## 🧪 **Tests**

### **Tests unitaires**
```bash
# Backend
mvn test

# Frontend
npm test
```

### **Tests d'intégration**
```bash
# Script de test complet
./test-integration.sh

# Tests WebSocket
./test-websocket.sh

# Tests notifications
./test-notifications.sh
```

## 📚 **Documentation**

- [Guide d'intégration](./INTEGRATION.md)
- [API Documentation](http://localhost:8084/swagger-ui.html)
- [WebSocket Events](./WEBSOCKET_EVENTS.md)
- [Chatbot Configuration](./CHATBOT_CONFIG.md)
- [Déploiement](./DEPLOYMENT.md)

## 🤝 **Support**

- **Email** : <EMAIL>
- **Documentation** : [docs.sprintbot.com](https://docs.sprintbot.com)
- **Issues** : [GitHub Issues](https://github.com/sprintbot/issues)

---

**Communication Service** - Connecter votre équipe de volleyball 🏐💬
