package com.sprintbot.medicaladmin.controller;

import com.sprintbot.medicaladmin.entity.RendezVousMedical;
import com.sprintbot.medicaladmin.service.RendezVousMedicalService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Contrôleur REST pour la gestion des rendez-vous médicaux
 * Expose les APIs pour la planification et le suivi des consultations
 */
@RestController
@RequestMapping("/api/rendez-vous")
@Tag(name = "Rendez-vous Médicaux", description = "APIs pour la gestion des rendez-vous médicaux")
@CrossOrigin(origins = "*")
public class RendezVousMedicalController {

    private static final Logger logger = LoggerFactory.getLogger(RendezVousMedicalController.class);

    @Autowired
    private RendezVousMedicalService rendezVousService;

    // CRUD Operations
    @PostMapping
    @Operation(summary = "Créer un nouveau rendez-vous", description = "Planifie un nouveau rendez-vous médical")
    public ResponseEntity<RendezVousMedical> creerRendezVous(@Valid @RequestBody RendezVousMedical rendezVous) {
        logger.info("Création d'un nouveau rendez-vous pour le joueur ID: {}", rendezVous.getJoueurId());
        
        try {
            RendezVousMedical created = rendezVousService.creerRendezVous(rendezVous);
            return ResponseEntity.status(HttpStatus.CREATED).body(created);
        } catch (Exception e) {
            logger.error("Erreur lors de la création du rendez-vous: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "Obtenir un rendez-vous par ID", description = "Récupère les détails d'un rendez-vous spécifique")
    public ResponseEntity<RendezVousMedical> obtenirRendezVous(
            @Parameter(description = "ID du rendez-vous") @PathVariable Long id) {
        
        Optional<RendezVousMedical> rendezVous = rendezVousService.obtenirRendezVous(id);
        return rendezVous.map(ResponseEntity::ok)
                        .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping
    @Operation(summary = "Lister tous les rendez-vous", description = "Récupère la liste paginée de tous les rendez-vous")
    public ResponseEntity<Page<RendezVousMedical>> obtenirTousRendezVous(
            @Parameter(description = "Numéro de page (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Taille de la page") @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<RendezVousMedical> rendezVous = rendezVousService.obtenirRendezVousPagines(pageable);
        return ResponseEntity.ok(rendezVous);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Mettre à jour un rendez-vous", description = "Met à jour un rendez-vous existant")
    public ResponseEntity<RendezVousMedical> mettreAJourRendezVous(
            @Parameter(description = "ID du rendez-vous") @PathVariable Long id,
            @Valid @RequestBody RendezVousMedical rendezVous) {
        
        try {
            rendezVous.setId(id);
            RendezVousMedical updated = rendezVousService.mettreAJourRendezVous(rendezVous);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            logger.error("Erreur lors de la mise à jour du rendez-vous ID {}: {}", id, e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Supprimer un rendez-vous", description = "Supprime un rendez-vous")
    public ResponseEntity<Void> supprimerRendezVous(
            @Parameter(description = "ID du rendez-vous") @PathVariable Long id) {
        
        try {
            rendezVousService.supprimerRendezVous(id);
            return ResponseEntity.noContent().build();
        } catch (RuntimeException e) {
            logger.error("Erreur lors de la suppression du rendez-vous ID {}: {}", id, e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    // Gestion des statuts
    @PutMapping("/{id}/confirmer")
    @Operation(summary = "Confirmer un rendez-vous", description = "Confirme un rendez-vous planifié")
    public ResponseEntity<RendezVousMedical> confirmerRendezVous(
            @Parameter(description = "ID du rendez-vous") @PathVariable Long id) {
        
        try {
            RendezVousMedical updated = rendezVousService.confirmerRendezVous(id);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            logger.error("Erreur lors de la confirmation du rendez-vous ID {}: {}", id, e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    @PutMapping("/{id}/commencer")
    @Operation(summary = "Commencer un rendez-vous", description = "Démarre un rendez-vous confirmé")
    public ResponseEntity<RendezVousMedical> commencerRendezVous(
            @Parameter(description = "ID du rendez-vous") @PathVariable Long id) {
        
        try {
            RendezVousMedical updated = rendezVousService.commencerRendezVous(id);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            logger.error("Erreur lors du début du rendez-vous ID {}: {}", id, e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    @PutMapping("/{id}/terminer")
    @Operation(summary = "Terminer un rendez-vous", description = "Termine un rendez-vous en cours avec compte-rendu")
    public ResponseEntity<RendezVousMedical> terminerRendezVous(
            @Parameter(description = "ID du rendez-vous") @PathVariable Long id,
            @RequestBody Map<String, String> details) {
        
        try {
            String compteRendu = details.get("compteRendu");
            String prescriptions = details.get("prescriptions");
            RendezVousMedical updated = rendezVousService.terminerRendezVous(id, compteRendu, prescriptions);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            logger.error("Erreur lors de la fin du rendez-vous ID {}: {}", id, e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    @PutMapping("/{id}/annuler")
    @Operation(summary = "Annuler un rendez-vous", description = "Annule un rendez-vous")
    public ResponseEntity<RendezVousMedical> annulerRendezVous(
            @Parameter(description = "ID du rendez-vous") @PathVariable Long id) {
        
        try {
            RendezVousMedical updated = rendezVousService.annulerRendezVous(id);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            logger.error("Erreur lors de l'annulation du rendez-vous ID {}: {}", id, e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    @PutMapping("/{id}/reporter")
    @Operation(summary = "Reporter un rendez-vous", description = "Reporte un rendez-vous à une nouvelle date/heure")
    public ResponseEntity<RendezVousMedical> reporterRendezVous(
            @Parameter(description = "ID du rendez-vous") @PathVariable Long id,
            @RequestBody Map<String, String> nouvelleDateTime) {
        
        try {
            LocalDate nouvelleDate = LocalDate.parse(nouvelleDateTime.get("date"));
            LocalTime nouvelleHeure = LocalTime.parse(nouvelleDateTime.get("heure"));
            RendezVousMedical updated = rendezVousService.reporterRendezVous(id, nouvelleDate, nouvelleHeure);
            return ResponseEntity.ok(updated);
        } catch (Exception e) {
            logger.error("Erreur lors du report du rendez-vous ID {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    // Recherches par joueur
    @GetMapping("/joueur/{joueurId}")
    @Operation(summary = "Obtenir les rendez-vous d'un joueur", description = "Récupère l'historique des rendez-vous d'un joueur")
    public ResponseEntity<List<RendezVousMedical>> obtenirRendezVousParJoueur(
            @Parameter(description = "ID du joueur") @PathVariable Long joueurId) {
        
        List<RendezVousMedical> rendezVous = rendezVousService.obtenirRendezVousParJoueur(joueurId);
        return ResponseEntity.ok(rendezVous);
    }

    @GetMapping("/joueur/{joueurId}/prochain")
    @Operation(summary = "Obtenir le prochain rendez-vous d'un joueur", description = "Récupère le prochain rendez-vous planifié d'un joueur")
    public ResponseEntity<RendezVousMedical> obtenirProchainRendezVousJoueur(
            @Parameter(description = "ID du joueur") @PathVariable Long joueurId) {
        
        Optional<RendezVousMedical> rendezVous = rendezVousService.obtenirProchainRendezVousJoueur(joueurId);
        return rendezVous.map(ResponseEntity::ok)
                        .orElse(ResponseEntity.notFound().build());
    }

    // Recherches par staff médical
    @GetMapping("/staff/{staffMedicalId}")
    @Operation(summary = "Obtenir les rendez-vous d'un staff médical", description = "Récupère les rendez-vous gérés par un staff médical")
    public ResponseEntity<List<RendezVousMedical>> obtenirRendezVousParStaffMedical(
            @Parameter(description = "ID du staff médical") @PathVariable Long staffMedicalId) {
        
        List<RendezVousMedical> rendezVous = rendezVousService.obtenirRendezVousParStaffMedical(staffMedicalId);
        return ResponseEntity.ok(rendezVous);
    }

    // Planning et calendrier
    @GetMapping("/aujourd-hui")
    @Operation(summary = "Obtenir les rendez-vous d'aujourd'hui", description = "Récupère tous les rendez-vous d'aujourd'hui")
    public ResponseEntity<List<RendezVousMedical>> obtenirRendezVousAujourdhui() {
        List<RendezVousMedical> rendezVous = rendezVousService.obtenirRendezVousAujourdhui();
        return ResponseEntity.ok(rendezVous);
    }

    @GetMapping("/aujourd-hui/staff/{staffMedicalId}")
    @Operation(summary = "Obtenir les rendez-vous d'aujourd'hui pour un staff", description = "Récupère les rendez-vous d'aujourd'hui d'un staff médical")
    public ResponseEntity<List<RendezVousMedical>> obtenirRendezVousAujourdhuiParStaff(
            @Parameter(description = "ID du staff médical") @PathVariable Long staffMedicalId) {
        
        List<RendezVousMedical> rendezVous = rendezVousService.obtenirRendezVousAujourdhuiParStaff(staffMedicalId);
        return ResponseEntity.ok(rendezVous);
    }

    @GetMapping("/demain")
    @Operation(summary = "Obtenir les rendez-vous de demain", description = "Récupère tous les rendez-vous de demain")
    public ResponseEntity<List<RendezVousMedical>> obtenirRendezVousDemain() {
        List<RendezVousMedical> rendezVous = rendezVousService.obtenirRendezVousDemain();
        return ResponseEntity.ok(rendezVous);
    }

    @GetMapping("/semaine-prochaine")
    @Operation(summary = "Obtenir les rendez-vous de la semaine prochaine", description = "Récupère les rendez-vous des 7 prochains jours")
    public ResponseEntity<List<RendezVousMedical>> obtenirRendezVousSemaineProchaine() {
        List<RendezVousMedical> rendezVous = rendezVousService.obtenirRendezVousSemaineProchaine();
        return ResponseEntity.ok(rendezVous);
    }

    @GetMapping("/planning/staff/{staffMedicalId}")
    @Operation(summary = "Obtenir le planning d'un staff médical", description = "Récupère le planning d'un staff médical sur une période")
    public ResponseEntity<List<RendezVousMedical>> obtenirPlanningStaffMedical(
            @Parameter(description = "ID du staff médical") @PathVariable Long staffMedicalId,
            @Parameter(description = "Date de début") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateDebut,
            @Parameter(description = "Date de fin") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateFin) {
        
        List<RendezVousMedical> planning = rendezVousService.obtenirPlanningStaffMedical(staffMedicalId, dateDebut, dateFin);
        return ResponseEntity.ok(planning);
    }

    @GetMapping("/planning/joueur/{joueurId}")
    @Operation(summary = "Obtenir le planning d'un joueur", description = "Récupère le planning médical d'un joueur sur une période")
    public ResponseEntity<List<RendezVousMedical>> obtenirPlanningJoueur(
            @Parameter(description = "ID du joueur") @PathVariable Long joueurId,
            @Parameter(description = "Date de début") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateDebut,
            @Parameter(description = "Date de fin") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateFin) {
        
        List<RendezVousMedical> planning = rendezVousService.obtenirPlanningJoueur(joueurId, dateDebut, dateFin);
        return ResponseEntity.ok(planning);
    }

    // Gestion des rappels
    @GetMapping("/rappels")
    @Operation(summary = "Obtenir les rendez-vous nécessitant un rappel", description = "Récupère les rendez-vous nécessitant l'envoi d'un rappel")
    public ResponseEntity<List<RendezVousMedical>> obtenirRendezVousNecessitantRappel() {
        List<RendezVousMedical> rendezVous = rendezVousService.obtenirRendezVousNecessitantRappel();
        return ResponseEntity.ok(rendezVous);
    }

    @PutMapping("/{id}/rappel-envoye")
    @Operation(summary = "Marquer le rappel comme envoyé", description = "Marque qu'un rappel a été envoyé pour ce rendez-vous")
    public ResponseEntity<RendezVousMedical> marquerRappelEnvoye(
            @Parameter(description = "ID du rendez-vous") @PathVariable Long id) {
        
        try {
            RendezVousMedical updated = rendezVousService.marquerRappelEnvoye(id);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            logger.error("Erreur lors du marquage du rappel pour le rendez-vous ID {}: {}", id, e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    // Recherches par statut et type
    @GetMapping("/statut/{statut}")
    @Operation(summary = "Obtenir les rendez-vous par statut", description = "Récupère les rendez-vous par statut")
    public ResponseEntity<List<RendezVousMedical>> obtenirRendezVousParStatut(
            @Parameter(description = "Statut") @PathVariable String statut) {
        
        List<RendezVousMedical> rendezVous = rendezVousService.obtenirRendezVousParStatut(statut);
        return ResponseEntity.ok(rendezVous);
    }

    @GetMapping("/type/{typeRendezVous}")
    @Operation(summary = "Obtenir les rendez-vous par type", description = "Récupère les rendez-vous par type")
    public ResponseEntity<List<RendezVousMedical>> obtenirRendezVousParType(
            @Parameter(description = "Type de rendez-vous") @PathVariable String typeRendezVous) {
        
        List<RendezVousMedical> rendezVous = rendezVousService.obtenirRendezVousParType(typeRendezVous);
        return ResponseEntity.ok(rendezVous);
    }

    @GetMapping("/urgents")
    @Operation(summary = "Obtenir les rendez-vous urgents", description = "Récupère les rendez-vous avec priorité élevée ou urgente")
    public ResponseEntity<List<RendezVousMedical>> obtenirRendezVousUrgents() {
        List<RendezVousMedical> rendezVous = rendezVousService.obtenirRendezVousUrgents();
        return ResponseEntity.ok(rendezVous);
    }

    // Vérification de disponibilité
    @GetMapping("/disponibilite/staff/{staffMedicalId}")
    @Operation(summary = "Vérifier la disponibilité d'un staff", description = "Vérifie si un staff médical est disponible sur un créneau")
    public ResponseEntity<Boolean> verifierDisponibiliteStaff(
            @Parameter(description = "ID du staff médical") @PathVariable Long staffMedicalId,
            @Parameter(description = "Date") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @Parameter(description = "Heure de début") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.TIME) LocalTime heureDebut,
            @Parameter(description = "Heure de fin") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.TIME) LocalTime heureFin) {
        
        boolean disponible = rendezVousService.verifierDisponibiliteStaff(staffMedicalId, date, heureDebut, heureFin);
        return ResponseEntity.ok(disponible);
    }

    @GetMapping("/disponibilite/joueur/{joueurId}")
    @Operation(summary = "Vérifier la disponibilité d'un joueur", description = "Vérifie si un joueur est disponible sur un créneau")
    public ResponseEntity<Boolean> verifierDisponibiliteJoueur(
            @Parameter(description = "ID du joueur") @PathVariable Long joueurId,
            @Parameter(description = "Date") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @Parameter(description = "Heure de début") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.TIME) LocalTime heureDebut,
            @Parameter(description = "Heure de fin") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.TIME) LocalTime heureFin) {
        
        boolean disponible = rendezVousService.verifierDisponibiliteJoueur(joueurId, date, heureDebut, heureFin);
        return ResponseEntity.ok(disponible);
    }

    // Statistiques
    @GetMapping("/statistiques/joueur/{joueurId}/count")
    @Operation(summary = "Compter les rendez-vous par joueur", description = "Compte le nombre de rendez-vous d'un joueur")
    public ResponseEntity<Long> compterRendezVousParJoueur(
            @Parameter(description = "ID du joueur") @PathVariable Long joueurId) {
        
        long count = rendezVousService.compterRendezVousParJoueur(joueurId);
        return ResponseEntity.ok(count);
    }

    @GetMapping("/statistiques/aujourd-hui")
    @Operation(summary = "Compter les rendez-vous d'aujourd'hui", description = "Compte le nombre de rendez-vous d'aujourd'hui")
    public ResponseEntity<Long> compterRendezVousAujourdhui() {
        long count = rendezVousService.compterRendezVousAujourdhui();
        return ResponseEntity.ok(count);
    }

    @GetMapping("/statistiques/demain")
    @Operation(summary = "Compter les rendez-vous de demain", description = "Compte le nombre de rendez-vous de demain")
    public ResponseEntity<Long> compterRendezVousDemain() {
        long count = rendezVousService.compterRendezVousDemain();
        return ResponseEntity.ok(count);
    }

    // Recherche avec filtres
    @GetMapping("/recherche")
    @Operation(summary = "Rechercher avec filtres", description = "Recherche les rendez-vous avec des filtres multiples")
    public ResponseEntity<Page<RendezVousMedical>> rechercherAvecFiltres(
            @RequestParam(required = false) Long joueurId,
            @RequestParam(required = false) Long staffMedicalId,
            @RequestParam(required = false) String typeRendezVous,
            @RequestParam(required = false) String statut,
            @RequestParam(required = false) String priorite,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateDebut,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateFin,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<RendezVousMedical> rendezVous = rendezVousService.rechercherAvecFiltres(
                joueurId, staffMedicalId, typeRendezVous, statut, priorite, dateDebut, dateFin, pageable);
        return ResponseEntity.ok(rendezVous);
    }

    @GetMapping("/recherche/texte")
    @Operation(summary = "Recherche textuelle", description = "Recherche les rendez-vous par texte libre")
    public ResponseEntity<List<RendezVousMedical>> rechercherParTexte(
            @Parameter(description = "Terme de recherche") @RequestParam String searchTerm) {
        
        List<RendezVousMedical> rendezVous = rendezVousService.rechercherParTexte(searchTerm);
        return ResponseEntity.ok(rendezVous);
    }

    // Alertes
    @GetMapping("/alertes/echus")
    @Operation(summary = "Obtenir les rendez-vous échus", description = "Récupère les rendez-vous passés non terminés")
    public ResponseEntity<List<RendezVousMedical>> obtenirRendezVousEchus() {
        List<RendezVousMedical> rendezVous = rendezVousService.obtenirRendezVousEchus();
        return ResponseEntity.ok(rendezVous);
    }

    @GetMapping("/alertes/prochaines-visites-echues")
    @Operation(summary = "Obtenir les prochaines visites échues", description = "Récupère les prochaines visites prévues échues")
    public ResponseEntity<List<RendezVousMedical>> obtenirProchainesVisitesEchues() {
        List<RendezVousMedical> rendezVous = rendezVousService.obtenirProchainesVisitesEchues();
        return ResponseEntity.ok(rendezVous);
    }
}
