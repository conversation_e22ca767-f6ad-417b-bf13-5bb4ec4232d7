/* Layout principal */
.main-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

/* Header */
.header {
  height: 60px;
  background: white;
  color: #2c3e50;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  position: relative;
  z-index: 1000;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-logo {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.header-title {
  font-size: 24px;
  font-weight: bold;
  letter-spacing: 1px;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-icon {
  font-size: 20px;
}

.user-name {
  font-weight: 500;
  margin-right: 15px;
}

.btn-logout {
  background: #667eea;
  border: 1px solid #667eea;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.btn-logout:hover {
  background: #5a6fd8;
  border-color: #5a6fd8;
  transform: translateY(-1px);
}

/* Container principal */
.main-container {
  flex: 1;
  display: flex;
  overflow: hidden;
  min-height: 0;
}

/* Sidebar */
.sidebar {
  width: 60px;
  background: #667eea;
  color: white;
  overflow-y: auto;
  box-shadow: 2px 0 10px rgba(0,0,0,0.1);
  transition: width 0.3s ease;
  flex-shrink: 0;
}

.sidebar:hover {
  width: 250px;
}

.nav-menu {
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.nav-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 18px 0 18px 18px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 0;
  position: relative;
  width: 100%;
  height: 60px;
}

.sidebar:hover .nav-item {
  justify-content: flex-start;
  padding: 18px 20px;
}

.nav-item:hover {
  background: rgba(64, 70, 75, 0.3);
}

.nav-item.active {
  background: #98ceea;
  border-right: 3px solid #2980b9;
}

.nav-icon {
  font-size: 22px;
  width: 24px;
  text-align: center;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.nav-text {
  font-size: 15px;
  font-weight: 500;
  margin-left: 15px;
  opacity: 0;
  transition: opacity 0.3s ease;
  white-space: nowrap;
  overflow: hidden;
  color: white;
}

.sidebar:hover .nav-text {
  opacity: 1;
}

/* Zone de contenu */
.content {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
  background: #f8fafc;
}

.module-content {
  max-width: 1200px;
  margin: 0 auto;
}

/* Section de bienvenue */
.welcome-section {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.welcome-header {
  display: flex;
  align-items: center;
  gap: 20px;
}

.welcome-logo {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

.welcome-text h1 {
  color: #2c3e50;
  font-size: 32px;
  margin-bottom: 10px;
  font-weight: 600;
}

.welcome-subtitle {
  color: #7f8c8d;
  font-size: 18px;
  margin: 0;
}

/* Dashboard Section */
.dashboard-section {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.section-title {
  color: #667eea;
  font-size: 24px;
  margin-bottom: 10px;
  font-weight: 600;
}

.section-subtitle {
  color: #7f8c8d;
  font-size: 16px;
  margin-bottom: 30px;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 30px;
}

.dashboard-card {
  background: #f8fafc;
  padding: 25px;
  border-radius: 10px;
  border: 1px solid #e2e8f0;
}

.dashboard-card h3 {
  color: #2c3e50;
  font-size: 18px;
  margin-bottom: 20px;
  font-weight: 600;
}

.event-placeholder {
  text-align: center;
  padding: 40px 20px;
}

.calendar-icon {
  font-size: 48px;
  display: block;
  margin-bottom: 15px;
  opacity: 0.5;
}

.event-placeholder p {
  color: #7f8c8d;
  margin-bottom: 15px;
}

.link-calendar {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  cursor: pointer;
}

.link-calendar:hover {
  text-decoration: underline;
}

.classement-section {
  background: #f8fafc;
  padding: 25px;
  border-radius: 10px;
  border: 1px solid #e2e8f0;
}

.classement-section h3 {
  color: #2c3e50;
  font-size: 18px;
  margin-bottom: 20px;
  font-weight: 600;
}

.classement-placeholder {
  text-align: center;
  padding: 40px 20px;
}

.classement-icon {
  font-size: 48px;
  display: block;
  margin-bottom: 15px;
  opacity: 0.5;
}

.classement-placeholder p {
  color: #7f8c8d;
  margin: 0;
}

/* Cartes du dashboard */
.dashboard-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
  margin-top: 30px;
}

.card {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  border: 2px solid transparent;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
  border-color: #3498db;
}

.card-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.card h3 {
  color: #2c3e50;
  font-size: 20px;
  margin-bottom: 10px;
  font-weight: 600;
}

.card p {
  color: #7f8c8d;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

/* Contenu des modules */
.module-content h2 {
  color: #2c3e50;
  font-size: 28px;
  margin-bottom: 25px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.placeholder-content {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  margin-top: 20px;
}

/* Cartes d'événements */
.event-card, .absence-card, .cours-card {
  background: #f8fafc;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 15px;
  border-left: 4px solid #3498db;
}

.event-card h4, .absence-card h4, .cours-card h4 {
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 16px;
}

.event-card p, .absence-card p, .cours-card p {
  color: #7f8c8d;
  margin: 0;
  font-size: 14px;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    width: 200px;
  }
  
  .content {
    padding: 20px;
  }
  
  .dashboard-cards {
    grid-template-columns: 1fr;
  }
  
  .header-title {
    display: none;
  }
}

@media (max-width: 480px) {
  .sidebar {
    position: fixed;
    left: -250px;
    z-index: 999;
    transition: left 0.3s ease;
  }
  
  .sidebar.open {
    left: 0;
  }
}

/* Planning Module Styles */
.planning-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 10px;
}

.tab-btn {
  padding: 10px 20px;
  border: none;
  background: #f8f9fa;
  color: #6c757d;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.tab-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.tab-btn.active {
  background: #007bff;
  color: white;
}

.tab-content {
  margin-top: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  color: #2c3e50;
}

.btn-primary {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.3s ease;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.3s ease;
}

.btn-secondary:hover {
  background: #545b62;
}

.entrainements-list {
  display: grid;
  gap: 15px;
}

.entrainement-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  border-left: 4px solid #007bff;
}

.entrainement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.entrainement-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
}

.entrainement-type {
  background: #007bff;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.entrainement-details {
  color: #6c757d;
}

.entrainement-details p {
  margin: 5px 0;
  font-size: 14px;
}

.entrainement-details strong {
  color: #495057;
}
