events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # Basic Settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # Gzip Settings
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # SprintBot Volleyball Platform - Reverse Proxy Configuration
    
    # Auth User Service
    server {
        listen 80;
        server_name auth.localhost;
        
        location / {
            proxy_pass http://auth-user-frontend:8080;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /api/ {
            proxy_pass http://auth-user-backend:8081;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # Planning Performance Service
    server {
        listen 80;
        server_name planning.localhost;
        
        location / {
            proxy_pass http://planning-performance-frontend:8080;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /api/ {
            proxy_pass http://planning-performance-backend:8082;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # Medical Admin Service
    server {
        listen 80;
        server_name medical.localhost;
        
        location / {
            proxy_pass http://medical-admin-frontend:8080;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /api/ {
            proxy_pass http://medical-admin-backend:8083;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # Communication Service
    server {
        listen 80;
        server_name communication.localhost;
        
        location / {
            proxy_pass http://communication-frontend:8080;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /api/ {
            proxy_pass http://communication-backend:8084;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # WebSocket support
        location /ws {
            proxy_pass http://communication-backend:8084;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # Finance Service
    server {
        listen 80;
        server_name finance.localhost;
        
        location / {
            proxy_pass http://finance-frontend:8080;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /api/ {
            proxy_pass http://finance-backend:8085;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # Gateway Service (API Gateway principal)
    server {
        listen 80;
        server_name gateway.localhost api.localhost;
        
        location / {
            proxy_pass http://gateway-service:8080;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # Discovery Service (Eureka Dashboard)
    server {
        listen 80;
        server_name discovery.localhost eureka.localhost;
        
        location / {
            proxy_pass http://discovery-service:8761;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # Default server (redirect to main services)
    server {
        listen 80 default_server;
        server_name localhost _;
        
        location / {
            return 200 '
<!DOCTYPE html>
<html>
<head>
    <title>🏐 SprintBot - Volleyball Platform</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; text-align: center; }
        .services { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 30px; }
        .service { padding: 20px; border: 1px solid #ddd; border-radius: 8px; text-align: center; }
        .service h3 { color: #3498db; margin-bottom: 10px; }
        .service a { display: inline-block; margin: 5px; padding: 8px 16px; background: #3498db; color: white; text-decoration: none; border-radius: 4px; }
        .service a:hover { background: #2980b9; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏐 SprintBot - Plateforme Intelligente de Volley-Ball</h1>
        <p style="text-align: center; color: #666;">Bienvenue sur la plateforme de gestion d\'équipe de volley-ball</p>
        
        <div class="services">
            <div class="service">
                <h3>🔐 Authentification</h3>
                <a href="http://auth.localhost">Interface</a>
                <a href="http://localhost:4201">Direct</a>
            </div>
            <div class="service">
                <h3>📅 Planning & Performance</h3>
                <a href="http://planning.localhost">Interface</a>
                <a href="http://localhost:4202">Direct</a>
            </div>
            <div class="service">
                <h3>🏥 Médical & Admin</h3>
                <a href="http://medical.localhost">Interface</a>
                <a href="http://localhost:4203">Direct</a>
            </div>
            <div class="service">
                <h3>💬 Communication</h3>
                <a href="http://communication.localhost">Interface</a>
                <a href="http://localhost:4204">Direct</a>
            </div>
            <div class="service">
                <h3>💰 Finance</h3>
                <a href="http://finance.localhost">Interface</a>
                <a href="http://localhost:4205">Direct</a>
            </div>
            <div class="service">
                <h3>🌐 API Gateway</h3>
                <a href="http://gateway.localhost">Gateway</a>
                <a href="http://localhost:8081">Direct</a>
            </div>
            <div class="service">
                <h3>🔍 Service Discovery</h3>
                <a href="http://discovery.localhost">Eureka</a>
                <a href="http://localhost:8761">Direct</a>
            </div>
        </div>
    </div>
</body>
</html>';
            add_header Content-Type text/html;
        }
    }
}
