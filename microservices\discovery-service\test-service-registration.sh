#!/bin/bash

# Script de test de registration des services pour Discovery Service
# SprintBot - Test de découverte de services avec Eureka

set -e

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
EUREKA_URL="http://localhost:8761"
MAX_WAIT_TIME=60

# Services SprintBot attendus
EXPECTED_SERVICES=(
    "auth-user-service"
    "planning-performance-service"
    "medical-admin-service"
    "communication-service"
    "finance-service"
    "gateway-service"
)

# Fonctions utilitaires
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Fonction d'affichage du banner
show_banner() {
    echo ""
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║            🔍 TEST SERVICE REGISTRATION                      ║"
    echo "║                 Discovery Service (Eureka)                  ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo ""
}

# Vérifier la disponibilité d'Eureka
check_eureka_availability() {
    log_info "🔍 Vérification de la disponibilité d'Eureka..."
    
    local count=0
    while [ $count -lt $MAX_WAIT_TIME ]; do
        if curl -s -f "$EUREKA_URL/actuator/health" > /dev/null 2>&1; then
            log_success "✅ Eureka Server disponible"
            return 0
        fi
        
        echo -n "."
        sleep 1
        count=$((count + 1))
    done
    
    log_error "❌ Eureka Server non disponible après ${MAX_WAIT_TIME}s"
    return 1
}

# Récupérer la liste des services enregistrés
get_registered_services() {
    log_info "📋 Récupération de la liste des services enregistrés..."
    
    local response=$(curl -s "$EUREKA_URL/eureka/apps" -H "Accept: application/json" 2>/dev/null)
    
    if [ $? -eq 0 ] && [ -n "$response" ]; then
        echo "$response"
        return 0
    else
        log_error "❌ Impossible de récupérer la liste des services"
        return 1
    fi
}

# Analyser les services enregistrés
analyze_registered_services() {
    local services_json="$1"
    
    log_info "🔍 Analyse des services enregistrés..."
    
    # Extraire les noms des applications (nécessite jq si disponible)
    if command -v jq &> /dev/null; then
        local registered_apps=$(echo "$services_json" | jq -r '.applications.application[]?.name // empty' 2>/dev/null | sort -u)
        
        if [ -n "$registered_apps" ]; then
            log_info "📋 Services actuellement enregistrés:"
            while IFS= read -r app; do
                if [ -n "$app" ]; then
                    log_success "   ✅ $app"
                    
                    # Obtenir les détails de l'instance
                    local instances=$(echo "$services_json" | jq -r ".applications.application[] | select(.name==\"$app\") | .instance[]? | \"\(.hostName):\(.port.\"$\") (\(.status))\"" 2>/dev/null)
                    while IFS= read -r instance; do
                        if [ -n "$instance" ]; then
                            log_info "      └─ Instance: $instance"
                        fi
                    done <<< "$instances"
                fi
            done <<< "$registered_apps"
            
            # Vérifier les services attendus
            check_expected_services "$registered_apps"
        else
            log_warning "⚠️ Aucun service enregistré trouvé"
        fi
    else
        # Analyse basique sans jq
        log_warning "⚠️ jq non disponible, analyse basique..."
        
        if echo "$services_json" | grep -q '"application"'; then
            log_info "📋 Des services sont enregistrés (analyse détaillée nécessite jq)"
            
            # Recherche basique des services SprintBot
            for service in "${EXPECTED_SERVICES[@]}"; do
                if echo "$services_json" | grep -q "\"$service\""; then
                    log_success "   ✅ $service trouvé"
                else
                    log_warning "   ⚠️ $service non trouvé"
                fi
            done
        else
            log_warning "⚠️ Aucun service enregistré"
        fi
    fi
}

# Vérifier les services attendus
check_expected_services() {
    local registered_apps="$1"
    
    log_info "🎯 Vérification des services SprintBot attendus..."
    
    local found_services=0
    local total_services=${#EXPECTED_SERVICES[@]}
    
    for service in "${EXPECTED_SERVICES[@]}"; do
        if echo "$registered_apps" | grep -q "^$service$"; then
            log_success "   ✅ $service"
            ((found_services++))
        else
            log_warning "   ⚠️ $service (non enregistré)"
        fi
    done
    
    echo ""
    log_info "📊 Résumé: $found_services/$total_services services SprintBot enregistrés"
    
    if [ $found_services -eq $total_services ]; then
        log_success "🎉 Tous les services SprintBot sont enregistrés!"
        return 0
    elif [ $found_services -gt 0 ]; then
        log_warning "⚠️ Certains services SprintBot ne sont pas encore enregistrés"
        return 1
    else
        log_warning "⚠️ Aucun service SprintBot n'est enregistré"
        return 1
    fi
}

# Tester l'enregistrement d'un service fictif
test_service_registration() {
    log_info "🧪 Test d'enregistrement d'un service fictif..."
    
    local test_service="test-service-$(date +%s)"
    local test_instance_id="test-instance-$(date +%s)"
    local test_port=9999
    
    # Données d'enregistrement
    local registration_data=$(cat <<EOF
{
    "instance": {
        "instanceId": "$test_instance_id",
        "hostName": "localhost",
        "app": "$test_service",
        "ipAddr": "127.0.0.1",
        "status": "UP",
        "overriddenstatus": "UNKNOWN",
        "port": {
            "\$": $test_port,
            "@enabled": "true"
        },
        "securePort": {
            "\$": 443,
            "@enabled": "false"
        },
        "countryId": 1,
        "dataCenterInfo": {
            "@class": "com.netflix.appinfo.InstanceInfo\$DefaultDataCenterInfo",
            "name": "MyOwn"
        },
        "leaseInfo": {
            "renewalIntervalInSecs": 30,
            "durationInSecs": 90
        },
        "metadata": {
            "management.port": "$test_port"
        },
        "homePageUrl": "http://localhost:$test_port/",
        "statusPageUrl": "http://localhost:$test_port/actuator/info",
        "healthCheckUrl": "http://localhost:$test_port/actuator/health"
    }
}
EOF
)
    
    # Tentative d'enregistrement
    local response=$(curl -s -w "%{http_code}" -X POST \
        "$EUREKA_URL/eureka/apps/$test_service" \
        -H "Content-Type: application/json" \
        -d "$registration_data" 2>/dev/null)
    
    local http_code="${response: -3}"
    
    if [ "$http_code" = "204" ]; then
        log_success "✅ Enregistrement du service test réussi"
        
        # Attendre un peu puis vérifier l'enregistrement
        sleep 5
        
        local check_response=$(curl -s "$EUREKA_URL/eureka/apps/$test_service" 2>/dev/null)
        if echo "$check_response" | grep -q "$test_service"; then
            log_success "✅ Service test visible dans le registry"
            
            # Nettoyer - désenregistrer le service test
            local delete_response=$(curl -s -w "%{http_code}" -X DELETE \
                "$EUREKA_URL/eureka/apps/$test_service/$test_instance_id" 2>/dev/null)
            
            local delete_code="${delete_response: -3}"
            if [ "$delete_code" = "200" ]; then
                log_success "✅ Service test désenregistré avec succès"
            else
                log_warning "⚠️ Échec du désenregistrement du service test"
            fi
        else
            log_warning "⚠️ Service test non visible dans le registry"
        fi
    else
        log_error "❌ Échec de l'enregistrement du service test (HTTP $http_code)"
        return 1
    fi
    
    return 0
}

# Afficher les statistiques Eureka
show_eureka_stats() {
    log_info "📊 Statistiques Eureka..."
    
    # Récupérer les métriques si disponibles
    local metrics_response=$(curl -s "$EUREKA_URL/actuator/metrics" 2>/dev/null)
    
    if [ $? -eq 0 ] && [ -n "$metrics_response" ]; then
        log_info "📈 Métriques disponibles:"
        
        # Rechercher les métriques liées à Eureka
        if command -v jq &> /dev/null; then
            local eureka_metrics=$(echo "$metrics_response" | jq -r '.names[] | select(contains("eureka"))' 2>/dev/null)
            if [ -n "$eureka_metrics" ]; then
                while IFS= read -r metric; do
                    if [ -n "$metric" ]; then
                        log_info "   - $metric"
                    fi
                done <<< "$eureka_metrics"
            else
                log_info "   - Métriques Eureka spécifiques non trouvées"
            fi
        else
            log_info "   - Analyse détaillée nécessite jq"
        fi
    else
        log_warning "⚠️ Métriques non disponibles"
    fi
    
    # Afficher le statut général
    local status_response=$(curl -s "$EUREKA_URL/eureka/status" 2>/dev/null)
    if [ $? -eq 0 ] && [ -n "$status_response" ]; then
        log_info "🔍 Statut Eureka Server disponible"
    else
        log_warning "⚠️ Statut Eureka Server non disponible"
    fi
}

# Afficher l'aide
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help              Afficher cette aide"
    echo "  -u, --url URL           URL d'Eureka (défaut: $EUREKA_URL)"
    echo "  -w, --wait SECONDS      Temps d'attente max (défaut: $MAX_WAIT_TIME)"
    echo "  -t, --test              Inclure le test d'enregistrement fictif"
    echo "  -s, --stats             Afficher les statistiques détaillées"
    echo ""
    echo "Exemples:"
    echo "  $0                      Test standard"
    echo "  $0 --test               Test avec enregistrement fictif"
    echo "  $0 --stats              Test avec statistiques détaillées"
}

# Fonction principale
main() {
    local include_test=false
    local include_stats=false
    
    # Traitement des arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -u|--url)
                EUREKA_URL="$2"
                shift 2
                ;;
            -w|--wait)
                MAX_WAIT_TIME="$2"
                shift 2
                ;;
            -t|--test)
                include_test=true
                shift
                ;;
            -s|--stats)
                include_stats=true
                shift
                ;;
            *)
                log_error "Option inconnue: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    show_banner
    
    log_info "🔍 Test de registration des services"
    log_info "URL Eureka: $EUREKA_URL"
    echo ""
    
    # Exécution des tests
    check_eureka_availability || exit 1
    
    local services_json=$(get_registered_services)
    if [ $? -eq 0 ]; then
        analyze_registered_services "$services_json"
    else
        exit 1
    fi
    
    if [ "$include_test" = true ]; then
        echo ""
        test_service_registration
    fi
    
    if [ "$include_stats" = true ]; then
        echo ""
        show_eureka_stats
    fi
    
    echo ""
    log_success "🎉 Test de registration terminé!"
    
    echo ""
    log_info "💡 Pour voir le dashboard Eureka:"
    log_info "   Ouvrir: $EUREKA_URL"
    log_info "   Utilisateur: admin / Mot de passe: admin123"
}

# Exécution du script
main "$@"
