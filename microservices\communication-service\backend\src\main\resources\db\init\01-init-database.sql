-- Script d'initialisation de la base de données pour le service de communication
-- Auteur: SprintBot Team
-- Version: 1.0.0

-- Création de la base de données si elle n'existe pas
SELECT 'CREATE DATABASE communication_db'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'communication_db')\gexec

-- Connexion à la base de données
\c communication_db;

-- Création du schéma communication
CREATE SCHEMA IF NOT EXISTS communication;

-- Définir le schéma par défaut
SET search_path TO communication, public;

-- Création de l'utilisateur pour l'application si nécessaire
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'communication_user') THEN
        CREATE ROLE communication_user WITH LOGIN PASSWORD 'communication_password';
    END IF;
END
$$;

-- Accorder les privilèges
GRANT USAGE ON SCHEMA communication TO communication_user;
GRANT CREATE ON SCHEMA communication TO communication_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA communication TO communication_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA communication TO communication_user;

-- Privilèges par défaut pour les futurs objets
ALTER DEFAULT PRIVILEGES IN SCHEMA communication GRANT ALL ON TABLES TO communication_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA communication GRANT ALL ON SEQUENCES TO communication_user;

-- Extensions nécessaires
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "unaccent";

-- Configuration pour les recherches textuelles
CREATE TEXT SEARCH CONFIGURATION IF NOT EXISTS french_unaccent (COPY = french);
ALTER TEXT SEARCH CONFIGURATION french_unaccent
    ALTER MAPPING FOR hword, hword_part, word
    WITH unaccent, french_stem;

-- Fonction utilitaire pour générer des UUIDs
CREATE OR REPLACE FUNCTION communication.generate_uuid()
RETURNS UUID AS $$
BEGIN
    RETURN uuid_generate_v4();
END;
$$ LANGUAGE plpgsql;

-- Fonction pour nettoyer le texte pour la recherche
CREATE OR REPLACE FUNCTION communication.clean_text_for_search(input_text TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN lower(unaccent(trim(input_text)));
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Fonction pour calculer la similarité de texte
CREATE OR REPLACE FUNCTION communication.text_similarity(text1 TEXT, text2 TEXT)
RETURNS FLOAT AS $$
BEGIN
    RETURN similarity(
        communication.clean_text_for_search(text1),
        communication.clean_text_for_search(text2)
    );
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Fonction pour formater les dates en français
CREATE OR REPLACE FUNCTION communication.format_date_fr(input_date TIMESTAMP)
RETURNS TEXT AS $$
BEGIN
    RETURN to_char(input_date, 'DD/MM/YYYY à HH24:MI');
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Fonction pour calculer le temps écoulé
CREATE OR REPLACE FUNCTION communication.time_ago(input_date TIMESTAMP)
RETURNS TEXT AS $$
DECLARE
    diff_seconds INTEGER;
    diff_minutes INTEGER;
    diff_hours INTEGER;
    diff_days INTEGER;
BEGIN
    diff_seconds := EXTRACT(EPOCH FROM (NOW() - input_date))::INTEGER;
    
    IF diff_seconds < 60 THEN
        RETURN 'À l''instant';
    ELSIF diff_seconds < 3600 THEN
        diff_minutes := diff_seconds / 60;
        RETURN diff_minutes || ' min';
    ELSIF diff_seconds < 86400 THEN
        diff_hours := diff_seconds / 3600;
        RETURN diff_hours || 'h';
    ELSE
        diff_days := diff_seconds / 86400;
        IF diff_days < 7 THEN
            RETURN diff_days || 'j';
        ELSE
            RETURN communication.format_date_fr(input_date);
        END IF;
    END IF;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Fonction pour valider les emails
CREATE OR REPLACE FUNCTION communication.is_valid_email(email TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$';
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Fonction pour valider les numéros de téléphone
CREATE OR REPLACE FUNCTION communication.is_valid_phone(phone TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    -- Format français : +33, 0X XX XX XX XX
    RETURN phone ~* '^(\+33|0)[1-9]([0-9]{8})$';
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Fonction pour nettoyer les anciens messages supprimés
CREATE OR REPLACE FUNCTION communication.cleanup_deleted_messages()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Supprimer les messages marqués comme supprimés depuis plus de 30 jours
    DELETE FROM communication.message 
    WHERE est_supprime = true 
    AND date_modification < NOW() - INTERVAL '30 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour nettoyer les anciennes sessions de présence
CREATE OR REPLACE FUNCTION communication.cleanup_old_presence()
RETURNS INTEGER AS $$
DECLARE
    cleaned_count INTEGER;
BEGIN
    -- Supprimer les sessions de présence inactives depuis plus de 24h
    DELETE FROM communication.user_presence 
    WHERE derniere_activite < NOW() - INTERVAL '24 hours';
    
    GET DIAGNOSTICS cleaned_count = ROW_COUNT;
    
    RETURN cleaned_count;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour nettoyer les anciennes notifications
CREATE OR REPLACE FUNCTION communication.cleanup_old_notifications()
RETURNS INTEGER AS $$
DECLARE
    cleaned_count INTEGER;
BEGIN
    -- Supprimer les notifications lues depuis plus de 90 jours
    DELETE FROM communication.notification 
    WHERE statut = 'LU' 
    AND date_lecture < NOW() - INTERVAL '90 days';
    
    GET DIAGNOSTICS cleaned_count = ROW_COUNT;
    
    RETURN cleaned_count;
END;
$$ LANGUAGE plpgsql;

-- Affichage des informations
SELECT 'Base de données communication_db initialisée avec succès' AS status;
SELECT 'Schéma communication créé' AS schema_status;
SELECT 'Extensions installées: uuid-ossp, pg_trgm, unaccent' AS extensions;
SELECT 'Fonctions utilitaires créées' AS functions_status;
