# Tests d'intégration pour Finance Service
# Utiliser avec l'extension REST Client de VS Code

### Variables
@baseUrl = http://localhost:8085
@contentType = application/json

### 1. Test de santé du service
GET {{baseUrl}}/actuator/health

### 2. Test des budgets

# Créer un nouveau budget
POST {{baseUrl}}/api/budgets
Content-Type: {{contentType}}

{
  "nom": "Budget Test API",
  "description": "Budget créé via tests d'intégration",
  "montantTotal": 25000.00,
  "periode": "ANNUEL",
  "dateDebut": "2024-01-01",
  "dateFin": "2024-12-31",
  "statut": "ACTIF",
  "seuilAlerte": 75.0,
  "responsableId": 1
}

### Récupérer tous les budgets
GET {{baseUrl}}/api/budgets

### Récupérer un budget par ID (remplacer {id} par un ID valide)
GET {{baseUrl}}/api/budgets/1

### Mettre à jour un budget
PUT {{baseUrl}}/api/budgets/1
Content-Type: {{contentType}}

{
  "nom": "Budget Test Modifié",
  "description": "Budget modifié via API",
  "seuilAlerte": 80.0
}

### 3. Test des transactions

# Créer une nouvelle transaction
POST {{baseUrl}}/api/transactions
Content-Type: {{contentType}}

{
  "reference": "TXN-TEST-001",
  "description": "Transaction de test via API",
  "montant": 750.00,
  "typeTransaction": "DEPENSE",
  "statut": "EN_ATTENTE",
  "modePaiement": "VIREMENT",
  "categorieTransactionId": 1,
  "utilisateurId": 1
}

### Récupérer toutes les transactions
GET {{baseUrl}}/api/transactions

### Récupérer les transactions avec pagination
GET {{baseUrl}}/api/transactions?page=0&size=10&sort=dateTransaction,desc

### Valider une transaction
PUT {{baseUrl}}/api/transactions/1/validation
Content-Type: {{contentType}}

{
  "validation": true,
  "commentaire": "Transaction validée via test API"
}

### Filtrer les transactions par type
GET {{baseUrl}}/api/transactions/filter?type=RECETTE&statut=VALIDEE

### 4. Test des sponsors

# Créer un nouveau sponsor
POST {{baseUrl}}/api/sponsors
Content-Type: {{contentType}}

{
  "nom": "TechCorp Solutions",
  "typePartenaire": "SPONSOR_PRINCIPAL",
  "secteurActivite": "TECHNOLOGIE",
  "montantContrat": 20000.00,
  "dateDebutContrat": "2024-01-01",
  "dateFinContrat": "2024-12-31",
  "statut": "ACTIF",
  "contactPrincipal": "<EMAIL>",
  "telephone": "+33123456789",
  "adresse": "123 Avenue de la Tech, 75001 Paris",
  "siteWeb": "https://techcorp.com"
}

### Récupérer tous les sponsors
GET {{baseUrl}}/api/sponsors

### Récupérer les sponsors actifs
GET {{baseUrl}}/api/sponsors/actifs

### Renouveler un contrat de sponsor
PUT {{baseUrl}}/api/sponsors/1/renouveler
Content-Type: {{contentType}}

{
  "nouvelleEcheance": "2025-12-31",
  "nouveauMontant": 25000.00
}

### 5. Test des salaires

# Récupérer tous les salaires
GET {{baseUrl}}/api/salaires

### Récupérer les salaires par période
GET {{baseUrl}}/api/salaires/periode?debut=2024-01-01&fin=2024-12-31

### Calculer un salaire
POST {{baseUrl}}/api/salaires/calculer
Content-Type: {{contentType}}

{
  "employeId": 101,
  "periode": "2024-10-01",
  "salaireBrut": 3000.00,
  "primes": 300.00,
  "heuresSupplementaires": 8,
  "joursAbsence": 0
}

### Valider un salaire
PUT {{baseUrl}}/api/salaires/1/validation
Content-Type: {{contentType}}

{
  "validation": true,
  "commentaire": "Salaire validé par RH"
}

### Générer un bulletin de paie
GET {{baseUrl}}/api/salaires/1/bulletin

### 6. Test des rapports

# Dashboard financier
GET {{baseUrl}}/api/rapports/dashboard

### Statistiques globales
GET {{baseUrl}}/api/rapports/stats-globales

### Rapport budgétaire
GET {{baseUrl}}/api/rapports/budget?budgetId=1&periode=2024

### Rapport des transactions
GET {{baseUrl}}/api/rapports/transactions?debut=2024-01-01&fin=2024-12-31

### Export Excel des transactions
GET {{baseUrl}}/api/rapports/transactions/export/excel?debut=2024-01-01&fin=2024-12-31

### Export PDF du rapport financier
GET {{baseUrl}}/api/rapports/financier/export/pdf?periode=2024

### 7. Test des catégories

# Récupérer toutes les catégories de transaction
GET {{baseUrl}}/api/categories-transaction

### Créer une nouvelle catégorie
POST {{baseUrl}}/api/categories-transaction
Content-Type: {{contentType}}

{
  "nom": "Formation",
  "description": "Frais de formation et développement",
  "type": "DEPENSE",
  "couleur": "#FF9800"
}

### 8. Test de recherche et filtres

# Recherche globale
GET {{baseUrl}}/api/search?q=budget&type=all

### Recherche dans les transactions
GET {{baseUrl}}/api/transactions/search?q=test&montantMin=100&montantMax=1000

### Recherche dans les sponsors
GET {{baseUrl}}/api/sponsors/search?nom=tech&secteur=TECHNOLOGIE

### 9. Test des statistiques

# Statistiques des budgets
GET {{baseUrl}}/api/budgets/stats

### Statistiques des transactions par mois
GET {{baseUrl}}/api/transactions/stats/mensuel?annee=2024

### Statistiques des sponsors
GET {{baseUrl}}/api/sponsors/stats

### Évolution des revenus/dépenses
GET {{baseUrl}}/api/rapports/evolution?debut=2024-01-01&fin=2024-12-31&granularite=MENSUEL

### 10. Test de gestion d'erreurs

# Test 404 - Ressource inexistante
GET {{baseUrl}}/api/budgets/99999

### Test 400 - Données invalides
POST {{baseUrl}}/api/budgets
Content-Type: {{contentType}}

{
  "nom": "",
  "montantTotal": -100
}

### Test 409 - Conflit (référence existante)
POST {{baseUrl}}/api/transactions
Content-Type: {{contentType}}

{
  "reference": "TXN-TEST-001",
  "description": "Transaction en doublon",
  "montant": 100.00,
  "typeTransaction": "DEPENSE"
}

### 11. Test des endpoints de monitoring

# Métriques Actuator
GET {{baseUrl}}/actuator/metrics

### Info de l'application
GET {{baseUrl}}/actuator/info

### Environnement
GET {{baseUrl}}/actuator/env

### Configuration
GET {{baseUrl}}/actuator/configprops
