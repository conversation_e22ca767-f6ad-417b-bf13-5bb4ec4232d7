package com.sprintbot.communication.repository;

import com.sprintbot.communication.entity.ChatbotConversation;
import com.sprintbot.communication.entity.StatutChatbot;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository pour l'entité ChatbotConversation
 */
@Repository
public interface ChatbotConversationRepository extends JpaRepository<ChatbotConversation, Long> {

    /**
     * Trouve une conversation par session ID
     */
    Optional<ChatbotConversation> findBySessionId(String sessionId);

    /**
     * Trouve les conversations d'un utilisateur
     */
    Page<ChatbotConversation> findByUtilisateurIdOrderByDateCreationDesc(
            Long utilisateurId, Pageable pageable);

    /**
     * Trouve les conversations actives d'un utilisateur
     */
    List<ChatbotConversation> findByUtilisateurIdAndStatutOrderByDateCreationDesc(
            Long utilisateurId, StatutChatbot statut);

    /**
     * Trouve la dernière conversation active d'un utilisateur
     */
    Optional<ChatbotConversation> findFirstByUtilisateurIdAndStatutOrderByDateCreationDesc(
            Long utilisateurId, StatutChatbot statut);

    /**
     * Trouve les conversations par statut
     */
    List<ChatbotConversation> findByStatutOrderByDateCreationDesc(StatutChatbot statut);

    /**
     * Trouve les conversations escaladées vers un agent humain
     */
    List<ChatbotConversation> findByEscaladeVersHumainTrueAndAgentHumainIdOrderByDateCreationDesc(
            Long agentHumainId);

    /**
     * Trouve les conversations escaladées non assignées
     */
    List<ChatbotConversation> findByEscaladeVersHumainTrueAndAgentHumainIdIsNullOrderByDateCreationDesc();

    /**
     * Trouve les conversations évaluées
     */
    List<ChatbotConversation> findByNoteSatisfactionIsNotNullOrderByDateCreationDesc();

    /**
     * Trouve les conversations par note de satisfaction
     */
    List<ChatbotConversation> findByNoteSatisfactionOrderByDateCreationDesc(Integer noteSatisfaction);

    /**
     * Statistiques des conversations par statut
     */
    @Query("SELECT c.statut, COUNT(c) FROM ChatbotConversation c " +
           "WHERE c.dateCreation >= :depuis " +
           "GROUP BY c.statut")
    List<Object[]> getStatistiquesParStatut(@Param("depuis") LocalDateTime depuis);

    /**
     * Statistiques des évaluations
     */
    @Query("SELECT c.noteSatisfaction, COUNT(c) FROM ChatbotConversation c " +
           "WHERE c.noteSatisfaction IS NOT NULL " +
           "AND c.dateCreation >= :depuis " +
           "GROUP BY c.noteSatisfaction " +
           "ORDER BY c.noteSatisfaction")
    List<Object[]> getStatistiquesEvaluations(@Param("depuis") LocalDateTime depuis);

    /**
     * Moyenne des notes de satisfaction
     */
    @Query("SELECT AVG(c.noteSatisfaction) FROM ChatbotConversation c " +
           "WHERE c.noteSatisfaction IS NOT NULL " +
           "AND c.dateCreation >= :depuis")
    Double getMoyenneNoteSatisfaction(@Param("depuis") LocalDateTime depuis);

    /**
     * Durée moyenne des sessions
     */
    @Query("SELECT AVG(c.dureeSession) FROM ChatbotConversation c " +
           "WHERE c.dureeSession IS NOT NULL " +
           "AND c.dateCreation >= :depuis")
    Double getDureeMoyenneSessions(@Param("depuis") LocalDateTime depuis);

    /**
     * Trouve les conversations longues
     */
    @Query("SELECT c FROM ChatbotConversation c " +
           "WHERE c.dureeSession > :dureeMinimum " +
           "ORDER BY c.dureeSession DESC")
    List<ChatbotConversation> findConversationsLongues(
            @Param("dureeMinimum") Long dureeMinimum,
            Pageable pageable);

    /**
     * Trouve les conversations avec beaucoup de messages
     */
    @Query("SELECT c FROM ChatbotConversation c " +
           "WHERE c.nombreMessages > :nombreMinimum " +
           "ORDER BY c.nombreMessages DESC")
    List<ChatbotConversation> findConversationsAvecBeaucoupDeMessages(
            @Param("nombreMinimum") Integer nombreMinimum,
            Pageable pageable);

    /**
     * Compte les conversations actives
     */
    Long countByStatut(StatutChatbot statut);

    /**
     * Compte les conversations d'un utilisateur par statut
     */
    Long countByUtilisateurIdAndStatut(Long utilisateurId, StatutChatbot statut);

    /**
     * Trouve les conversations récentes
     */
    @Query("SELECT c FROM ChatbotConversation c " +
           "WHERE c.dateCreation >= :depuis " +
           "ORDER BY c.dateCreation DESC")
    List<ChatbotConversation> findConversationsRecentes(
            @Param("depuis") LocalDateTime depuis,
            Pageable pageable);

    /**
     * Trouve les conversations par langue
     */
    List<ChatbotConversation> findByLangueOrderByDateCreationDesc(String langue);

    /**
     * Supprime les anciennes conversations terminées
     */
    @Query("DELETE FROM ChatbotConversation c " +
           "WHERE c.statut = 'TERMINE' " +
           "AND c.dateFin < :dateLimit " +
           "AND c.noteSatisfaction IS NULL")
    void supprimerAnciennesConversationsTerminees(@Param("dateLimit") LocalDateTime dateLimit);

    /**
     * Trouve les conversations abandonnées (actives mais anciennes)
     */
    @Query("SELECT c FROM ChatbotConversation c " +
           "WHERE c.statut = 'ACTIF' " +
           "AND c.dateModification < :dateLimit")
    List<ChatbotConversation> findConversationsAbandonnees(@Param("dateLimit") LocalDateTime dateLimit);

    /**
     * Statistiques par utilisateur
     */
    @Query("SELECT c.utilisateurId, COUNT(c), AVG(c.noteSatisfaction), AVG(c.dureeSession) " +
           "FROM ChatbotConversation c " +
           "WHERE c.dateCreation >= :depuis " +
           "GROUP BY c.utilisateurId " +
           "ORDER BY COUNT(c) DESC")
    List<Object[]> getStatistiquesParUtilisateur(@Param("depuis") LocalDateTime depuis);

    /**
     * Taux d'escalade
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN c.escaladeVersHumain = true THEN 1 END) * 100.0 / COUNT(c) " +
           "FROM ChatbotConversation c " +
           "WHERE c.dateCreation >= :depuis")
    Double getTauxEscalade(@Param("depuis") LocalDateTime depuis);
}
