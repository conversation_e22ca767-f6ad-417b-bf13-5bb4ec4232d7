Write-Host "=== INSTALLATION MAVEN ET TEST MICROSERVICE ===" -ForegroundColor Green
Write-Host "Club Olympique de Kelibia" -ForegroundColor Cyan
Write-Host ""

# Variables
$mavenVersion = "3.9.6"
$mavenUrl = "https://archive.apache.org/dist/maven/maven-3/$mavenVersion/binaries/apache-maven-$mavenVersion-bin.zip"
$mavenDir = "C:\maven"
$mavenBin = "$mavenDir\apache-maven-$mavenVersion\bin"

Write-Host "1. VÉRIFICATION MAVEN" -ForegroundColor Yellow
Write-Host "=====================" -ForegroundColor Yellow

# Vérifier si Maven est déjà installé
try {
    $existingMaven = mvn --version 2>&1 | Select-String "Apache Maven"
    if ($existingMaven) {
        Write-Host "✅ Maven déjà installé : $($existingMaven.Line)" -ForegroundColor Green
        $mavenInstalled = $true
    }
} catch {
    Write-Host "❌ Maven non installé" -ForegroundColor Red
    $mavenInstalled = $false
}

if (-not $mavenInstalled) {
    Write-Host ""
    Write-Host "2. INSTALLATION MAVEN" -ForegroundColor Yellow
    Write-Host "=====================" -ForegroundColor Yellow
    
    # Créer le répertoire Maven
    if (-not (Test-Path $mavenDir)) {
        New-Item -ItemType Directory -Path $mavenDir -Force | Out-Null
        Write-Host "✅ Répertoire Maven créé : $mavenDir" -ForegroundColor Green
    }
    
    $zipPath = "$mavenDir\maven.zip"
    
    # Télécharger Maven
    Write-Host "📥 Téléchargement de Maven $mavenVersion..." -ForegroundColor Cyan
    try {
        Invoke-WebRequest -Uri $mavenUrl -OutFile $zipPath -UseBasicParsing
        Write-Host "✅ Maven téléchargé" -ForegroundColor Green
    } catch {
        Write-Host "❌ Erreur de téléchargement : $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
        Write-Host "SOLUTION ALTERNATIVE :" -ForegroundColor Yellow
        Write-Host "1. Télécharger manuellement depuis : https://maven.apache.org/download.cgi" -ForegroundColor White
        Write-Host "2. Extraire dans C:\maven" -ForegroundColor White
        Write-Host "3. Ajouter C:\maven\apache-maven-$mavenVersion\bin au PATH" -ForegroundColor White
        Read-Host "Appuyez sur Entrée pour continuer"
        exit 1
    }
    
    # Extraire Maven
    Write-Host "📦 Extraction de Maven..." -ForegroundColor Cyan
    try {
        Expand-Archive -Path $zipPath -DestinationPath $mavenDir -Force
        Remove-Item $zipPath -Force
        Write-Host "✅ Maven extrait" -ForegroundColor Green
    } catch {
        Write-Host "❌ Erreur d'extraction : $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
    
    # Ajouter au PATH pour cette session
    $env:PATH = "$mavenBin;$env:PATH"
    Write-Host "✅ Maven ajouté au PATH pour cette session" -ForegroundColor Green
    
    # Vérifier l'installation
    try {
        $mavenCheck = & "$mavenBin\mvn.cmd" --version 2>&1 | Select-String "Apache Maven"
        if ($mavenCheck) {
            Write-Host "✅ Maven installé avec succès : $($mavenCheck.Line)" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ Problème avec l'installation Maven" -ForegroundColor Red
        exit 1
    }
}

Write-Host ""
Write-Host "3. TEST DU BACKEND" -ForegroundColor Yellow
Write-Host "==================" -ForegroundColor Yellow

# Aller dans le répertoire backend
$backendPath = "microservices\planning-performance-service\backend"
if (Test-Path $backendPath) {
    Set-Location $backendPath
    Write-Host "✅ Répertoire backend trouvé" -ForegroundColor Green
} else {
    Write-Host "❌ Répertoire backend non trouvé" -ForegroundColor Red
    exit 1
}

# Compiler et lancer le backend
Write-Host ""
Write-Host "🔨 Compilation du backend..." -ForegroundColor Cyan

try {
    if ($mavenInstalled) {
        $compileResult = mvn clean compile 2>&1
    } else {
        $compileResult = & "$mavenBin\mvn.cmd" clean compile 2>&1
    }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Compilation réussie" -ForegroundColor Green
    } else {
        Write-Host "❌ Erreur de compilation" -ForegroundColor Red
        Write-Host $compileResult
        exit 1
    }
} catch {
    Write-Host "❌ Erreur lors de la compilation : $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🚀 Lancement du backend..." -ForegroundColor Cyan
Write-Host "Port : 8082" -ForegroundColor White
Write-Host "Base de données : H2 (en mémoire)" -ForegroundColor White
Write-Host ""

# Lancer le backend en arrière-plan
try {
    if ($mavenInstalled) {
        Start-Process -FilePath "mvn" -ArgumentList "spring-boot:run" -WindowStyle Minimized
    } else {
        Start-Process -FilePath "$mavenBin\mvn.cmd" -ArgumentList "spring-boot:run" -WindowStyle Minimized
    }
    
    Write-Host "✅ Backend lancé en arrière-plan" -ForegroundColor Green
    Write-Host ""
    
    # Attendre que le service démarre
    Write-Host "⏳ Attente du démarrage du service..." -ForegroundColor Cyan
    Start-Sleep -Seconds 15
    
    # Tester le health check
    Write-Host "🔍 Test du health check..." -ForegroundColor Cyan
    try {
        $healthResponse = Invoke-WebRequest -Uri "http://localhost:8082/actuator/health" -UseBasicParsing -TimeoutSec 10
        if ($healthResponse.StatusCode -eq 200) {
            Write-Host "✅ Backend opérationnel !" -ForegroundColor Green
            Write-Host "Réponse : $($healthResponse.Content)" -ForegroundColor White
        }
    } catch {
        Write-Host "⚠️  Service en cours de démarrage..." -ForegroundColor Yellow
        Write-Host "Vérifiez manuellement : http://localhost:8082/actuator/health" -ForegroundColor White
    }
    
} catch {
    Write-Host "❌ Erreur lors du lancement : $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "4. URLS DE TEST" -ForegroundColor Yellow
Write-Host "===============" -ForegroundColor Yellow
Write-Host "Health Check : http://localhost:8082/actuator/health" -ForegroundColor White
Write-Host "H2 Console   : http://localhost:8082/h2-console" -ForegroundColor White
Write-Host "API Base     : http://localhost:8082/api" -ForegroundColor White
Write-Host "Entraînements: http://localhost:8082/api/entrainements" -ForegroundColor White
Write-Host "Performances : http://localhost:8082/api/performances" -ForegroundColor White
Write-Host ""

Write-Host "=== TEST TERMINÉ ===" -ForegroundColor Green
Write-Host "🏐 Club Olympique de Kelibia - Backend Planning Performance lancé !" -ForegroundColor Yellow

Read-Host "Appuyez sur Entrée pour continuer"
