# Medical Admin Service - Frontend

Frontend Angular pour le microservice de gestion médicale et administrative de SprintBot.

## 🚀 Technologies

- **Angular 17** - Framework frontend
- **TypeScript** - Langage de programmation
- **Bootstrap 5** - Framework CSS
- **Angular Material** - Composants UI
- **Font Awesome** - Icônes
- **NgBootstrap** - Composants Bootstrap pour Angular
- **ngx-toastr** - Notifications toast
- **Chart.js** - Graphiques et statistiques
- **RxJS** - Programmation réactive

## 📁 Structure du projet

```
src/
├── app/
│   ├── core/                    # Services et modèles centraux
│   │   ├── models/             # Modèles TypeScript
│   │   ├── services/           # Services Angular
│   │   └── interceptors/       # Intercepteurs HTTP
│   ├── shared/                 # Composants partagés
│   │   └── components/         # Composants réutilisables
│   ├── features/               # Modules fonctionnels
│   │   ├── dashboard/          # Tableau de bord
│   │   ├── donnees-sante/      # Gestion données de santé
│   │   ├── rendez-vous/        # Gestion rendez-vous
│   │   ├── demandes-administratives/  # Gestion demandes admin
│   │   └── rapports/           # Rapports et statistiques
│   ├── app.component.ts        # Composant racine
│   ├── app.config.ts           # Configuration de l'application
│   └── app.routes.ts           # Configuration des routes
├── environments/               # Configuration par environnement
├── assets/                     # Ressources statiques
└── styles.scss                # Styles globaux
```

## 🛠️ Installation et démarrage

### Prérequis
- Node.js 18+
- npm 9+
- Angular CLI 17+

### Installation
```bash
# Installer les dépendances
npm install

# Démarrer en mode développement
npm run start
# ou
ng serve

# Démarrer sur un port spécifique
npm run serve:dev
```

### Build
```bash
# Build de développement
npm run build

# Build de production
npm run build:prod
```

## 🔧 Configuration

### Environnements

**Développement** (`environment.ts`)
```typescript
export const environment = {
  production: false,
  apiUrl: 'http://localhost:8083',
  appName: 'Medical Admin Service',
  version: '1.0.0'
};
```

**Production** (`environment.prod.ts`)
```typescript
export const environment = {
  production: true,
  apiUrl: 'https://api.sprintbot.com/medical-admin',
  appName: 'Medical Admin Service',
  version: '1.0.0'
};
```

## 📋 Fonctionnalités

### 🏥 Gestion des données de santé
- Création et modification des examens médicaux
- Suivi des blessures et traitements
- Gestion des statuts et gravités
- Historique médical des joueurs

### 📅 Gestion des rendez-vous
- Planification des consultations
- Gestion des créneaux et disponibilités
- Suivi des statuts de rendez-vous
- Rappels automatiques

### 📄 Gestion administrative
- Création et suivi des demandes
- Workflow de validation multi-niveaux
- Gestion des coûts et échéances
- Historique des traitements

### 📊 Tableau de bord
- Vue d'ensemble des activités
- Statistiques en temps réel
- Alertes et notifications
- Actions rapides

## 🎨 Architecture

### Services principaux

**ApiService** - Service de base pour les appels HTTP
```typescript
// Exemple d'utilisation
this.apiService.get<DonneesSante[]>('/api/donnees-sante')
```

**DonneesSanteService** - Gestion des données médicales
```typescript
// Créer une nouvelle donnée de santé
this.donneesSanteService.creerDonneesSante(donneesSante)
```

**RendezVousService** - Gestion des rendez-vous
```typescript
// Planifier un nouveau rendez-vous
this.rendezVousService.creerRendezVous(rendezVous)
```

**DemandesAdministrativesService** - Gestion des demandes
```typescript
// Créer une nouvelle demande
this.demandesService.creerDemande(demande)
```

### Intercepteurs

**AuthInterceptor** - Ajout automatique du token JWT
**ErrorInterceptor** - Gestion centralisée des erreurs
**LoadingInterceptor** - Affichage du spinner de chargement

### Modèles TypeScript

Tous les modèles sont typés avec des interfaces TypeScript :
- `DonneesSante` - Données médicales
- `RendezVousMedical` - Rendez-vous médicaux
- `DemandeAdministrative` - Demandes administratives
- `User` - Utilisateur connecté

## 🔐 Sécurité

- Authentification JWT
- Intercepteur d'authentification automatique
- Gestion des rôles et permissions
- Protection des routes sensibles

## 📱 Responsive Design

- Interface adaptative mobile/desktop
- Composants Bootstrap responsives
- Navigation optimisée pour mobile
- Tableaux avec scroll horizontal

## 🧪 Tests

```bash
# Tests unitaires
npm run test

# Tests e2e
npm run e2e

# Linting
npm run lint
```

## 🚀 Déploiement

### Docker
```bash
# Build de l'image
docker build -t medical-admin-frontend .

# Lancement du conteneur
docker run -p 4203:80 medical-admin-frontend
```

### Intégration avec le backend
L'application se connecte automatiquement au backend Spring Boot sur le port 8083.

## 📝 Scripts disponibles

- `npm start` - Démarrage en mode développement
- `npm run build` - Build de développement
- `npm run build:prod` - Build de production
- `npm run test` - Tests unitaires
- `npm run lint` - Vérification du code
- `npm run serve:dev` - Démarrage sur port 4203

## 🔄 Intégration continue

Le projet est configuré pour :
- Build automatique sur push
- Tests automatisés
- Déploiement automatique en production

## 📞 Support

Pour toute question ou problème :
- Consulter la documentation du backend
- Vérifier les logs de la console
- Contacter l'équipe de développement SprintBot

## 🔮 Roadmap

- [ ] Implémentation complète des composants de liste
- [ ] Formulaires de création/édition
- [ ] Composants de détail
- [ ] Système de notifications en temps réel
- [ ] Rapports avancés avec graphiques
- [ ] Mode hors ligne
- [ ] Tests e2e complets
