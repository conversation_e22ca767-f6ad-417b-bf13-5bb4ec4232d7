version: '3.8'

services:
  # Base de données de test
  finance-test-db:
    image: postgres:15-alpine
    container_name: finance-test-db
    environment:
      POSTGRES_DB: finance_test_db
      POSTGRES_USER: finance_test_user
      POSTGRES_PASSWORD: finance_test_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - finance_test_db_data:/var/lib/postgresql/data
      - ./backend/src/main/resources/db/migration:/docker-entrypoint-initdb.d
    ports:
      - "5438:5432"
    networks:
      - finance-test-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U finance_test_user -d finance_test_db"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis pour les tests
  finance-test-redis:
    image: redis:7-alpine
    container_name: finance-test-redis
    command: redis-server --appendonly yes --requirepass finance_test_123
    volumes:
      - finance_test_redis_data:/data
    ports:
      - "6381:6379"
    networks:
      - finance-test-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Backend de test
  finance-test-backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: finance-test-backend
    environment:
      SPRING_PROFILES_ACTIVE: test
      SPRING_DATASOURCE_URL: ****************************************************************************
      SPRING_DATASOURCE_USERNAME: finance_test_user
      SPRING_DATASOURCE_PASSWORD: finance_test_password
      SPRING_JPA_HIBERNATE_DDL_AUTO: validate
      SPRING_FLYWAY_ENABLED: true
      SPRING_FLYWAY_BASELINE_ON_MIGRATE: true
      REDIS_HOST: finance-test-redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: finance_test_123
      JWT_SECRET: finance-test-secret-key-2024-very-long-and-secure
      JWT_EXPIRATION: 86400
      CORS_ALLOWED_ORIGINS: http://localhost:4206
      JAVA_OPTS: "-Xmx256m -Xms128m -XX:+UseG1GC"
      # Configuration de test
      LOGGING_LEVEL_COM_SPRINTBOT_FINANCE: DEBUG
      MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: health,metrics,info,env
    ports:
      - "8086:8085"
    depends_on:
      finance-test-db:
        condition: service_healthy
      finance-test-redis:
        condition: service_healthy
    networks:
      - finance-test-network
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8085/actuator/health || exit 1"]
      interval: 15s
      timeout: 10s
      retries: 5
      start_period: 60s
    restart: unless-stopped

  # Frontend de test
  finance-test-frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: finance-test-frontend
    environment:
      API_URL: http://finance-test-backend:8085
      NODE_ENV: test
    ports:
      - "4206:8080"
    depends_on:
      finance-test-backend:
        condition: service_healthy
    networks:
      - finance-test-network
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1"]
      interval: 15s
      timeout: 5s
      retries: 3
      start_period: 30s
    restart: unless-stopped

  # Service de tests d'intégration
  finance-integration-tests:
    image: curlimages/curl:latest
    container_name: finance-integration-tests
    depends_on:
      finance-test-backend:
        condition: service_healthy
      finance-test-frontend:
        condition: service_healthy
    networks:
      - finance-test-network
    volumes:
      - ./test-integration.sh:/tests/test-integration.sh
      - ./integration-test.http:/tests/integration-test.http
    command: >
      sh -c "
        echo 'Attente du démarrage complet des services...' &&
        sleep 30 &&
        echo 'Début des tests d''intégration...' &&
        /tests/test-integration.sh --backend-url http://finance-test-backend:8085 --frontend-url http://finance-test-frontend:8080
      "

  # Service de monitoring des tests
  finance-test-monitor:
    image: prom/prometheus:latest
    container_name: finance-test-monitor
    ports:
      - "9091:9090"
    volumes:
      - ./monitoring/prometheus-test.yml:/etc/prometheus/prometheus.yml
    networks:
      - finance-test-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=1h'
      - '--web.enable-lifecycle'

volumes:
  finance_test_db_data:
    driver: local
  finance_test_redis_data:
    driver: local

networks:
  finance-test-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
