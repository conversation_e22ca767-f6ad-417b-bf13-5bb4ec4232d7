-- Script d'insertion des données initiales pour le service de communication
-- Auteur: SprintBot Team
-- Version: 1.0.0

-- Utiliser le schéma communication
SET search_path TO communication, public;

-- Insertion des types de notifications par défaut dans les préférences
-- Ces données seront utilisées comme référence pour créer les préférences utilisateur

-- Créer une table temporaire pour les types de notifications
CREATE TEMP TABLE temp_notification_types (
    type VARCHAR(100),
    description TEXT,
    canaux_supportes TEXT[]
);

INSERT INTO temp_notification_types VALUES
('NOUVEAU_MESSAGE', 'Notification lors de la réception d''un nouveau message', ARRAY['PUSH', 'EMAIL', 'INTERNE']),
('NOUVELLE_CONVERSATION', 'Notification lors de la création d''une nouvelle conversation', ARRAY['PUSH', 'EMAIL', 'INTERNE']),
('AJOUT_CONVERSATION', 'Notification lors de l''ajout à une conversation', ARRAY['PUSH', 'EMAIL', 'INTERNE']),
('SUPPRESSION_CONVERSATION', 'Notification lors de la suppression d''une conversation', ARRAY['PUSH', 'EMAIL', 'INTERNE']),
('ESCALADE_CHATBOT', 'Notification lors de l''escalade d''une conversation chatbot', ARRAY['PUSH', 'EMAIL', 'INTERNE']),
('SYSTEME', 'Notifications système importantes', ARRAY['PUSH', 'EMAIL', 'INTERNE']),
('MENTION', 'Notification lors d''une mention dans un message', ARRAY['PUSH', 'EMAIL', 'INTERNE']),
('REACTION_MESSAGE', 'Notification lors d''une réaction à un message', ARRAY['PUSH', 'INTERNE']),
('REPONSE_MESSAGE', 'Notification lors d''une réponse à un message', ARRAY['PUSH', 'EMAIL', 'INTERNE']);

-- Insertion de conversations système par défaut
INSERT INTO conversation (nom, description, type, createur_id, est_archive) VALUES
('Support Technique', 'Canal de support technique pour tous les utilisateurs', 'CANAL', 1, false),
('Annonces Générales', 'Canal pour les annonces importantes de la plateforme', 'CANAL', 1, false),
('Feedback Utilisateurs', 'Canal pour collecter les retours des utilisateurs', 'CANAL', 1, false);

-- Insertion de messages système par défaut
INSERT INTO message (conversation_id, expediteur_id, contenu, type) VALUES
(1, 1, 'Bienvenue sur le canal de support technique ! N''hésitez pas à poser vos questions.', 'SYSTEME'),
(2, 1, 'Bienvenue sur SprintBot ! Ce canal vous tiendra informé des nouveautés et mises à jour.', 'SYSTEME'),
(3, 1, 'Vos retours sont précieux pour améliorer SprintBot. Partagez vos suggestions ici !', 'SYSTEME');

-- Insertion de données de test pour le chatbot
INSERT INTO chatbot_conversation (utilisateur_id, session_id, contexte, statut) VALUES
(1, 'demo-session-001', '{"derniere_intention": "salutation", "contexte_conversation": "demo"}', 'TERMINEE');

INSERT INTO chatbot_message (chatbot_conversation_id, contenu, est_utilisateur, intention, score_confiance) VALUES
(1, 'Bonjour, comment puis-je vous aider ?', false, 'salutation', 0.95),
(1, 'Bonjour ! J''aimerais savoir comment utiliser le système de messagerie.', true, 'demande_aide', 0.88),
(1, 'Je peux vous expliquer le fonctionnement de la messagerie. Vous pouvez créer des conversations privées, rejoindre des groupes, et utiliser les canaux d''équipe. Souhaitez-vous plus de détails sur un aspect particulier ?', false, 'explication', 0.92);

-- Insertion de préférences de notification par défaut pour l'utilisateur système (ID 1)
DO $$
BEGIN
    PERFORM communication.create_default_notification_preferences(1);
END $$;

-- Insertion de données de présence par défaut
INSERT INTO user_presence (utilisateur_id, statut, message_statut, plateforme, est_mobile) VALUES
(1, 'EN_LIGNE', 'Disponible pour le support', 'web', false);

-- Création d'une vue pour les statistiques globales
CREATE OR REPLACE VIEW communication.vue_statistiques_globales AS
SELECT 
    (SELECT COUNT(*) FROM communication.conversation WHERE est_archive = false) as conversations_actives,
    (SELECT COUNT(*) FROM communication.message WHERE est_supprime = false) as total_messages,
    (SELECT COUNT(*) FROM communication.message WHERE date_creation >= CURRENT_DATE) as messages_aujourd_hui,
    (SELECT COUNT(DISTINCT utilisateur_id) FROM communication.participant_conversation) as utilisateurs_actifs,
    (SELECT COUNT(*) FROM communication.notification WHERE statut = 'EN_ATTENTE') as notifications_en_attente,
    (SELECT COUNT(*) FROM communication.chatbot_conversation WHERE statut = 'ACTIVE') as sessions_chatbot_actives;

-- Création d'une vue pour les conversations avec derniers messages
CREATE OR REPLACE VIEW communication.vue_conversations_avec_dernier_message AS
SELECT 
    c.*,
    m.contenu as dernier_message_contenu,
    m.date_creation as dernier_message_date,
    m.expediteur_id as dernier_message_expediteur_id,
    (SELECT COUNT(*) FROM communication.participant_conversation pc WHERE pc.conversation_id = c.id) as nombre_participants
FROM communication.conversation c
LEFT JOIN LATERAL (
    SELECT contenu, date_creation, expediteur_id
    FROM communication.message 
    WHERE conversation_id = c.id 
    AND est_supprime = false
    ORDER BY date_creation DESC 
    LIMIT 1
) m ON true
WHERE c.est_archive = false;

-- Création d'une vue pour les messages avec informations étendues
CREATE OR REPLACE VIEW communication.vue_messages_etendus AS
SELECT 
    m.*,
    c.nom as conversation_nom,
    c.type as conversation_type,
    (SELECT COUNT(*) FROM communication.reaction_message rm WHERE rm.message_id = m.id) as nombre_reactions,
    (SELECT COUNT(*) FROM communication.lecture_message lm WHERE lm.message_id = m.id) as nombre_lectures,
    CASE WHEN m.message_parent_id IS NOT NULL THEN true ELSE false END as est_reponse
FROM communication.message m
INNER JOIN communication.conversation c ON m.conversation_id = c.id
WHERE m.est_supprime = false;

-- Insertion de données de configuration pour les emojis de réaction populaires
CREATE TEMP TABLE temp_emojis_populaires (emoji VARCHAR(10), description TEXT);

INSERT INTO temp_emojis_populaires VALUES
('👍', 'Pouce en l''air'),
('❤️', 'Cœur rouge'),
('😂', 'Rire aux larmes'),
('😮', 'Surpris'),
('😢', 'Triste'),
('🔥', 'Feu'),
('👏', 'Applaudissements'),
('🎉', 'Fête'),
('💯', 'Cent pour cent'),
('⚡', 'Éclair');

-- Fonction pour obtenir les emojis populaires
CREATE OR REPLACE FUNCTION communication.get_popular_emojis()
RETURNS TABLE (emoji VARCHAR(10), description TEXT, usage_count BIGINT) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        rm.emoji,
        COALESCE(tep.description, 'Emoji personnalisé') as description,
        COUNT(*) as usage_count
    FROM communication.reaction_message rm
    LEFT JOIN temp_emojis_populaires tep ON rm.emoji = tep.emoji
    GROUP BY rm.emoji, tep.description
    ORDER BY usage_count DESC
    LIMIT 10;
END;
$$ LANGUAGE plpgsql;

-- Création d'un job de nettoyage automatique (à exécuter périodiquement)
CREATE OR REPLACE FUNCTION communication.maintenance_job()
RETURNS TEXT AS $$
DECLARE
    result TEXT := '';
    deleted_messages INTEGER;
    cleaned_presence INTEGER;
    cleaned_notifications INTEGER;
BEGIN
    -- Nettoyage des messages supprimés
    SELECT communication.cleanup_deleted_messages() INTO deleted_messages;
    result := result || 'Messages supprimés: ' || deleted_messages || E'\n';
    
    -- Nettoyage des sessions de présence
    SELECT communication.cleanup_old_presence() INTO cleaned_presence;
    result := result || 'Sessions de présence nettoyées: ' || cleaned_presence || E'\n';
    
    -- Nettoyage des notifications
    SELECT communication.cleanup_old_notifications() INTO cleaned_notifications;
    result := result || 'Notifications nettoyées: ' || cleaned_notifications || E'\n';
    
    result := result || 'Maintenance terminée le: ' || CURRENT_TIMESTAMP;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Affichage du statut final
SELECT 'Données initiales insérées avec succès' AS status;
SELECT 'Vues et fonctions utilitaires créées' AS views_status;
SELECT 'Configuration des emojis et maintenance configurée' AS config_status;

-- Affichage des statistiques initiales
SELECT * FROM communication.vue_statistiques_globales;
