# 🚀 Guide de Déploiement - Planning Performance Service

## 📋 Vue d'ensemble

Ce guide détaille les procédures de déploiement du microservice Planning Performance Service dans différents environnements.

## 🏗️ Prérequis

### Infrastructure Requise

#### Serveur de Production
- **CPU** : 4 cores minimum (8 cores recommandé)
- **RAM** : 8 GB minimum (16 GB recommandé)
- **Stockage** : 100 GB SSD minimum
- **OS** : Ubuntu 20.04 LTS ou CentOS 8+

#### Logiciels Requis
- **Docker** 24.0+
- **Docker Compose** 2.20+
- **Git** 2.30+
- **Nginx** 1.20+ (pour le reverse proxy)

#### Base de Données
- **PostgreSQL** 15+
- **Redis** 7.0+ (pour le cache)

### Réseau et Sécurité
- **Ports ouverts** : 80, 443, 8082 (backend), 4202 (frontend)
- **Certificats SSL** : Let's Encrypt ou certificats d'entreprise
- **Firewall** : UFW ou iptables configuré

## 🌍 Environnements

### 1. Développement Local

#### Configuration
```bash
# Variables d'environnement
export SPRING_PROFILES_ACTIVE=dev
export DB_HOST=localhost
export DB_PORT=5433
export DB_NAME=planning_performance_db
export DB_USERNAME=planning_user
export DB_PASSWORD=dev_password
export JWT_SECRET=dev_secret_key
export LOG_LEVEL=DEBUG
```

#### Démarrage
```bash
# Cloner le repository
git clone <repository-url>
cd microservices/planning-performance-service

# Démarrer la base de données
docker run --name planning-dev-db \
  -e POSTGRES_DB=planning_performance_db \
  -e POSTGRES_USER=planning_user \
  -e POSTGRES_PASSWORD=dev_password \
  -p 5433:5432 \
  -d postgres:15

# Démarrer le backend
cd backend
./mvnw spring-boot:run

# Démarrer le frontend (nouveau terminal)
cd frontend
npm install
ng serve --port 4202
```

### 2. Staging

#### Configuration Docker Compose
```yaml
# docker-compose.staging.yml
version: '3.8'

services:
  planning-backend:
    image: sprintbot/planning-performance-backend:staging
    environment:
      - SPRING_PROFILES_ACTIVE=staging
      - DB_HOST=planning-staging-db
      - DB_PORT=5432
      - DB_NAME=planning_performance_staging
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
      - LOG_LEVEL=INFO
    ports:
      - "8082:8082"
    depends_on:
      - planning-staging-db
    networks:
      - planning-network

  planning-frontend:
    image: sprintbot/planning-performance-frontend:staging
    environment:
      - API_URL=http://planning-backend:8082/api
    ports:
      - "4202:80"
    depends_on:
      - planning-backend
    networks:
      - planning-network

  planning-staging-db:
    image: postgres:15
    environment:
      - POSTGRES_DB=planning_performance_staging
      - POSTGRES_USER=${DB_USERNAME}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - planning_staging_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5433:5432"
    networks:
      - planning-network

volumes:
  planning_staging_data:

networks:
  planning-network:
    driver: bridge
```

#### Déploiement Staging
```bash
# Variables d'environnement
export DB_USERNAME=planning_staging_user
export DB_PASSWORD=staging_secure_password
export JWT_SECRET=staging_jwt_secret_key_very_long

# Déploiement
docker-compose -f docker-compose.staging.yml up -d

# Vérification
curl http://localhost:8082/actuator/health
curl http://localhost:4202
```

### 3. Production

#### Configuration Production
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  planning-backend:
    image: sprintbot/planning-performance-backend:latest
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=${DB_HOST}
      - DB_PORT=5432
      - DB_NAME=${DB_NAME}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
      - LOG_LEVEL=WARN
      - METRICS_ENABLED=true
    ports:
      - "8082:8082"
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8082/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - planning-network

  planning-frontend:
    image: sprintbot/planning-performance-frontend:latest
    environment:
      - API_URL=${API_URL}
    ports:
      - "4202:80"
    restart: unless-stopped
    networks:
      - planning-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - planning-network

volumes:
  redis_data:

networks:
  planning-network:
    driver: bridge
```

#### Variables d'Environnement Production
```bash
# Créer le fichier .env.prod
cat > .env.prod << EOF
# Base de données
DB_HOST=prod-db-server.example.com
DB_NAME=planning_performance_prod
DB_USERNAME=planning_prod_user
DB_PASSWORD=super_secure_production_password

# Sécurité
JWT_SECRET=very_long_and_secure_jwt_secret_key_for_production

# API
API_URL=https://api.sprintbot.com/planning

# Monitoring
METRICS_ENABLED=true
LOG_LEVEL=WARN
EOF
```

#### Déploiement Production
```bash
# Charger les variables d'environnement
source .env.prod

# Déploiement
docker-compose -f docker-compose.prod.yml up -d

# Vérification des services
docker-compose -f docker-compose.prod.yml ps
docker-compose -f docker-compose.prod.yml logs -f planning-backend
```

## 🔄 CI/CD Pipeline

### GitHub Actions

#### Workflow Principal
```yaml
# .github/workflows/deploy.yml
name: Deploy Planning Performance Service

on:
  push:
    branches: [main, develop]
    paths:
      - 'microservices/planning-performance-service/**'
  pull_request:
    branches: [main]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME_BACKEND: sprintbot/planning-performance-backend
  IMAGE_NAME_FRONTEND: sprintbot/planning-performance-frontend

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'

      - name: Cache Maven dependencies
        uses: actions/cache@v3
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}

      - name: Run backend tests
        working-directory: microservices/planning-performance-service/backend
        run: ./mvnw test

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Cache Node modules
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}

      - name: Install frontend dependencies
        working-directory: microservices/planning-performance-service/frontend
        run: npm ci

      - name: Run frontend tests
        working-directory: microservices/planning-performance-service/frontend
        run: npm test -- --watch=false --browsers=ChromeHeadless

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Log in to Container Registry
        uses: docker/login-action@v2
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata (tags, labels)
        id: meta-backend
        uses: docker/metadata-action@v4
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME_BACKEND }}
          tags: |
            type=ref,event=branch
            type=sha,prefix={{branch}}-

      - name: Build and push backend image
        uses: docker/build-push-action@v4
        with:
          context: microservices/planning-performance-service/backend
          push: true
          tags: ${{ steps.meta-backend.outputs.tags }}
          labels: ${{ steps.meta-backend.outputs.labels }}

      - name: Extract metadata (tags, labels)
        id: meta-frontend
        uses: docker/metadata-action@v4
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME_FRONTEND }}
          tags: |
            type=ref,event=branch
            type=sha,prefix={{branch}}-

      - name: Build and push frontend image
        uses: docker/build-push-action@v4
        with:
          context: microservices/planning-performance-service/frontend
          push: true
          tags: ${{ steps.meta-frontend.outputs.tags }}
          labels: ${{ steps.meta-frontend.outputs.labels }}

  deploy-staging:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    environment: staging
    steps:
      - name: Deploy to staging
        uses: appleboy/ssh-action@v0.1.5
        with:
          host: ${{ secrets.STAGING_HOST }}
          username: ${{ secrets.STAGING_USER }}
          key: ${{ secrets.STAGING_SSH_KEY }}
          script: |
            cd /app/planning-performance-service
            docker-compose -f docker-compose.staging.yml pull
            docker-compose -f docker-compose.staging.yml up -d
            docker system prune -f

  deploy-production:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
      - name: Deploy to production
        uses: appleboy/ssh-action@v0.1.5
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USER }}
          key: ${{ secrets.PROD_SSH_KEY }}
          script: |
            cd /app/planning-performance-service
            docker-compose -f docker-compose.prod.yml pull
            docker-compose -f docker-compose.prod.yml up -d --no-deps planning-backend
            sleep 30
            docker-compose -f docker-compose.prod.yml up -d --no-deps planning-frontend
            docker system prune -f
```

### Scripts de Déploiement

#### Script de Déploiement Automatisé
```bash
#!/bin/bash
# deploy.sh

set -e

ENVIRONMENT=${1:-staging}
VERSION=${2:-latest}

echo "🚀 Déploiement du Planning Performance Service"
echo "Environnement: $ENVIRONMENT"
echo "Version: $VERSION"

# Validation de l'environnement
if [[ ! "$ENVIRONMENT" =~ ^(staging|production)$ ]]; then
    echo "❌ Environnement invalide. Utilisez 'staging' ou 'production'"
    exit 1
fi

# Chargement des variables d'environnement
if [ -f ".env.$ENVIRONMENT" ]; then
    source ".env.$ENVIRONMENT"
    echo "✅ Variables d'environnement chargées"
else
    echo "❌ Fichier .env.$ENVIRONMENT non trouvé"
    exit 1
fi

# Sauvegarde de la base de données
echo "💾 Sauvegarde de la base de données..."
docker exec planning-${ENVIRONMENT}-db pg_dump -U $DB_USERNAME $DB_NAME > "backup_$(date +%Y%m%d_%H%M%S).sql"
echo "✅ Sauvegarde terminée"

# Arrêt des services
echo "🛑 Arrêt des services..."
docker-compose -f docker-compose.$ENVIRONMENT.yml down

# Mise à jour des images
echo "📥 Téléchargement des nouvelles images..."
docker-compose -f docker-compose.$ENVIRONMENT.yml pull

# Démarrage des services
echo "🚀 Démarrage des services..."
docker-compose -f docker-compose.$ENVIRONMENT.yml up -d

# Vérification de la santé des services
echo "🔍 Vérification de la santé des services..."
sleep 30

# Test du backend
if curl -f http://localhost:8082/actuator/health > /dev/null 2>&1; then
    echo "✅ Backend opérationnel"
else
    echo "❌ Backend non opérationnel"
    exit 1
fi

# Test du frontend
if curl -f http://localhost:4202 > /dev/null 2>&1; then
    echo "✅ Frontend opérationnel"
else
    echo "❌ Frontend non opérationnel"
    exit 1
fi

echo "🎉 Déploiement terminé avec succès!"
```

#### Script de Rollback
```bash
#!/bin/bash
# rollback.sh

set -e

ENVIRONMENT=${1:-staging}
BACKUP_FILE=${2}

echo "🔄 Rollback du Planning Performance Service"
echo "Environnement: $ENVIRONMENT"

if [ -z "$BACKUP_FILE" ]; then
    echo "❌ Fichier de sauvegarde requis"
    echo "Usage: ./rollback.sh <environment> <backup_file>"
    exit 1
fi

# Chargement des variables d'environnement
source ".env.$ENVIRONMENT"

# Arrêt des services
echo "🛑 Arrêt des services..."
docker-compose -f docker-compose.$ENVIRONMENT.yml down

# Restauration de la base de données
echo "💾 Restauration de la base de données..."
docker exec -i planning-${ENVIRONMENT}-db psql -U $DB_USERNAME $DB_NAME < $BACKUP_FILE
echo "✅ Restauration terminée"

# Démarrage avec l'ancienne version
echo "🚀 Démarrage avec l'ancienne version..."
docker-compose -f docker-compose.$ENVIRONMENT.yml up -d

echo "🎉 Rollback terminé avec succès!"
```

## 🔧 Configuration Nginx

### Reverse Proxy
```nginx
# /etc/nginx/sites-available/planning-performance-service
server {
    listen 80;
    server_name planning.sprintbot.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name planning.sprintbot.com;

    ssl_certificate /etc/letsencrypt/live/planning.sprintbot.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/planning.sprintbot.com/privkey.pem;

    # Frontend
    location / {
        proxy_pass http://localhost:4202;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Backend API
    location /api/ {
        proxy_pass http://localhost:8082;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # CORS headers
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "Authorization, Content-Type";
    }

    # Health check
    location /health {
        proxy_pass http://localhost:8082/actuator/health;
        access_log off;
    }
}
```

## 📊 Monitoring et Alertes

### Prometheus Configuration
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'planning-performance-service'
    static_configs:
      - targets: ['localhost:8082']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 5s
```

### Grafana Dashboard
```json
{
  "dashboard": {
    "title": "Planning Performance Service",
    "panels": [
      {
        "title": "Requests per Second",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))"
          }
        ]
      }
    ]
  }
}
```

## 🚨 Procédures d'Urgence

### Redémarrage Rapide
```bash
# Redémarrage complet
docker-compose restart

# Redémarrage du backend uniquement
docker-compose restart planning-backend

# Redémarrage du frontend uniquement
docker-compose restart planning-frontend
```

### Diagnostic des Problèmes
```bash
# Vérification des logs
docker-compose logs -f planning-backend
docker-compose logs -f planning-frontend

# Vérification de l'état des conteneurs
docker-compose ps

# Vérification des ressources
docker stats

# Test de connectivité base de données
docker exec planning-backend pg_isready -h planning-db -p 5432
```

### Contacts d'Urgence
- **Équipe DevOps** : <EMAIL>
- **Équipe Développement** : <EMAIL>
- **Support 24/7** : +33 1 23 45 67 89

---

## 📝 Checklist de Déploiement

### Avant le Déploiement
- [ ] Tests unitaires passent
- [ ] Tests d'intégration passent
- [ ] Code review approuvé
- [ ] Variables d'environnement configurées
- [ ] Sauvegarde de la base de données effectuée
- [ ] Certificats SSL valides

### Pendant le Déploiement
- [ ] Services arrêtés proprement
- [ ] Images mises à jour
- [ ] Services redémarrés
- [ ] Health checks validés
- [ ] Tests de fumée passent

### Après le Déploiement
- [ ] Monitoring activé
- [ ] Logs vérifiés
- [ ] Performance validée
- [ ] Équipe notifiée
- [ ] Documentation mise à jour
