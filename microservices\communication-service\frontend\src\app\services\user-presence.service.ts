import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, interval, Subscription } from 'rxjs';
import { tap, switchMap } from 'rxjs/operators';

// Configuration temporaire pour le développement
const environment = {
  production: false,
  apiUrl: 'http://localhost:8084'
};

export interface UserPresence {
  id: number;
  utilisateurId: number;
  statut: 'EN_LIGNE' | 'ABSENT' | 'OCCUPE' | 'HORS_LIGNE' | 'INVISIBLE';
  messageStatut?: string;
  plateforme: string;
  estMobile: boolean;
  derniereActivite: string;
  dateCreation: string;
}

export interface PresenceUpdate {
  statut: string;
  messageStatut?: string;
}

@Injectable({
  providedIn: 'root'
})
export class UserPresenceService {
  private apiUrl = `${environment.apiUrl}/api/presence`;
  private presenceSubject = new BehaviorSubject<UserPresence | null>(null);
  private presencesUtilisateursSubject = new BehaviorSubject<Map<number, UserPresence>>(new Map());
  
  private heartbeatSubscription?: Subscription;
  private readonly HEARTBEAT_INTERVAL = 30000; // 30 secondes
  private readonly ACTIVITY_THRESHOLD = 60000; // 1 minute

  private lastActivity = Date.now();
  private currentUserId?: number;

  constructor(private http: HttpClient) {
    this.initializeActivityTracking();
  }

  // Observables pour les composants
  getPresence(): Observable<UserPresence | null> {
    return this.presenceSubject.asObservable();
  }

  getPresencesUtilisateurs(): Observable<Map<number, UserPresence>> {
    return this.presencesUtilisateursSubject.asObservable();
  }

  // Initialisation de la présence
  initialiserPresence(utilisateurId: number): Observable<UserPresence> {
    this.currentUserId = utilisateurId;
    
    return this.http.post<UserPresence>(`${this.apiUrl}/initialiser`, {
      utilisateurId,
      plateforme: this.detectPlatform(),
      estMobile: this.isMobile()
    }).pipe(
      tap(presence => {
        this.presenceSubject.next(presence);
        this.startHeartbeat();
      })
    );
  }

  // Mise à jour du statut
  mettreAJourStatut(statut: string, messageStatut?: string): Observable<UserPresence> {
    if (!this.currentUserId) {
      throw new Error('Utilisateur non initialisé');
    }

    return this.http.put<UserPresence>(`${this.apiUrl}/${this.currentUserId}`, {
      statut,
      messageStatut
    }).pipe(
      tap(presence => {
        this.presenceSubject.next(presence);
        this.updatePresenceMap(presence);
      })
    );
  }

  // Obtenir la présence d'un utilisateur
  obtenirPresenceUtilisateur(utilisateurId: number): Observable<UserPresence> {
    return this.http.get<UserPresence>(`${this.apiUrl}/${utilisateurId}`).pipe(
      tap(presence => this.updatePresenceMap(presence))
    );
  }

  // Obtenir les présences de plusieurs utilisateurs
  obtenirPresencesUtilisateurs(utilisateurIds: number[]): Observable<UserPresence[]> {
    return this.http.post<UserPresence[]>(`${this.apiUrl}/batch`, {
      utilisateurIds
    }).pipe(
      tap(presences => {
        presences.forEach(presence => this.updatePresenceMap(presence));
      })
    );
  }

  // Obtenir les utilisateurs en ligne
  obtenirUtilisateursEnLigne(): Observable<UserPresence[]> {
    return this.http.get<UserPresence[]>(`${this.apiUrl}/en-ligne`);
  }

  // Obtenir les statistiques de présence
  obtenirStatistiquesPresence(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/statistiques`);
  }

  // Heartbeat pour maintenir la présence
  private startHeartbeat(): void {
    if (this.heartbeatSubscription) {
      this.heartbeatSubscription.unsubscribe();
    }

    this.heartbeatSubscription = interval(this.HEARTBEAT_INTERVAL).pipe(
      switchMap(() => this.envoyerHeartbeat())
    ).subscribe({
      next: (presence) => {
        this.presenceSubject.next(presence);
        this.updatePresenceMap(presence);
      },
      error: (error) => {
        console.error('Erreur heartbeat présence:', error);
      }
    });
  }

  private envoyerHeartbeat(): Observable<UserPresence> {
    if (!this.currentUserId) {
      throw new Error('Utilisateur non initialisé');
    }

    // Déterminer le statut basé sur l'activité
    const timeSinceLastActivity = Date.now() - this.lastActivity;
    let statut = this.presenceSubject.value?.statut || 'EN_LIGNE';

    if (timeSinceLastActivity > this.ACTIVITY_THRESHOLD && statut === 'EN_LIGNE') {
      statut = 'ABSENT';
    }

    return this.http.post<UserPresence>(`${this.apiUrl}/${this.currentUserId}/heartbeat`, {
      statut,
      derniereActivite: new Date().toISOString()
    });
  }

  // Suivi de l'activité utilisateur
  private initializeActivityTracking(): void {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    events.forEach(event => {
      document.addEventListener(event, () => {
        this.lastActivity = Date.now();
        this.handleUserActivity();
      }, true);
    });

    // Gérer la visibilité de la page
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.handlePageHidden();
      } else {
        this.handlePageVisible();
      }
    });

    // Gérer la fermeture de la page
    window.addEventListener('beforeunload', () => {
      this.deconnecterPresence();
    });
  }

  private handleUserActivity(): void {
    const currentPresence = this.presenceSubject.value;
    if (currentPresence && currentPresence.statut === 'ABSENT') {
      this.mettreAJourStatut('EN_LIGNE').subscribe();
    }
  }

  private handlePageHidden(): void {
    const currentPresence = this.presenceSubject.value;
    if (currentPresence && currentPresence.statut === 'EN_LIGNE') {
      this.mettreAJourStatut('ABSENT').subscribe();
    }
  }

  private handlePageVisible(): void {
    this.lastActivity = Date.now();
    const currentPresence = this.presenceSubject.value;
    if (currentPresence && currentPresence.statut === 'ABSENT') {
      this.mettreAJourStatut('EN_LIGNE').subscribe();
    }
  }

  // Déconnexion de la présence
  deconnecterPresence(): void {
    if (this.currentUserId) {
      // Appel synchrone pour la fermeture de page
      navigator.sendBeacon(
        `${this.apiUrl}/${this.currentUserId}/deconnecter`,
        JSON.stringify({ statut: 'HORS_LIGNE' })
      );
    }

    if (this.heartbeatSubscription) {
      this.heartbeatSubscription.unsubscribe();
    }

    this.presenceSubject.next(null);
    this.currentUserId = undefined;
  }

  // Utilitaires privées
  private updatePresenceMap(presence: UserPresence): void {
    const presences = this.presencesUtilisateursSubject.value;
    presences.set(presence.utilisateurId, presence);
    this.presencesUtilisateursSubject.next(new Map(presences));
  }

  private detectPlatform(): string {
    const userAgent = navigator.userAgent.toLowerCase();
    if (userAgent.includes('android')) return 'android';
    if (userAgent.includes('iphone') || userAgent.includes('ipad')) return 'ios';
    if (userAgent.includes('windows')) return 'windows';
    if (userAgent.includes('mac')) return 'mac';
    if (userAgent.includes('linux')) return 'linux';
    return 'web';
  }

  private isMobile(): boolean {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  }

  // Utilitaires publiques
  getStatutColor(statut: string): string {
    switch (statut) {
      case 'EN_LIGNE': return 'success';
      case 'ABSENT': return 'warning';
      case 'OCCUPE': return 'danger';
      case 'HORS_LIGNE': return 'secondary';
      case 'INVISIBLE': return 'secondary';
      default: return 'secondary';
    }
  }

  getStatutIcon(statut: string): string {
    switch (statut) {
      case 'EN_LIGNE': return 'circle';
      case 'ABSENT': return 'schedule';
      case 'OCCUPE': return 'do_not_disturb';
      case 'HORS_LIGNE': return 'radio_button_unchecked';
      case 'INVISIBLE': return 'visibility_off';
      default: return 'help';
    }
  }

  getStatutLabel(statut: string): string {
    switch (statut) {
      case 'EN_LIGNE': return 'En ligne';
      case 'ABSENT': return 'Absent';
      case 'OCCUPE': return 'Occupé';
      case 'HORS_LIGNE': return 'Hors ligne';
      case 'INVISIBLE': return 'Invisible';
      default: return 'Inconnu';
    }
  }

  formatDerniereActivite(date: string): string {
    const now = new Date();
    const activity = new Date(date);
    const diffMs = now.getTime() - activity.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'À l\'instant';
    if (diffMins < 60) return `Il y a ${diffMins} min`;
    if (diffHours < 24) return `Il y a ${diffHours}h`;
    if (diffDays < 7) return `Il y a ${diffDays}j`;
    
    return activity.toLocaleDateString('fr-FR');
  }

  isUserOnline(utilisateurId: number): boolean {
    const presence = this.presencesUtilisateursSubject.value.get(utilisateurId);
    return presence?.statut === 'EN_LIGNE' || presence?.statut === 'OCCUPE';
  }

  // Nettoyage
  ngOnDestroy(): void {
    this.deconnecterPresence();
  }
}
