package com.sprintbot.communication.controller;

import com.sprintbot.communication.entity.ChatbotConversation;
import com.sprintbot.communication.entity.ChatbotMessage;
import com.sprintbot.communication.service.ChatbotService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.Map;

/**
 * Contrôleur REST pour la gestion du chatbot
 */
@RestController
@RequestMapping("/api/chatbot")
@CrossOrigin(origins = {"http://localhost:4200", "http://localhost:4204"})
public class ChatbotController {

    private static final Logger logger = LoggerFactory.getLogger(ChatbotController.class);

    @Autowired
    private ChatbotService chatbotService;

    /**
     * Démarre une nouvelle conversation avec le chatbot
     */
    @PostMapping("/demarrer")
    public ResponseEntity<?> demarrerConversation(@Valid @RequestBody DemarrerConversationRequest request) {
        try {
            ChatbotConversation conversation = chatbotService.demarrerConversation(
                    request.utilisateurId(),
                    request.langue()
            );

            return ResponseEntity.status(HttpStatus.CREATED).body(conversation);

        } catch (Exception e) {
            logger.error("Erreur lors du démarrage de conversation chatbot: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Envoie un message au chatbot
     */
    @PostMapping("/message")
    public ResponseEntity<?> envoyerMessage(@Valid @RequestBody EnvoyerMessageChatbotRequest request) {
        try {
            ChatbotMessage reponse = chatbotService.traiterMessageUtilisateur(
                    request.sessionId(),
                    request.contenu()
            );

            return ResponseEntity.ok(reponse);

        } catch (Exception e) {
            logger.error("Erreur lors de l'envoi de message au chatbot: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Obtient l'historique d'une conversation chatbot
     */
    @GetMapping("/conversation/{sessionId}/historique")
    public ResponseEntity<?> obtenirHistoriqueConversation(
            @PathVariable String sessionId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "50") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<ChatbotMessage> messages = chatbotService.obtenirHistoriqueConversation(sessionId, pageable);

            return ResponseEntity.ok(messages);

        } catch (Exception e) {
            logger.error("Erreur lors de l'obtention de l'historique chatbot: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Obtient les conversations chatbot d'un utilisateur
     */
    @GetMapping("/utilisateur/{utilisateurId}/conversations")
    public ResponseEntity<?> obtenirConversationsUtilisateur(
            @PathVariable Long utilisateurId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<ChatbotConversation> conversations = chatbotService
                    .obtenirConversationsUtilisateur(utilisateurId, pageable);

            return ResponseEntity.ok(conversations);

        } catch (Exception e) {
            logger.error("Erreur lors de l'obtention des conversations chatbot: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Évalue un message du chatbot
     */
    @PostMapping("/message/{messageId}/evaluer")
    public ResponseEntity<?> evaluerMessage(
            @PathVariable Long messageId,
            @RequestBody EvaluerMessageRequest request) {
        try {
            chatbotService.evaluerMessage(messageId, request.estUtile(), request.feedback());

            return ResponseEntity.ok(Map.of("message", "Évaluation enregistrée"));

        } catch (Exception e) {
            logger.error("Erreur lors de l'évaluation de message chatbot: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Escalade une conversation vers un agent humain
     */
    @PostMapping("/conversation/{sessionId}/escalader")
    public ResponseEntity<?> escaladerVersHumain(
            @PathVariable String sessionId,
            @RequestBody EscaladerConversationRequest request) {
        try {
            // Récupérer la conversation par sessionId
            ChatbotConversation conversation = chatbotService.obtenirHistoriqueConversation(sessionId, 
                    PageRequest.of(0, 1)).getContent().get(0).getConversation();
            
            chatbotService.escaladerVersHumain(conversation, request.raison());

            return ResponseEntity.ok(Map.of("message", "Conversation escaladée vers un agent humain"));

        } catch (Exception e) {
            logger.error("Erreur lors de l'escalade vers humain: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Termine une conversation chatbot
     */
    @PostMapping("/conversation/{sessionId}/terminer")
    public ResponseEntity<?> terminerConversation(
            @PathVariable String sessionId,
            @RequestBody TerminerConversationRequest request) {
        try {
            chatbotService.terminerConversation(
                    sessionId,
                    request.noteSatisfaction(),
                    request.commentaire()
            );

            return ResponseEntity.ok(Map.of("message", "Conversation terminée"));

        } catch (Exception e) {
            logger.error("Erreur lors de la terminaison de conversation chatbot: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Obtient les statistiques du chatbot
     */
    @GetMapping("/statistiques")
    public ResponseEntity<?> obtenirStatistiquesChatbot() {
        try {
            // TODO: Implémenter les statistiques du chatbot
            Map<String, Object> statistiques = Map.of(
                    "conversationsActives", 0,
                    "conversationsTerminees", 0,
                    "satisfactionMoyenne", 0.0,
                    "tempsReponseMoyen", 0.0
            );

            return ResponseEntity.ok(statistiques);

        } catch (Exception e) {
            logger.error("Erreur lors de l'obtention des statistiques chatbot: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Teste la connexion avec OpenAI
     */
    @GetMapping("/test-connexion")
    public ResponseEntity<?> testerConnexionOpenAI() {
        try {
            // TODO: Implémenter un test de connexion simple
            return ResponseEntity.ok(Map.of(
                    "statut", "OK",
                    "message", "Connexion OpenAI fonctionnelle"
            ));

        } catch (Exception e) {
            logger.error("Erreur lors du test de connexion OpenAI: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                    .body(Map.of("erreur", "Service OpenAI indisponible"));
        }
    }

    /**
     * Obtient les intentions détectées les plus fréquentes
     */
    @GetMapping("/intentions-frequentes")
    public ResponseEntity<?> obtenirIntentionsFrequentes() {
        try {
            // TODO: Implémenter l'analyse des intentions
            Map<String, Object> intentions = Map.of(
                    "planning", 25,
                    "performance", 20,
                    "medical", 15,
                    "communication", 10,
                    "general", 30
            );

            return ResponseEntity.ok(intentions);

        } catch (Exception e) {
            logger.error("Erreur lors de l'obtention des intentions fréquentes: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Envoie un message de test au chatbot
     */
    @PostMapping("/test")
    public ResponseEntity<?> testerChatbot(@RequestBody TestChatbotRequest request) {
        try {
            // Démarrer une conversation de test
            ChatbotConversation conversation = chatbotService.demarrerConversation(
                    request.utilisateurId(),
                    "fr"
            );

            // Envoyer le message de test
            ChatbotMessage reponse = chatbotService.traiterMessageUtilisateur(
                    conversation.getSessionId(),
                    request.message()
            );

            return ResponseEntity.ok(Map.of(
                    "conversation", conversation,
                    "reponse", reponse
            ));

        } catch (Exception e) {
            logger.error("Erreur lors du test chatbot: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    // Records pour les requêtes
    public record DemarrerConversationRequest(
            Long utilisateurId,
            String langue
    ) {}

    public record EnvoyerMessageChatbotRequest(
            String sessionId,
            String contenu
    ) {}

    public record EvaluerMessageRequest(
            boolean estUtile,
            String feedback
    ) {}

    public record EscaladerConversationRequest(
            String raison
    ) {}

    public record TerminerConversationRequest(
            Integer noteSatisfaction,
            String commentaire
    ) {}

    public record TestChatbotRequest(
            Long utilisateurId,
            String message
    ) {}
}
