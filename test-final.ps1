Write-Host "=== TEST INTEGRATION MICROSERVICES ===" -ForegroundColor Green
Write-Host "Club Olympique de Kelibia" -ForegroundColor Cyan
Write-Host ""

Write-Host "ARCHITECTURE TESTEE :" -ForegroundColor Yellow
Write-Host "Frontend (4201) -> Gateway (8080) -> Auth (8081) + Planning (8082)" -ForegroundColor White
Write-Host ""

Write-Host "1. TEST DES SERVICES" -ForegroundColor Yellow
Write-Host "====================" -ForegroundColor Yellow

$services = @(
    "Discovery Service (8761)",
    "Gateway Service (8080)", 
    "Auth User Service (8081)",
    "Planning Performance Service (8082)",
    "Frontend Angular (4201)"
)

$urls = @(
    "http://localhost:8761/actuator/health",
    "http://localhost:8080/actuator/health",
    "http://localhost:8081/actuator/health", 
    "http://localhost:8082/actuator/health",
    "http://localhost:4201"
)

$servicesUp = 0
for ($i = 0; $i -lt $services.Count; $i++) {
    Write-Host "Test de $($services[$i])..." -ForegroundColor Cyan
    
    try {
        $response = Invoke-WebRequest -Uri $urls[$i] -UseBasicParsing -TimeoutSec 5
        if ($response.StatusCode -eq 200) {
            Write-Host "OK - $($services[$i]) operationnel" -ForegroundColor Green
            $servicesUp++
        }
    } catch {
        Write-Host "ERREUR - $($services[$i]) non accessible" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "RESULTAT : $servicesUp/$($services.Count) services operationnels" -ForegroundColor Yellow
Write-Host ""

if ($servicesUp -ge 3) {
    Write-Host "2. TEST AUTHENTIFICATION" -ForegroundColor Yellow
    Write-Host "=========================" -ForegroundColor Yellow
    
    $timestamp = Get-Date -Format "HHmmss"
    $registerData = @{
        username = "testuser_$timestamp"
        email = "test$<EMAIL>"
        password = "TestPassword123!"
        nom = "Test"
        prenom = "User"
        role = "JOUEUR"
    } | ConvertTo-Json
    
    Write-Host "Creation utilisateur..." -ForegroundColor Cyan
    try {
        $registerResponse = Invoke-WebRequest -Uri "http://localhost:8080/auth-user-service/api/auth/register" -Method POST -ContentType "application/json" -Body $registerData -UseBasicParsing -TimeoutSec 10
        
        if ($registerResponse.StatusCode -eq 201) {
            Write-Host "OK - Utilisateur cree" -ForegroundColor Green
            
            Write-Host "Test connexion..." -ForegroundColor Cyan
            $loginData = @{
                username = "testuser_$timestamp"
                password = "TestPassword123!"
            } | ConvertTo-Json
            
            $loginResponse = Invoke-WebRequest -Uri "http://localhost:8080/auth-user-service/api/auth/login" -Method POST -ContentType "application/json" -Body $loginData -UseBasicParsing -TimeoutSec 10
            
            if ($loginResponse.StatusCode -eq 200) {
                Write-Host "OK - Connexion reussie" -ForegroundColor Green
                $loginResult = $loginResponse.Content | ConvertFrom-Json
                $token = $loginResult.token
                Write-Host "OK - Token JWT obtenu" -ForegroundColor Green
                
                Write-Host ""
                Write-Host "3. TEST API PLANNING" -ForegroundColor Yellow
                Write-Host "====================" -ForegroundColor Yellow
                
                $headers = @{
                    "Authorization" = "Bearer $token"
                    "Content-Type" = "application/json"
                }
                
                Write-Host "Test API Entrainements..." -ForegroundColor Cyan
                $entrainementsResponse = Invoke-WebRequest -Uri "http://localhost:8080/planning-performance-service/api/entrainements" -Headers $headers -UseBasicParsing -TimeoutSec 10
                
                if ($entrainementsResponse.StatusCode -eq 200) {
                    Write-Host "OK - API Entrainements accessible" -ForegroundColor Green
                }
                
                Write-Host "Creation entrainement test..." -ForegroundColor Cyan
                $entrainementData = @{
                    titre = "Entrainement Test COK"
                    date = (Get-Date).AddDays(1).ToString("yyyy-MM-dd")
                    heureDebut = "18:00"
                    heureFin = "20:00"
                    lieu = "Gymnase Municipal Kelibia"
                    type = "TECHNIQUE"
                    intensite = 7
                    description = "Test integration microservices"
                } | ConvertTo-Json
                
                $createResponse = Invoke-WebRequest -Uri "http://localhost:8080/planning-performance-service/api/entrainements" -Method POST -Headers $headers -Body $entrainementData -UseBasicParsing -TimeoutSec 10
                
                if ($createResponse.StatusCode -eq 201) {
                    Write-Host "OK - Entrainement cree avec succes" -ForegroundColor Green
                }
            }
        }
    } catch {
        Write-Host "ERREUR - Probleme authentification" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== RESUME ===" -ForegroundColor Green
Write-Host ""

if ($servicesUp -eq $services.Count) {
    Write-Host "SUCCES COMPLET !" -ForegroundColor Green
    Write-Host "Tous les microservices sont operationnels" -ForegroundColor Green
    Write-Host "Integration reussie" -ForegroundColor Green
} elseif ($servicesUp -ge 3) {
    Write-Host "SUCCES PARTIEL" -ForegroundColor Yellow
    Write-Host "Services principaux operationnels" -ForegroundColor Green
} else {
    Write-Host "INTEGRATION INCOMPLETE" -ForegroundColor Red
    Write-Host "Plusieurs services non demarres" -ForegroundColor Red
}

Write-Host ""
Write-Host "URLS DISPONIBLES :" -ForegroundColor Cyan
Write-Host "Frontend : http://localhost:4201" -ForegroundColor White
Write-Host "Gateway : http://localhost:8080" -ForegroundColor White
Write-Host "Discovery : http://localhost:8761" -ForegroundColor White
Write-Host "Auth API : http://localhost:8081" -ForegroundColor White
Write-Host "Planning API : http://localhost:8082" -ForegroundColor White
Write-Host ""

Write-Host "Club Olympique de Kelibia - Test termine !" -ForegroundColor Yellow

Read-Host "Appuyez sur Entree pour continuer"
