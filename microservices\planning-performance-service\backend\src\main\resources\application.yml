spring:
  application:
    name: planning-performance-service

  profiles:
    active: test

  datasource:
    url: jdbc:h2:mem:planning_performance_db
    username: sa
    password:
    driver-class-name: org.h2.Driver
    
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
    defer-datasource-initialization: true

  h2:
    console:
      enabled: true
      path: /h2-console
    
  sql:
    init:
      mode: never

server:
  port: 8082

# Configuration Eureka Client
eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_CLIENT_SERVICE_URL:http://localhost:8761/eureka/}
    register-with-eureka: true
    fetch-registry: true
    registry-fetch-interval-seconds: 30
  instance:
    hostname: ${EUREKA_INSTANCE_HOSTNAME:localhost}
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90
    metadata-map:
      version: "1.0.0"
      description: "Planning Performance Service - Gestion des plannings et performances"
      team: "SprintBot"
  servlet:
    context-path: /

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

logging:
  level:
    com.sprintbot.planningperformance: INFO
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Configuration spécifique au microservice
planning-performance:
  service:
    name: "Planning & Performance Service"
    version: "1.0.0"
    description: "Microservice pour la gestion des entraînements, performances et absences"

---
# Profil de développement
spring:
  config:
    activate:
      on-profile: dev
      
  datasource:
    url: ********************************************************
    username: planning_user
    password: planning_password
    
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: true
    
logging:
  level:
    com.sprintbot.planningperformance: DEBUG
    org.springframework.web: DEBUG

---
# Profil Docker
spring:
  config:
    activate:
      on-profile: docker
      
  datasource:
    url: **********************************************************************
    username: planning_user
    password: planning_password
    
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    
server:
  port: 8082

logging:
  level:
    com.sprintbot.planningperformance: INFO

---
# Profil Docker
spring:
  config:
    activate:
      on-profile: docker

  datasource:
    url: **********************************************************************
    username: planning_user
    password: planning_password

# Configuration Eureka pour Docker
eureka:
  client:
    service-url:
      defaultZone: http://discovery-service:8761/eureka/
  instance:
    hostname: planning-performance-service
    prefer-ip-address: true

---
# Profil de production
spring:
  config:
    activate:
      on-profile: prod

  datasource:
    url: ${DATABASE_URL:**********************************************************************}
    username: ${DATABASE_USERNAME:planning_user}
    password: ${DATABASE_PASSWORD:planning_password}

  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false

server:
  port: ${SERVER_PORT:8082}

# Configuration Eureka pour Production
eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_CLIENT_SERVICE_URL:http://discovery-service:8761/eureka/}
  instance:
    hostname: ${EUREKA_INSTANCE_HOSTNAME:planning-performance-service}
    prefer-ip-address: true

logging:
  level:
    com.sprintbot.planningperformance: WARN
    org.springframework.security: WARN
