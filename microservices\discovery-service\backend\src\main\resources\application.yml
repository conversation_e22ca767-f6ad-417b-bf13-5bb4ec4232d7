# Configuration du Discovery Service (Eureka Server)
# SprintBot - Service de découverte pour l'écosystème microservices

spring:
  application:
    name: discovery-service
  
  # Configuration des profils
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
  
  # Configuration de sécurité
  security:
    user:
      name: ${EUREKA_DASHBOARD_USERNAME:admin}
      password: ${EUREKA_DASHBOARD_PASSWORD:admin123}
      roles: ADMIN

# Configuration du serveur
server:
  port: ${SERVER_PORT:8761}
  servlet:
    context-path: /
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
  http2:
    enabled: true

# Configuration Eureka Server
eureka:
  instance:
    hostname: ${EUREKA_INSTANCE_HOSTNAME:localhost}
    prefer-ip-address: false
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90
    metadata-map:
      version: "1.0.0"
      description: "SprintBot Discovery Service"
      team: "Infrastructure"
  
  client:
    # Le serveur Eureka ne s'enregistre pas lui-même
    register-with-eureka: ${EUREKA_CLIENT_REGISTER_WITH_EUREKA:false}
    fetch-registry: ${EUREKA_CLIENT_FETCH_REGISTRY:false}
    service-url:
      defaultZone: ${EUREKA_CLIENT_SERVICE_URL:http://localhost:8761/eureka/}
    registry-fetch-interval-seconds: 30
  
  server:
    # Configuration du serveur Eureka
    enable-self-preservation: ${EUREKA_SERVER_ENABLE_SELF_PRESERVATION:true}
    eviction-interval-timer-in-ms: ${EUREKA_SERVER_EVICTION_INTERVAL:60000}
    renewal-percent-threshold: ${EUREKA_SERVER_RENEWAL_THRESHOLD:0.85}
    renewal-threshold-update-interval-ms: 900000
    expected-client-renewal-interval-seconds: 30
    
    # Configuration de la réplication peer-to-peer
    peer-eureka-nodes-update-interval-ms: 600000
    peer-eureka-status-refresh-time-interval-ms: 30000
    
    # Configuration du cache de réponse
    response-cache-auto-expiration-in-seconds: 180
    response-cache-update-interval-ms: 30000
    use-read-only-response-cache: true
    
    # Configuration des limites
    max-threads-for-status-replication: 1
    max-threads-for-peer-replication: 20
    
  dashboard:
    enabled: ${EUREKA_DASHBOARD_ENABLED:true}
    path: /

# Configuration du monitoring et des métriques
management:
  endpoints:
    web:
      exposure:
        include: ${MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE:health,info,metrics,env,prometheus}
      base-path: /actuator
  endpoint:
    health:
      show-details: ${MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS:always}
      show-components: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5, 0.95, 0.99
  info:
    env:
      enabled: true
    java:
      enabled: true
    os:
      enabled: true

# Configuration des logs
logging:
  level:
    root: ${LOGGING_LEVEL_ROOT:INFO}
    com.sprintbot: ${LOGGING_LEVEL_SPRINTBOT:INFO}
    com.netflix.eureka: ${LOGGING_LEVEL_EUREKA:INFO}
    com.netflix.discovery: ${LOGGING_LEVEL_DISCOVERY:INFO}
    org.springframework.security: ${LOGGING_LEVEL_SECURITY:INFO}
    org.springframework.web: ${LOGGING_LEVEL_WEB:INFO}
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{36}] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{36}] - %msg%n"
  file:
    name: ${LOGGING_FILE_NAME:logs/discovery-service.log}

# Informations de l'application
info:
  app:
    name: ${spring.application.name}
    description: "Service de découverte Eureka pour l'écosystème SprintBot"
    version: "1.0.0"
    team: "Infrastructure Team"
    contact: "<EMAIL>"
  build:
    artifact: ${project.artifactId:discovery-service}
    name: ${project.name:SprintBot Discovery Service}
    description: ${project.description:Service de découverte Eureka}
    version: ${project.version:1.0.0}
  java:
    version: ${java.version}
    vendor: ${java.vendor}
  os:
    name: ${os.name}
    version: ${os.version}
    arch: ${os.arch}

---
# Profil de développement
spring:
  config:
    activate:
      on-profile: dev

logging:
  level:
    com.sprintbot: DEBUG
    com.netflix.eureka: DEBUG
    com.netflix.discovery: DEBUG

eureka:
  instance:
    hostname: localhost
  server:
    enable-self-preservation: false
    eviction-interval-timer-in-ms: 15000

---
# Profil Docker
spring:
  config:
    activate:
      on-profile: docker

server:
  port: 8761

eureka:
  instance:
    hostname: discovery-service
    prefer-ip-address: true
  client:
    service-url:
      defaultZone: http://discovery-service:8761/eureka/

logging:
  level:
    com.sprintbot: INFO

---
# Profil de production
spring:
  config:
    activate:
      on-profile: prod

server:
  port: 8761
  ssl:
    enabled: ${SSL_ENABLED:false}
    key-store: ${SSL_KEY_STORE:}
    key-store-password: ${SSL_KEY_STORE_PASSWORD:}
    key-store-type: ${SSL_KEY_STORE_TYPE:PKCS12}

eureka:
  instance:
    hostname: ${EUREKA_INSTANCE_HOSTNAME:discovery-service}
    prefer-ip-address: true
    secure-port-enabled: ${SSL_ENABLED:false}
    non-secure-port-enabled: ${SSL_ENABLED:true}
    metadata-map:
      version: "1.0.0"
      environment: "production"
      zone: "primary"
  
  server:
    enable-self-preservation: true
    eviction-interval-timer-in-ms: 60000
    renewal-percent-threshold: 0.85
    
    # Configuration de sécurité renforcée
    peer-node-read-timeout-ms: 1000
    peer-node-connect-timeout-ms: 2000
    peer-node-total-connections: 1000
    peer-node-total-connections-per-host: 500

logging:
  level:
    root: WARN
    com.sprintbot: INFO
    com.netflix.eureka: WARN
  file:
    name: /var/log/discovery-service/application.log
    max-size: 100MB
    max-history: 30

management:
  endpoint:
    health:
      show-details: when-authorized
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
