# Configuration d'environnement pour Gateway Service - SprintBot
# Copiez ce fichier vers .env et adaptez les valeurs selon votre environnement

# ============================================================================
# CONFIGURATION SPRING
# ============================================================================

# Profil Spring actif (dev, docker, prod)
SPRING_PROFILES_ACTIVE=docker

# Port du serveur
SERVER_PORT=8080

# Nom de l'instance pour identification
GATEWAY_INSTANCE_HOSTNAME=gateway-service

# ============================================================================
# CONFIGURATION EUREKA (SERVICE DISCOVERY)
# ============================================================================

# URL du serveur Eureka
EUREKA_CLIENT_SERVICE_URL=http://discovery-service:8761/eureka/

# ============================================================================
# CONFIGURATION REDIS (RATE LIMITING & CACHE)
# ============================================================================

# Hôte Redis
REDIS_HOST=redis

# Port Redis
REDIS_PORT=6379

# Base de données Redis (0-15)
REDIS_DATABASE=0

# Mot de passe Redis (optionnel)
REDIS_PASSWORD=

# ============================================================================
# CONFIGURATION JWT (AUTHENTIFICATION)
# ============================================================================

# Clé secrète pour signer les tokens JWT
# IMPORTANT: Changez cette valeur en production et gardez-la secrète
JWT_SECRET_KEY=SprintBot-Gateway-Secret-Key-2024-Very-Long-And-Secure-Change-In-Production

# Durée de vie des tokens d'accès en millisecondes (24h par défaut)
JWT_EXPIRATION_TIME=86400000

# Durée de vie des tokens de rafraîchissement en millisecondes (7 jours par défaut)
JWT_REFRESH_EXPIRATION_TIME=604800000

# ============================================================================
# CONFIGURATION RATE LIMITING
# ============================================================================

# Nombre de requêtes par seconde autorisées par utilisateur/IP
RATE_LIMIT_REQUESTS_PER_SECOND=100

# Capacité de burst (pic de requêtes autorisé)
RATE_LIMIT_BURST_CAPACITY=200

# Taux de renouvellement des tokens
RATE_LIMIT_REPLENISH_RATE=100

# ============================================================================
# CONFIGURATION DES LOGS
# ============================================================================

# Niveau de log pour le Gateway Service (TRACE, DEBUG, INFO, WARN, ERROR)
LOGGING_LEVEL_GATEWAY=INFO

# Niveau de log pour Spring Cloud Gateway
LOGGING_LEVEL_GATEWAY_FRAMEWORK=INFO

# Niveau de log pour Spring Security
LOGGING_LEVEL_SECURITY=WARN

# Niveau de log pour Resilience4j
LOGGING_LEVEL_RESILIENCE4J=INFO

# Niveau de log pour Netty
LOGGING_LEVEL_NETTY=WARN

# ============================================================================
# CONFIGURATION JVM
# ============================================================================

# Options JVM pour optimiser les performances
JAVA_OPTS=-Xms512m -Xmx1024m -XX:+UseG1GC -XX:G1HeapRegionSize=16m -XX:+UseStringDeduplication

# ============================================================================
# CONFIGURATION DOCKER
# ============================================================================

# Date de build (automatique)
BUILD_DATE=

# Référence VCS (automatique)
VCS_REF=

# ============================================================================
# CONFIGURATION MONITORING
# ============================================================================

# Activer les métriques Prometheus
METRICS_PROMETHEUS_ENABLED=true

# Activer le tracing distribué
TRACING_ENABLED=true

# URL Zipkin pour le tracing (optionnel)
ZIPKIN_BASE_URL=http://zipkin:9411

# ============================================================================
# CONFIGURATION SÉCURITÉ (PRODUCTION)
# ============================================================================

# Activer HTTPS en production
SECURITY_REQUIRE_SSL=false

# Domaines autorisés pour CORS (séparés par des virgules)
CORS_ALLOWED_ORIGINS=http://localhost:4200,https://sprintbot.com

# Headers de sécurité
SECURITY_HEADERS_ENABLED=true

# ============================================================================
# CONFIGURATION CIRCUIT BREAKER
# ============================================================================

# Seuil de taux d'échec pour ouvrir le circuit breaker (%)
CIRCUIT_BREAKER_FAILURE_RATE_THRESHOLD=50

# Nombre minimum d'appels avant évaluation
CIRCUIT_BREAKER_MINIMUM_NUMBER_OF_CALLS=5

# Durée d'attente en état ouvert (secondes)
CIRCUIT_BREAKER_WAIT_DURATION_IN_OPEN_STATE=5

# ============================================================================
# CONFIGURATION RETRY
# ============================================================================

# Nombre maximum de tentatives
RETRY_MAX_ATTEMPTS=3

# Délai d'attente initial (millisecondes)
RETRY_WAIT_DURATION=100

# Multiplicateur pour le backoff exponentiel
RETRY_EXPONENTIAL_BACKOFF_MULTIPLIER=2

# ============================================================================
# CONFIGURATION TIMEOUTS
# ============================================================================

# Timeout de connexion HTTP (millisecondes)
HTTP_CLIENT_CONNECT_TIMEOUT=5000

# Timeout de réponse HTTP (secondes)
HTTP_CLIENT_RESPONSE_TIMEOUT=30

# ============================================================================
# CONFIGURATION BASE DE DONNÉES (SI NÉCESSAIRE)
# ============================================================================

# URL de la base de données (pour les sessions, etc.)
# DATABASE_URL=*************************************************

# Utilisateur de la base de données
# DATABASE_USERNAME=gateway_user

# Mot de passe de la base de données
# DATABASE_PASSWORD=gateway_password

# ============================================================================
# CONFIGURATION DÉVELOPPEMENT
# ============================================================================

# Activer le rechargement automatique en développement
SPRING_DEVTOOLS_RESTART_ENABLED=true

# Activer le debug en développement
DEBUG_MODE=false

# ============================================================================
# CONFIGURATION TESTS
# ============================================================================

# URL pour les tests d'intégration
TEST_GATEWAY_URL=http://localhost:8080

# Token de test (ne pas utiliser en production)
TEST_JWT_TOKEN=

# ============================================================================
# NOTES IMPORTANTES
# ============================================================================

# 1. Changez TOUJOURS JWT_SECRET_KEY en production
# 2. Utilisez des mots de passe forts pour Redis et les bases de données
# 3. Configurez CORS_ALLOWED_ORIGINS selon vos domaines
# 4. Activez HTTPS en production (SECURITY_REQUIRE_SSL=true)
# 5. Surveillez les logs et métriques en production
# 6. Sauvegardez régulièrement la configuration Redis
# 7. Testez les circuit breakers et retry en conditions réelles
