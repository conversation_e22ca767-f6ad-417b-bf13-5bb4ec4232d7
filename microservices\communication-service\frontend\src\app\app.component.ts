import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';

import { WebSocketService } from './services/websocket.service';
import { NotificationService } from './services/notification.service';
import { UserPresenceService } from './services/user-presence.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit, OnDestroy {
  title = 'Communication Service';
  
  // État de l'application
  isConnected = false;
  notificationsCount = 0;
  currentUser: any = null;
  
  // Subscriptions
  private subscriptions: Subscription[] = [];

  // Navigation
  navigationItems = [
    {
      label: 'Conversations',
      icon: 'chat',
      route: '/conversations',
      badge: 0
    },
    {
      label: 'Notifications',
      icon: 'notifications',
      route: '/notifications',
      badge: 0
    },
    {
      label: 'Chatbot',
      icon: 'smart_toy',
      route: '/chatbot',
      badge: 0
    },
    {
      label: 'Paramètres',
      icon: 'settings',
      route: '/settings',
      badge: 0
    }
  ];

  constructor(
    private router: Router,
    private webSocketService: WebSocketService,
    private notificationService: NotificationService,
    private userPresenceService: UserPresenceService
  ) {}

  ngOnInit(): void {
    this.initializeApplication();
    this.setupSubscriptions();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.webSocketService.disconnect();
    this.userPresenceService.deconnecterPresence();
  }

  private initializeApplication(): void {
    // Simuler un utilisateur connecté (à remplacer par l'authentification réelle)
    this.currentUser = {
      id: 1,
      nom: 'Utilisateur Test',
      email: '<EMAIL>'
    };

    // Initialiser les services
    this.initializeWebSocket();
    this.initializePresence();
    this.loadNotifications();
  }

  private initializeWebSocket(): void {
    if (this.currentUser) {
      this.webSocketService.connect(this.currentUser.id);
    }
  }

  private initializePresence(): void {
    if (this.currentUser) {
      this.userPresenceService.initialiserPresence(this.currentUser.id).subscribe({
        next: (presence) => {
          console.log('Présence initialisée:', presence);
        },
        error: (error) => {
          console.error('Erreur initialisation présence:', error);
        }
      });
    }
  }

  private loadNotifications(): void {
    if (this.currentUser) {
      this.notificationService.obtenirNotificationsUtilisateur(this.currentUser.id).subscribe({
        next: (response) => {
          console.log('Notifications chargées:', response);
        },
        error: (error) => {
          console.error('Erreur chargement notifications:', error);
        }
      });
    }
  }

  private setupSubscriptions(): void {
    // Connexion WebSocket
    const connectionSub = this.webSocketService.isConnected().subscribe(
      connected => {
        this.isConnected = connected;
        if (connected) {
          console.log('WebSocket connecté');
        } else {
          console.log('WebSocket déconnecté');
        }
      }
    );
    this.subscriptions.push(connectionSub);

    // Notifications non lues
    const notificationsSub = this.notificationService.getNotificationsNonLues().subscribe(
      count => {
        this.notificationsCount = count;
        this.updateNavigationBadge('notifications', count);
      }
    );
    this.subscriptions.push(notificationsSub);

    // Messages WebSocket
    const messagesSub = this.webSocketService.getMessages().subscribe(
      message => {
        this.handleWebSocketMessage(message);
      }
    );
    this.subscriptions.push(messagesSub);
  }

  private handleWebSocketMessage(message: any): void {
    console.log('Message WebSocket reçu:', message);
    
    switch (message.type) {
      case 'MESSAGE':
        this.handleNewMessage(message.data);
        break;
      case 'NOTIFICATION':
        this.handleNewNotification(message.data);
        break;
      case 'REACTION':
        this.handleMessageReaction(message.data);
        break;
      case 'READ':
        this.handleMessageRead(message.data);
        break;
    }
  }

  private handleNewMessage(messageData: any): void {
    // Mettre à jour le badge des conversations
    const conversationsItem = this.navigationItems.find(item => item.route === '/conversations');
    if (conversationsItem) {
      conversationsItem.badge++;
    }
  }

  private handleNewNotification(notificationData: any): void {
    // Les notifications sont déjà gérées par le service
    console.log('Nouvelle notification:', notificationData);
  }

  private handleMessageReaction(reactionData: any): void {
    console.log('Nouvelle réaction:', reactionData);
  }

  private handleMessageRead(readData: any): void {
    console.log('Message lu:', readData);
  }

  private updateNavigationBadge(route: string, count: number): void {
    const item = this.navigationItems.find(item => item.route.includes(route));
    if (item) {
      item.badge = count;
    }
  }

  // Actions de navigation
  navigateTo(route: string): void {
    this.router.navigate([route]);
  }

  // Actions utilisateur
  onUserMenuClick(): void {
    // Ouvrir le menu utilisateur
    console.log('Menu utilisateur cliqué');
  }

  onNotificationClick(): void {
    this.router.navigate(['/notifications']);
  }

  onSettingsClick(): void {
    this.router.navigate(['/settings']);
  }

  // Gestion de la présence
  changeUserStatus(status: string): void {
    this.userPresenceService.mettreAJourStatut(status).subscribe({
      next: (presence) => {
        console.log('Statut mis à jour:', presence);
      },
      error: (error) => {
        console.error('Erreur mise à jour statut:', error);
      }
    });
  }

  // Utilitaires
  getConnectionStatusIcon(): string {
    return this.isConnected ? 'wifi' : 'wifi_off';
  }

  getConnectionStatusColor(): string {
    return this.isConnected ? 'primary' : 'warn';
  }

  getTotalBadgeCount(): number {
    return this.navigationItems.reduce((total, item) => total + item.badge, 0);
  }
}
