package com.sprintbot.communication.controller;

import com.sprintbot.communication.entity.StatutPresence;
import com.sprintbot.communication.entity.UserPresence;
import com.sprintbot.communication.service.UserPresenceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Contrôleur REST pour la gestion de la présence des utilisateurs
 */
@RestController
@RequestMapping("/api/presence")
@CrossOrigin(origins = {"http://localhost:4200", "http://localhost:4204"})
public class UserPresenceController {

    private static final Logger logger = LoggerFactory.getLogger(UserPresenceController.class);

    @Autowired
    private UserPresenceService presenceService;

    /**
     * Met à jour le statut de présence d'un utilisateur
     */
    @PostMapping("/statut")
    public ResponseEntity<?> mettreAJourStatut(@Valid @RequestBody MettreAJourStatutRequest request) {
        try {
            UserPresence presence = presenceService.mettreAJourPresence(
                    request.utilisateurId(),
                    request.statut(),
                    request.sessionId(),
                    request.plateforme(),
                    request.estMobile()
            );

            return ResponseEntity.ok(presence);

        } catch (Exception e) {
            logger.error("Erreur lors de la mise à jour du statut: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Définit un utilisateur comme en ligne
     */
    @PostMapping("/en-ligne")
    public ResponseEntity<?> definirEnLigne(@Valid @RequestBody ConnexionRequest request) {
        try {
            UserPresence presence = presenceService.definirEnLigne(
                    request.utilisateurId(),
                    request.sessionId(),
                    request.plateforme(),
                    request.estMobile()
            );

            return ResponseEntity.ok(presence);

        } catch (Exception e) {
            logger.error("Erreur lors de la connexion: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Définit un utilisateur comme hors ligne
     */
    @PostMapping("/hors-ligne")
    public ResponseEntity<?> definirHorsLigne(@RequestParam Long utilisateurId) {
        try {
            presenceService.definirHorsLigne(utilisateurId);

            return ResponseEntity.ok(Map.of("message", "Utilisateur défini comme hors ligne"));

        } catch (Exception e) {
            logger.error("Erreur lors de la déconnexion: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Met à jour l'activité d'un utilisateur
     */
    @PostMapping("/activite")
    public ResponseEntity<?> mettreAJourActivite(@RequestParam Long utilisateurId) {
        try {
            presenceService.mettreAJourActivite(utilisateurId);

            return ResponseEntity.ok(Map.of("message", "Activité mise à jour"));

        } catch (Exception e) {
            logger.error("Erreur lors de la mise à jour d'activité: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Définit un message de statut personnalisé
     */
    @PostMapping("/message-statut")
    public ResponseEntity<?> definirMessageStatut(@RequestBody MessageStatutRequest request) {
        try {
            presenceService.definirMessageStatut(request.utilisateurId(), request.message());

            return ResponseEntity.ok(Map.of("message", "Message de statut mis à jour"));

        } catch (Exception e) {
            logger.error("Erreur lors de la définition du message de statut: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Active/désactive le mode invisible
     */
    @PostMapping("/invisible")
    public ResponseEntity<?> toggleModeInvisible(@RequestParam Long utilisateurId) {
        try {
            presenceService.toggleModeInvisible(utilisateurId);

            return ResponseEntity.ok(Map.of("message", "Mode invisible mis à jour"));

        } catch (Exception e) {
            logger.error("Erreur lors du toggle mode invisible: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Obtient la présence d'un utilisateur
     */
    @GetMapping("/utilisateur/{utilisateurId}")
    public ResponseEntity<?> obtenirPresence(@PathVariable Long utilisateurId) {
        try {
            Optional<UserPresence> presence = presenceService.obtenirPresence(utilisateurId);

            if (presence.isPresent()) {
                return ResponseEntity.ok(presence.get());
            } else {
                return ResponseEntity.ok(Map.of("message", "Aucune présence trouvée"));
            }

        } catch (Exception e) {
            logger.error("Erreur lors de l'obtention de la présence: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Obtient les utilisateurs en ligne
     */
    @GetMapping("/en-ligne")
    public ResponseEntity<?> obtenirUtilisateursEnLigne() {
        try {
            List<UserPresence> utilisateurs = presenceService.obtenirUtilisateursEnLigne();

            return ResponseEntity.ok(utilisateurs);

        } catch (Exception e) {
            logger.error("Erreur lors de l'obtention des utilisateurs en ligne: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Obtient les utilisateurs actifs récemment
     */
    @GetMapping("/actifs-recemment")
    public ResponseEntity<?> obtenirUtilisateursActifsRecemment(
            @RequestParam(defaultValue = "60") int minutes) {
        try {
            List<UserPresence> utilisateurs = presenceService.obtenirUtilisateursActifsRecemment(minutes);

            return ResponseEntity.ok(utilisateurs);

        } catch (Exception e) {
            logger.error("Erreur lors de l'obtention des utilisateurs actifs: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Compte les utilisateurs en ligne
     */
    @GetMapping("/count")
    public ResponseEntity<?> compterUtilisateursEnLigne() {
        try {
            Long count = presenceService.compterUtilisateursEnLigne();

            return ResponseEntity.ok(Map.of("count", count));

        } catch (Exception e) {
            logger.error("Erreur lors du comptage des utilisateurs en ligne: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Obtient les statistiques de présence
     */
    @GetMapping("/statistiques")
    public ResponseEntity<?> obtenirStatistiquesPresence() {
        try {
            List<Object[]> stats = presenceService.obtenirStatistiquesPresence();

            return ResponseEntity.ok(stats);

        } catch (Exception e) {
            logger.error("Erreur lors de l'obtention des statistiques de présence: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Obtient les utilisateurs par plateforme
     */
    @GetMapping("/plateforme/{plateforme}")
    public ResponseEntity<?> obtenirUtilisateursParPlateforme(@PathVariable String plateforme) {
        try {
            List<UserPresence> utilisateurs = presenceService.obtenirUtilisateursParPlateforme(plateforme);

            return ResponseEntity.ok(utilisateurs);

        } catch (Exception e) {
            logger.error("Erreur lors de l'obtention des utilisateurs par plateforme: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Obtient les utilisateurs mobiles en ligne
     */
    @GetMapping("/mobiles")
    public ResponseEntity<?> obtenirUtilisateursMobilesEnLigne() {
        try {
            List<UserPresence> utilisateurs = presenceService.obtenirUtilisateursMobilesEnLigne();

            return ResponseEntity.ok(utilisateurs);

        } catch (Exception e) {
            logger.error("Erreur lors de l'obtention des utilisateurs mobiles: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Déconnecte un utilisateur par session ID
     */
    @PostMapping("/deconnecter-session")
    public ResponseEntity<?> deconnecterParSession(@RequestParam String sessionId) {
        try {
            presenceService.deconnecterParSession(sessionId);

            return ResponseEntity.ok(Map.of("message", "Session déconnectée"));

        } catch (Exception e) {
            logger.error("Erreur lors de la déconnexion par session: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Force le nettoyage des utilisateurs inactifs
     */
    @PostMapping("/nettoyer-inactifs")
    public ResponseEntity<?> nettoyerUtilisateursInactifs() {
        try {
            presenceService.nettoyerUtilisateursInactifs();

            return ResponseEntity.ok(Map.of("message", "Nettoyage effectué"));

        } catch (Exception e) {
            logger.error("Erreur lors du nettoyage des utilisateurs inactifs: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    // Records pour les requêtes
    public record MettreAJourStatutRequest(
            Long utilisateurId,
            StatutPresence statut,
            String sessionId,
            String plateforme,
            boolean estMobile
    ) {}

    public record ConnexionRequest(
            Long utilisateurId,
            String sessionId,
            String plateforme,
            boolean estMobile
    ) {}

    public record MessageStatutRequest(
            Long utilisateurId,
            String message
    ) {}
}
