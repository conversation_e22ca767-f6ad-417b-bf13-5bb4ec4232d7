# 🚀 Guide de Déploiement - Discovery Service

## Vue d'ensemble

Le **Discovery Service** est le service de découverte Eureka pour l'écosystème SprintBot. Il permet aux microservices de s'enregistrer automatiquement et de se découvrir mutuellement sans configuration statique.

## 📋 Prérequis

### Logiciels requis
- **Docker** 20.10+
- **Docker Compose** 2.0+
- **Java** 21+ (pour le développement local)
- **Maven** 3.9+ (pour le build local)

### Ressources système recommandées
- **CPU**: 1 core minimum, 2 cores recommandés
- **RAM**: 512MB minimum, 1GB recommandé
- **Stockage**: 2GB minimum pour les logs et cache
- **Réseau**: Port 8761 disponible

## 🔧 Configuration

### Variables d'environnement

Copiez le fichier `.env.example` vers `.env` et ajustez les valeurs :

```bash
cp .env.example .env
```

#### Variables critiques à configurer :

```bash
# Configuration de base
SPRING_PROFILES_ACTIVE=docker
SERVER_PORT=8761
EUREKA_INSTANCE_HOSTNAME=discovery-service

# Sécurité du dashboard
EUREKA_DASHBOARD_USERNAME=admin
EUREKA_DASHBOARD_PASSWORD=VotreMotDePasseSecurise

# Configuration JVM
JAVA_OPTS=-Xms512m -Xmx1024m -XX:+UseG1GC
```

## 🚀 Déploiement

### 1. Déploiement avec Docker Compose (Recommandé)

#### Déploiement simple
```bash
# Démarrage du service
docker-compose up -d discovery-service

# Vérification des logs
docker-compose logs -f discovery-service

# Vérification du statut
docker-compose ps discovery-service
```

#### Déploiement avec build
```bash
# Build et démarrage
docker-compose up -d --build discovery-service

# Suivi des logs en temps réel
docker-compose logs -f discovery-service
```

### 2. Déploiement Docker manuel

#### Build de l'image
```bash
cd backend
docker build -t sprintbot/discovery-service:latest .
```

#### Exécution du conteneur
```bash
docker run -d \
  --name sprintbot-discovery-service \
  --hostname discovery-service \
  -p 8761:8761 \
  -e SPRING_PROFILES_ACTIVE=docker \
  -e EUREKA_INSTANCE_HOSTNAME=discovery-service \
  -v discovery_logs:/app/logs \
  sprintbot/discovery-service:latest
```

### 3. Déploiement local (Développement)

#### Avec Maven
```bash
cd backend
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

#### Avec Java
```bash
cd backend
mvn clean package -DskipTests
java -jar target/discovery-service.jar --spring.profiles.active=dev
```

## 🔍 Validation du déploiement

### Scripts de validation automatique

```bash
# Validation complète
./validate-discovery.sh --full

# Validation rapide
./validate-discovery.sh --quick

# Test du service en cours
./validate-discovery.sh --service-only
```

### Vérifications manuelles

#### 1. Health Check
```bash
curl http://localhost:8761/actuator/health
```

Réponse attendue :
```json
{
  "status": "UP",
  "components": {
    "diskSpace": {"status": "UP"},
    "ping": {"status": "UP"}
  }
}
```

#### 2. Dashboard Eureka
- URL : http://localhost:8761
- Utilisateur : admin
- Mot de passe : (configuré dans .env)

#### 3. Registry des services
```bash
curl http://localhost:8761/eureka/apps
```

#### 4. Métriques Prometheus
```bash
curl http://localhost:8761/actuator/prometheus
```

## 🔧 Configuration avancée

### Configuration SSL (Production)

```yaml
# Dans application.yml ou via variables d'environnement
server:
  ssl:
    enabled: true
    key-store: classpath:keystore.p12
    key-store-password: ${SSL_KEY_STORE_PASSWORD}
    key-store-type: PKCS12
```

### Configuration haute disponibilité

Pour un déploiement en cluster :

```yaml
eureka:
  client:
    register-with-eureka: true
    fetch-registry: true
    service-url:
      defaultZone: http://eureka1:8761/eureka/,http://eureka2:8761/eureka/
```

### Configuration de sécurité renforcée

```yaml
spring:
  security:
    user:
      name: ${EUREKA_ADMIN_USER}
      password: ${EUREKA_ADMIN_PASSWORD}
      roles: ADMIN

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
```

## 📊 Monitoring et observabilité

### Endpoints de monitoring

| Endpoint | Description | Authentification |
|----------|-------------|------------------|
| `/actuator/health` | État de santé | Non |
| `/actuator/info` | Informations du service | Non |
| `/actuator/metrics` | Métriques détaillées | Oui |
| `/actuator/prometheus` | Métriques Prometheus | Oui |
| `/eureka/apps` | Registry des services | Non |

### Intégration Prometheus

Ajoutez cette configuration à votre `prometheus.yml` :

```yaml
scrape_configs:
  - job_name: 'discovery-service'
    static_configs:
      - targets: ['discovery-service:8761']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 15s
```

### Alertes recommandées

```yaml
# Alertes Prometheus
groups:
  - name: discovery-service
    rules:
      - alert: DiscoveryServiceDown
        expr: up{job="discovery-service"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Discovery Service is down"
          
      - alert: HighMemoryUsage
        expr: jvm_memory_used_bytes{job="discovery-service"} / jvm_memory_max_bytes > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage on Discovery Service"
```

## 🔄 Mise à jour et maintenance

### Mise à jour du service

```bash
# Arrêt du service
docker-compose stop discovery-service

# Mise à jour de l'image
docker-compose pull discovery-service

# Redémarrage
docker-compose up -d discovery-service

# Vérification
./validate-discovery.sh --service-only
```

### Sauvegarde

```bash
# Sauvegarde des logs
docker cp sprintbot-discovery-service:/app/logs ./backup/logs-$(date +%Y%m%d)

# Sauvegarde de la configuration
docker cp sprintbot-discovery-service:/app/config ./backup/config-$(date +%Y%m%d)
```

### Nettoyage

```bash
# Nettoyage des logs anciens
docker exec sprintbot-discovery-service find /app/logs -name "*.log" -mtime +30 -delete

# Nettoyage des images Docker
docker image prune -f
```

## 🐛 Dépannage

### Problèmes courants

#### 1. Service ne démarre pas
```bash
# Vérifier les logs
docker-compose logs discovery-service

# Vérifier la configuration
docker-compose config

# Vérifier les ports
netstat -tulpn | grep 8761
```

#### 2. Dashboard inaccessible
- Vérifier l'authentification (admin/password)
- Vérifier la configuration de sécurité
- Vérifier les logs d'erreur

#### 3. Services ne s'enregistrent pas
- Vérifier la connectivité réseau
- Vérifier la configuration Eureka des clients
- Vérifier les logs du Discovery Service

### Logs utiles

```bash
# Logs en temps réel
docker-compose logs -f discovery-service

# Logs avec timestamp
docker-compose logs -t discovery-service

# Dernières 100 lignes
docker-compose logs --tail=100 discovery-service
```

### Commandes de diagnostic

```bash
# État du conteneur
docker inspect sprintbot-discovery-service

# Utilisation des ressources
docker stats sprintbot-discovery-service

# Processus dans le conteneur
docker exec sprintbot-discovery-service ps aux

# Test de connectivité
docker exec sprintbot-discovery-service curl -f http://localhost:8761/actuator/health
```

## 🔐 Sécurité

### Bonnes pratiques

1. **Changez les mots de passe par défaut**
2. **Utilisez HTTPS en production**
3. **Limitez l'exposition des endpoints**
4. **Surveillez les logs d'accès**
5. **Mettez à jour régulièrement**

### Configuration firewall

```bash
# Autoriser uniquement le port 8761
ufw allow 8761/tcp

# Limiter l'accès au dashboard
ufw allow from 10.0.0.0/8 to any port 8761
```

## 📞 Support

### Contacts
- **Équipe Infrastructure** : <EMAIL>
- **Documentation** : [Wiki SprintBot](https://wiki.sprintbot.com)
- **Issues** : [GitHub Issues](https://github.com/sprintbot/issues)

### Ressources utiles
- [Documentation Spring Cloud Netflix](https://spring.io/projects/spring-cloud-netflix)
- [Guide Eureka Server](https://cloud.spring.io/spring-cloud-netflix/reference/html/#spring-cloud-eureka-server)
- [Monitoring avec Actuator](https://docs.spring.io/spring-boot/docs/current/reference/html/actuator.html)
