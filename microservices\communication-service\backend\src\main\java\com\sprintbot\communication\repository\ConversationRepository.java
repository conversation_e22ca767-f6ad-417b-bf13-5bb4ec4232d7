package com.sprintbot.communication.repository;

import com.sprintbot.communication.entity.Conversation;
import com.sprintbot.communication.entity.StatutConversation;
import com.sprintbot.communication.entity.TypeConversation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository pour l'entité Conversation
 */
@Repository
public interface ConversationRepository extends JpaRepository<Conversation, Long> {

    /**
     * Trouve les conversations d'un utilisateur
     */
    @Query("SELECT DISTINCT c FROM Conversation c " +
           "JOIN c.participants p " +
           "WHERE p.utilisateurId = :utilisateurId " +
           "AND c.statut = :statut " +
           "ORDER BY c.derniereActivite DESC")
    Page<Conversation> findByUtilisateurIdAndStatut(
            @Param("utilisateurId") Long utilisateurId,
            @Param("statut") StatutConversation statut,
            Pageable pageable);

    /**
     * Trouve toutes les conversations d'un utilisateur
     */
    @Query("SELECT DISTINCT c FROM Conversation c " +
           "JOIN c.participants p " +
           "WHERE p.utilisateurId = :utilisateurId " +
           "ORDER BY c.derniereActivite DESC")
    Page<Conversation> findByUtilisateurId(
            @Param("utilisateurId") Long utilisateurId,
            Pageable pageable);

    /**
     * Trouve les conversations par type
     */
    @Query("SELECT DISTINCT c FROM Conversation c " +
           "JOIN c.participants p " +
           "WHERE p.utilisateurId = :utilisateurId " +
           "AND c.type = :type " +
           "AND c.statut = :statut " +
           "ORDER BY c.derniereActivite DESC")
    List<Conversation> findByUtilisateurIdAndTypeAndStatut(
            @Param("utilisateurId") Long utilisateurId,
            @Param("type") TypeConversation type,
            @Param("statut") StatutConversation statut);

    /**
     * Trouve une conversation privée entre deux utilisateurs
     */
    @Query("SELECT c FROM Conversation c " +
           "WHERE c.type = 'PRIVE' " +
           "AND c.statut = 'ACTIF' " +
           "AND c.id IN (" +
           "    SELECT c1.id FROM Conversation c1 " +
           "    JOIN c1.participants p1 " +
           "    WHERE p1.utilisateurId = :utilisateur1Id " +
           "    AND c1.id IN (" +
           "        SELECT c2.id FROM Conversation c2 " +
           "        JOIN c2.participants p2 " +
           "        WHERE p2.utilisateurId = :utilisateur2Id" +
           "    )" +
           ")")
    Optional<Conversation> findConversationPriveeEntreUtilisateurs(
            @Param("utilisateur1Id") Long utilisateur1Id,
            @Param("utilisateur2Id") Long utilisateur2Id);

    /**
     * Trouve les conversations épinglées d'un utilisateur
     */
    @Query("SELECT DISTINCT c FROM Conversation c " +
           "JOIN c.participants p " +
           "WHERE p.utilisateurId = :utilisateurId " +
           "AND p.estEpinglee = true " +
           "AND c.statut = 'ACTIF' " +
           "ORDER BY c.derniereActivite DESC")
    List<Conversation> findConversationsEpinglees(@Param("utilisateurId") Long utilisateurId);

    /**
     * Recherche de conversations par nom
     */
    @Query("SELECT DISTINCT c FROM Conversation c " +
           "JOIN c.participants p " +
           "WHERE p.utilisateurId = :utilisateurId " +
           "AND LOWER(c.nom) LIKE LOWER(CONCAT('%', :recherche, '%')) " +
           "AND c.statut = 'ACTIF' " +
           "ORDER BY c.derniereActivite DESC")
    List<Conversation> rechercherConversations(
            @Param("utilisateurId") Long utilisateurId,
            @Param("recherche") String recherche);

    /**
     * Trouve les conversations avec des messages non lus
     */
    @Query("SELECT DISTINCT c FROM Conversation c " +
           "JOIN c.participants p " +
           "JOIN c.messages m " +
           "WHERE p.utilisateurId = :utilisateurId " +
           "AND c.statut = 'ACTIF' " +
           "AND m.dateEnvoi > p.dateDerniereLecture " +
           "AND m.expediteurId != :utilisateurId " +
           "ORDER BY c.derniereActivite DESC")
    List<Conversation> findConversationsAvecMessagesNonLus(@Param("utilisateurId") Long utilisateurId);

    /**
     * Compte les conversations actives d'un utilisateur
     */
    @Query("SELECT COUNT(DISTINCT c) FROM Conversation c " +
           "JOIN c.participants p " +
           "WHERE p.utilisateurId = :utilisateurId " +
           "AND c.statut = 'ACTIF'")
    Long countConversationsActives(@Param("utilisateurId") Long utilisateurId);

    /**
     * Trouve les conversations créées par un utilisateur
     */
    List<Conversation> findByCreateurIdAndStatutOrderByDateCreationDesc(
            Long createurId, StatutConversation statut);

    /**
     * Trouve les conversations modifiées après une date
     */
    @Query("SELECT DISTINCT c FROM Conversation c " +
           "JOIN c.participants p " +
           "WHERE p.utilisateurId = :utilisateurId " +
           "AND c.dateModification > :depuis " +
           "ORDER BY c.dateModification DESC")
    List<Conversation> findConversationsModifieesDepuis(
            @Param("utilisateurId") Long utilisateurId,
            @Param("depuis") LocalDateTime depuis);

    /**
     * Trouve les conversations par type et statut
     */
    List<Conversation> findByTypeAndStatutOrderByDerniereActiviteDesc(
            TypeConversation type, StatutConversation statut);

    /**
     * Supprime les conversations inactives anciennes
     */
    @Query("DELETE FROM Conversation c " +
           "WHERE c.statut = 'ARCHIVE' " +
           "AND c.dateModification < :dateLimit")
    void supprimerConversationsArchiveesAnciennes(@Param("dateLimit") LocalDateTime dateLimit);

    /**
     * Met à jour la dernière activité d'une conversation
     */
    @Query("UPDATE Conversation c " +
           "SET c.derniereActivite = :maintenant " +
           "WHERE c.id = :conversationId")
    void mettreAJourDerniereActivite(
            @Param("conversationId") Long conversationId,
            @Param("maintenant") LocalDateTime maintenant);

    /**
     * Trouve les conversations les plus actives
     */
    @Query("SELECT c FROM Conversation c " +
           "WHERE c.statut = 'ACTIF' " +
           "AND c.derniereActivite > :depuis " +
           "ORDER BY c.nombreMessages DESC")
    List<Conversation> findConversationsLesPlusActives(
            @Param("depuis") LocalDateTime depuis,
            Pageable pageable);
}
