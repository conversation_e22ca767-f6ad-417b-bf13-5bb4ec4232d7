#!/bin/sh
# Script d'entrée pour le frontend Communication Service

set -e

echo "🚀 Démarrage du frontend Communication Service..."

# Variables d'environnement avec valeurs par défaut
API_URL=${API_URL:-"http://localhost:8084"}
NGINX_PORT=${NGINX_PORT:-"80"}

echo "📡 Configuration API URL: $API_URL"
echo "🌐 Port Nginx: $NGINX_PORT"

# Remplacer les variables d'environnement dans la configuration Nginx
echo "🔧 Configuration de Nginx..."

# Remplacer API_URL dans la configuration
sed -i "s|\${API_URL}|$API_URL|g" /etc/nginx/conf.d/default.conf

# Remplacer le port si nécessaire
if [ "$NGINX_PORT" != "80" ]; then
    sed -i "s|listen 80|listen $NGINX_PORT|g" /etc/nginx/conf.d/default.conf
    sed -i "s|listen \[::\]:80|listen [::]:$NGINX_PORT|g" /etc/nginx/conf.d/default.conf
fi

# Vérifier la configuration Nginx
echo "✅ Vérification de la configuration Nginx..."
nginx -t

if [ $? -eq 0 ]; then
    echo "✅ Configuration Nginx valide"
else
    echo "❌ Erreur dans la configuration Nginx"
    exit 1
fi

# Créer les répertoires de logs si nécessaire
mkdir -p /var/log/nginx
chown -R nginx:nginx /var/log/nginx

# Définir les permissions sur les fichiers statiques
echo "🔐 Configuration des permissions..."
chown -R nginx:nginx /usr/share/nginx/html
chmod -R 755 /usr/share/nginx/html

# Afficher les informations de démarrage
echo "📋 Informations de démarrage:"
echo "   - Version Nginx: $(nginx -v 2>&1)"
echo "   - Répertoire web: /usr/share/nginx/html"
echo "   - Configuration: /etc/nginx/conf.d/default.conf"
echo "   - Logs: /var/log/nginx/"

# Lister les fichiers dans le répertoire web
echo "📁 Fichiers disponibles:"
ls -la /usr/share/nginx/html/

# Vérifier que index.html existe
if [ ! -f "/usr/share/nginx/html/index.html" ]; then
    echo "⚠️  Attention: index.html non trouvé!"
    echo "📁 Contenu du répertoire:"
    find /usr/share/nginx/html -type f -name "*.html" | head -10
fi

# Test de connectivité vers l'API (optionnel)
if command -v curl >/dev/null 2>&1; then
    echo "🔍 Test de connectivité vers l'API..."
    if curl -s --connect-timeout 5 "$API_URL/actuator/health" >/dev/null 2>&1; then
        echo "✅ API accessible à $API_URL"
    else
        echo "⚠️  API non accessible à $API_URL (normal au démarrage)"
    fi
fi

echo "🎯 Frontend Communication Service prêt!"
echo "🌐 Accès: http://localhost:$NGINX_PORT"

# Exécuter la commande passée en paramètre
exec "$@"
