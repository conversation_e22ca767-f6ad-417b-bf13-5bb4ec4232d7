import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { DashboardStats } from '@core/models/common.model';
import { DonneesSanteService } from '@core/services/donnees-sante.service';
import { RendezVousService } from '@core/services/rendez-vous.service';
import { DemandesAdministrativesService } from '@core/services/demandes-administratives.service';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="dashboard">
      <!-- Header -->
      <div class="dashboard-header mb-4">
        <h1 class="h3 mb-0">Tableau de bord médical</h1>
        <p class="text-muted">Vue d'ensemble des activités médicales et administratives</p>
      </div>

      <!-- Statistiques principales -->
      <div class="row mb-4">
        <div class="col-md-3 mb-3">
          <div class="stat-card bg-primary">
            <div class="stat-number">{{ stats.totalDonneesSante }}</div>
            <div class="stat-label">Données de santé</div>
          </div>
        </div>
        <div class="col-md-3 mb-3">
          <div class="stat-card bg-success">
            <div class="stat-number">{{ stats.totalRendezVous }}</div>
            <div class="stat-label">Rendez-vous</div>
          </div>
        </div>
        <div class="col-md-3 mb-3">
          <div class="stat-card bg-warning">
            <div class="stat-number">{{ stats.totalDemandes }}</div>
            <div class="stat-label">Demandes admin</div>
          </div>
        </div>
        <div class="col-md-3 mb-3">
          <div class="stat-card bg-danger">
            <div class="stat-number">{{ stats.alertesActives }}</div>
            <div class="stat-label">Alertes actives</div>
          </div>
        </div>
      </div>

      <!-- Activités du jour -->
      <div class="row mb-4">
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="fas fa-calendar-day me-2"></i>
                Rendez-vous d'aujourd'hui
              </h5>
            </div>
            <div class="card-body">
              <div class="d-flex align-items-center justify-content-between mb-3">
                <span class="h4 mb-0 text-primary">{{ stats.rendezVousAujourdhui }}</span>
                <a routerLink="/rendez-vous/aujourd-hui" class="btn btn-sm btn-outline-primary">
                  Voir tout
                </a>
              </div>
              <div class="progress">
                <div class="progress-bar" role="progressbar" 
                     [style.width.%]="getProgressPercentage(stats.rendezVousAujourdhui, 10)">
                </div>
              </div>
              <small class="text-muted">Objectif quotidien: 10 rendez-vous</small>
            </div>
          </div>
        </div>
        
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="fas fa-hourglass-half me-2"></i>
                Demandes en attente
              </h5>
            </div>
            <div class="card-body">
              <div class="d-flex align-items-center justify-content-between mb-3">
                <span class="h4 mb-0 text-warning">{{ stats.demandesEnAttente }}</span>
                <a routerLink="/demandes-administratives/en-attente" class="btn btn-sm btn-outline-warning">
                  Traiter
                </a>
              </div>
              <div class="progress">
                <div class="progress-bar bg-warning" role="progressbar" 
                     [style.width.%]="getProgressPercentage(stats.demandesEnAttente, 20)">
                </div>
              </div>
              <small class="text-muted">Limite recommandée: 20 demandes</small>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions rapides -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="fas fa-bolt me-2"></i>
                Actions rapides
              </h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-3 mb-3">
                  <a routerLink="/donnees-sante/nouveau" class="btn btn-outline-primary w-100">
                    <i class="fas fa-plus-circle mb-2 d-block"></i>
                    Nouvelle donnée de santé
                  </a>
                </div>
                <div class="col-md-3 mb-3">
                  <a routerLink="/rendez-vous/nouveau" class="btn btn-outline-success w-100">
                    <i class="fas fa-calendar-plus mb-2 d-block"></i>
                    Planifier RDV
                  </a>
                </div>
                <div class="col-md-3 mb-3">
                  <a routerLink="/demandes-administratives/nouveau" class="btn btn-outline-warning w-100">
                    <i class="fas fa-file-plus mb-2 d-block"></i>
                    Nouvelle demande
                  </a>
                </div>
                <div class="col-md-3 mb-3">
                  <a routerLink="/rapports" class="btn btn-outline-info w-100">
                    <i class="fas fa-chart-line mb-2 d-block"></i>
                    Voir rapports
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Alertes et notifications -->
      <div class="row" *ngIf="alertes.length > 0">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Alertes importantes
              </h5>
            </div>
            <div class="card-body">
              <div class="alert alert-warning" *ngFor="let alerte of alertes">
                <div class="d-flex align-items-center">
                  <i class="fas fa-exclamation-triangle me-2"></i>
                  <span>{{ alerte.message }}</span>
                  <button class="btn btn-sm btn-outline-warning ms-auto" 
                          (click)="traiterAlerte(alerte)">
                    Traiter
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .dashboard {
      padding: 0;
    }
    
    .dashboard-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 2rem;
      border-radius: 8px;
      margin-bottom: 2rem;
    }
    
    .stat-card {
      color: white;
      border-radius: 8px;
      padding: 1.5rem;
      text-align: center;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease;
    }
    
    .stat-card:hover {
      transform: translateY(-2px);
    }
    
    .stat-number {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
    }
    
    .stat-label {
      font-size: 0.875rem;
      opacity: 0.9;
    }
    
    .card {
      border: none;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: box-shadow 0.3s ease;
    }
    
    .card:hover {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
    
    .card-header {
      background-color: #f8f9fa;
      border-bottom: 1px solid #dee2e6;
    }
    
    .progress {
      height: 8px;
      margin-bottom: 0.5rem;
    }
    
    .btn-outline-primary:hover,
    .btn-outline-success:hover,
    .btn-outline-warning:hover,
    .btn-outline-info:hover {
      transform: translateY(-1px);
    }
    
    .alert {
      border: none;
      border-radius: 8px;
    }
    
    @media (max-width: 768px) {
      .dashboard-header {
        padding: 1.5rem;
        text-align: center;
      }
      
      .stat-number {
        font-size: 2rem;
      }
      
      .btn {
        margin-bottom: 0.5rem;
      }
    }
  `]
})
export class DashboardComponent implements OnInit {
  stats: DashboardStats = {
    totalDonneesSante: 0,
    totalRendezVous: 0,
    totalDemandes: 0,
    alertesActives: 0,
    rendezVousAujourdhui: 0,
    demandesEnAttente: 0
  };

  alertes: any[] = [];

  constructor(
    private donneesSanteService: DonneesSanteService,
    private rendezVousService: RendezVousService,
    private demandesService: DemandesAdministrativesService
  ) {}

  ngOnInit(): void {
    this.chargerStatistiques();
    this.chargerAlertes();
  }

  private chargerStatistiques(): void {
    // Simuler le chargement des statistiques
    // En production, ces données viendraient des services
    this.stats = {
      totalDonneesSante: 156,
      totalRendezVous: 89,
      totalDemandes: 23,
      alertesActives: 5,
      rendezVousAujourdhui: 8,
      demandesEnAttente: 12
    };
  }

  private chargerAlertes(): void {
    // Simuler le chargement des alertes
    this.alertes = [
      {
        id: 1,
        message: '3 rendez-vous en retard nécessitent une attention immédiate',
        type: 'warning'
      },
      {
        id: 2,
        message: '2 demandes urgentes en attente de validation',
        type: 'danger'
      }
    ];
  }

  getProgressPercentage(current: number, target: number): number {
    return Math.min((current / target) * 100, 100);
  }

  traiterAlerte(alerte: any): void {
    // Logique pour traiter l'alerte
    console.log('Traitement de l\'alerte:', alerte);
  }
}
