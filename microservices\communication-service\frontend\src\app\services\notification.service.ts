import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { tap } from 'rxjs/operators';

// Configuration temporaire pour le développement
const environment = {
  production: false,
  apiUrl: 'http://localhost:8084'
};

export interface Notification {
  id: number;
  destinataireId: number;
  type: 'NOUVEAU_MESSAGE' | 'NOUVELLE_CONVERSATION' | 'AJOUT_CONVERSATION' | 
        'SUPPRESSION_CONVERSATION' | 'ESCALADE_CHATBOT' | 'SYSTEME';
  canal: 'PUSH' | 'EMAIL' | 'SMS' | 'INTERNE';
  titre: string;
  contenu: string;
  priorite: 'BASSE' | 'NORMALE' | 'HAUTE' | 'URGENTE';
  statut: 'EN_ATTENTE' | 'EN_COURS' | 'ENVOYE' | 'LU' | 'ERREUR' | 'EXPIRE';
  dateProgrammee?: string;
  dateCreation: string;
  dateEnvoi?: string;
  dateLecture?: string;
  nombreTentatives: number;
  messageErreur?: string;
  donnees?: any;
}

export interface NotificationPreference {
  id: number;
  utilisateurId: number;
  typeNotification: string;
  canal: string;
  active: boolean;
  dateCreation: string;
  dateModification: string;
}

export interface CreerNotificationRequest {
  destinataireId: number;
  type: string;
  canal: string;
  titre: string;
  contenu: string;
  priorite?: string;
  dateProgrammee?: string;
  donnees?: any;
}

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private apiUrl = `${environment.apiUrl}/api/notifications`;
  private notificationsSubject = new BehaviorSubject<Notification[]>([]);
  private notificationsNonLuesSubject = new BehaviorSubject<number>(0);
  private preferencesSubject = new BehaviorSubject<NotificationPreference[]>([]);

  constructor(private http: HttpClient) {}

  // Observables pour les composants
  getNotifications(): Observable<Notification[]> {
    return this.notificationsSubject.asObservable();
  }

  getNotificationsNonLues(): Observable<number> {
    return this.notificationsNonLuesSubject.asObservable();
  }

  getPreferences(): Observable<NotificationPreference[]> {
    return this.preferencesSubject.asObservable();
  }

  // Gestion des notifications
  creerNotification(request: CreerNotificationRequest): Observable<Notification> {
    return this.http.post<Notification>(this.apiUrl, request).pipe(
      tap(notification => {
        const notifications = this.notificationsSubject.value;
        this.notificationsSubject.next([notification, ...notifications]);
        this.updateNotificationsNonLues();
      })
    );
  }

  obtenirNotificationsUtilisateur(
    utilisateurId: number, 
    page: number = 0, 
    size: number = 20,
    statut?: string,
    type?: string
  ): Observable<any> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    if (statut) params = params.set('statut', statut);
    if (type) params = params.set('type', type);

    return this.http.get<any>(`${this.apiUrl}/utilisateur/${utilisateurId}`, { params }).pipe(
      tap(response => {
        if (page === 0) {
          this.notificationsSubject.next(response.content);
        } else {
          const notifications = this.notificationsSubject.value;
          this.notificationsSubject.next([...notifications, ...response.content]);
        }
        this.updateNotificationsNonLues();
      })
    );
  }

  obtenirNotification(notificationId: number): Observable<Notification> {
    return this.http.get<Notification>(`${this.apiUrl}/${notificationId}`);
  }

  marquerCommeLue(notificationId: number): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${notificationId}/lue`, {}).pipe(
      tap(() => {
        const notifications = this.notificationsSubject.value;
        const index = notifications.findIndex(n => n.id === notificationId);
        if (index !== -1) {
          notifications[index].statut = 'LU';
          notifications[index].dateLecture = new Date().toISOString();
          this.notificationsSubject.next([...notifications]);
          this.updateNotificationsNonLues();
        }
      })
    );
  }

  marquerToutesCommeLues(utilisateurId: number): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/utilisateur/${utilisateurId}/marquer-toutes-lues`, {}).pipe(
      tap(() => {
        const notifications = this.notificationsSubject.value;
        const updatedNotifications = notifications.map(n => ({
          ...n,
          statut: 'LU' as const,
          dateLecture: new Date().toISOString()
        }));
        this.notificationsSubject.next(updatedNotifications);
        this.notificationsNonLuesSubject.next(0);
      })
    );
  }

  supprimerNotification(notificationId: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${notificationId}`).pipe(
      tap(() => {
        const notifications = this.notificationsSubject.value;
        this.notificationsSubject.next(notifications.filter(n => n.id !== notificationId));
        this.updateNotificationsNonLues();
      })
    );
  }

  supprimerToutesLues(utilisateurId: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/utilisateur/${utilisateurId}/supprimer-lues`).pipe(
      tap(() => {
        const notifications = this.notificationsSubject.value;
        this.notificationsSubject.next(notifications.filter(n => n.statut !== 'LU'));
      })
    );
  }

  // Gestion des préférences
  obtenirPreferencesUtilisateur(utilisateurId: number): Observable<NotificationPreference[]> {
    return this.http.get<NotificationPreference[]>(`${this.apiUrl}/preferences/${utilisateurId}`).pipe(
      tap(preferences => this.preferencesSubject.next(preferences))
    );
  }

  mettreAJourPreference(
    utilisateurId: number, 
    typeNotification: string, 
    canal: string, 
    active: boolean
  ): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/preferences/${utilisateurId}`, {
      typeNotification,
      canal,
      active
    }).pipe(
      tap(() => {
        const preferences = this.preferencesSubject.value;
        const index = preferences.findIndex(p => 
          p.typeNotification === typeNotification && p.canal === canal
        );
        if (index !== -1) {
          preferences[index].active = active;
          this.preferencesSubject.next([...preferences]);
        }
      })
    );
  }

  initialiserPreferencesUtilisateur(utilisateurId: number): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/preferences/${utilisateurId}/initialiser`, {});
  }

  // Notifications push
  demanderPermissionPush(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      console.warn('Ce navigateur ne supporte pas les notifications push');
      return Promise.resolve('denied');
    }

    return Notification.requestPermission();
  }

  envoyerNotificationPush(titre: string, options?: NotificationOptions): void {
    if (Notification.permission === 'granted') {
      new Notification(titre, {
        icon: '/assets/icons/icon-192x192.png',
        badge: '/assets/icons/badge-72x72.png',
        ...options
      });
    }
  }

  // Statistiques
  obtenirStatistiquesNotifications(utilisateurId: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/utilisateur/${utilisateurId}/statistiques`);
  }

  obtenirNotificationsEnAttente(): Observable<Notification[]> {
    return this.http.get<Notification[]>(`${this.apiUrl}/en-attente`);
  }

  obtenirNotificationsEchouees(): Observable<Notification[]> {
    return this.http.get<Notification[]>(`${this.apiUrl}/echouees`);
  }

  // Gestion des groupes de notifications
  obtenirNotificationsGroupees(utilisateurId: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/utilisateur/${utilisateurId}/groupees`);
  }

  // Test et debug
  testerNotification(utilisateurId: number, canal: string): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/test`, {
      utilisateurId,
      canal
    });
  }

  // Utilitaires privées
  private updateNotificationsNonLues(): void {
    const notifications = this.notificationsSubject.value;
    const nonLues = notifications.filter(n => n.statut !== 'LU').length;
    this.notificationsNonLuesSubject.next(nonLues);
  }

  // Formatage et utilitaires
  getNotificationIcon(type: string): string {
    switch (type) {
      case 'NOUVEAU_MESSAGE': return 'message';
      case 'NOUVELLE_CONVERSATION': return 'chat';
      case 'AJOUT_CONVERSATION': return 'group_add';
      case 'SUPPRESSION_CONVERSATION': return 'group_remove';
      case 'ESCALADE_CHATBOT': return 'support_agent';
      case 'SYSTEME': return 'info';
      default: return 'notifications';
    }
  }

  getNotificationColor(priorite: string): string {
    switch (priorite) {
      case 'BASSE': return 'primary';
      case 'NORMALE': return 'accent';
      case 'HAUTE': return 'warn';
      case 'URGENTE': return 'warn';
      default: return 'primary';
    }
  }

  formatTimeAgo(date: string): string {
    const now = new Date();
    const notificationDate = new Date(date);
    const diffMs = now.getTime() - notificationDate.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'À l\'instant';
    if (diffMins < 60) return `Il y a ${diffMins} min`;
    if (diffHours < 24) return `Il y a ${diffHours}h`;
    if (diffDays < 7) return `Il y a ${diffDays}j`;
    
    return notificationDate.toLocaleDateString('fr-FR');
  }

  // Nettoyage
  clearNotifications(): void {
    this.notificationsSubject.next([]);
    this.notificationsNonLuesSubject.next(0);
    this.preferencesSubject.next([]);
  }
}
