-- Script de création des tables pour le service de communication
-- Auteur: SprintBot Team
-- Version: 1.0.0

-- Utiliser le schéma communication
SET search_path TO communication, public;

-- Table des conversations
CREATE TABLE IF NOT EXISTS conversation (
    id BIGSERIAL PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL CHECK (type IN ('PRIVE', 'GROUPE', 'EQUIPE', 'CANAL')),
    createur_id BIGINT NOT NULL,
    est_archive BOOLEAN DEFAULT FALSE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des participants aux conversations
CREATE TABLE IF NOT EXISTS participant_conversation (
    id BIGSERIAL PRIMARY KEY,
    conversation_id BIGINT NOT NULL,
    utilisateur_id BIGINT NOT NULL,
    role VARCHAR(50) DEFAULT 'MEMBRE' CHECK (role IN ('MEMBRE', 'ADMIN', 'MODERATEUR')),
    date_ajout TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_derniere_lecture TIMESTAMP,
    notifications_actives BOOLEAN DEFAULT TRUE,
    est_epingle BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (conversation_id) REFERENCES conversation(id) ON DELETE CASCADE,
    UNIQUE(conversation_id, utilisateur_id)
);

-- Table des messages
CREATE TABLE IF NOT EXISTS message (
    id BIGSERIAL PRIMARY KEY,
    conversation_id BIGINT NOT NULL,
    expediteur_id BIGINT NOT NULL,
    contenu TEXT NOT NULL,
    type VARCHAR(50) DEFAULT 'TEXTE' CHECK (type IN ('TEXTE', 'IMAGE', 'VIDEO', 'AUDIO', 'FICHIER', 'SYSTEME')),
    url_fichier VARCHAR(500),
    message_parent_id BIGINT,
    est_modifie BOOLEAN DEFAULT FALSE,
    est_supprime BOOLEAN DEFAULT FALSE,
    est_epingle BOOLEAN DEFAULT FALSE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (conversation_id) REFERENCES conversation(id) ON DELETE CASCADE,
    FOREIGN KEY (message_parent_id) REFERENCES message(id) ON DELETE SET NULL
);

-- Table des réactions aux messages
CREATE TABLE IF NOT EXISTS reaction_message (
    id BIGSERIAL PRIMARY KEY,
    message_id BIGINT NOT NULL,
    utilisateur_id BIGINT NOT NULL,
    emoji VARCHAR(10) NOT NULL,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (message_id) REFERENCES message(id) ON DELETE CASCADE,
    UNIQUE(message_id, utilisateur_id, emoji)
);

-- Table des lectures de messages
CREATE TABLE IF NOT EXISTS lecture_message (
    id BIGSERIAL PRIMARY KEY,
    message_id BIGINT NOT NULL,
    utilisateur_id BIGINT NOT NULL,
    date_lecture TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (message_id) REFERENCES message(id) ON DELETE CASCADE,
    UNIQUE(message_id, utilisateur_id)
);

-- Table des notifications
CREATE TABLE IF NOT EXISTS notification (
    id BIGSERIAL PRIMARY KEY,
    destinataire_id BIGINT NOT NULL,
    type VARCHAR(100) NOT NULL,
    canal VARCHAR(50) NOT NULL CHECK (canal IN ('PUSH', 'EMAIL', 'SMS', 'INTERNE')),
    titre VARCHAR(255) NOT NULL,
    contenu TEXT NOT NULL,
    priorite VARCHAR(50) DEFAULT 'NORMALE' CHECK (priorite IN ('BASSE', 'NORMALE', 'HAUTE', 'URGENTE')),
    statut VARCHAR(50) DEFAULT 'EN_ATTENTE' CHECK (statut IN ('EN_ATTENTE', 'EN_COURS', 'ENVOYE', 'LU', 'ERREUR', 'EXPIRE')),
    date_programmee TIMESTAMP,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_envoi TIMESTAMP,
    date_lecture TIMESTAMP,
    nombre_tentatives INTEGER DEFAULT 0,
    message_erreur TEXT,
    donnees JSONB
);

-- Table des préférences de notification
CREATE TABLE IF NOT EXISTS notification_preference (
    id BIGSERIAL PRIMARY KEY,
    utilisateur_id BIGINT NOT NULL,
    type_notification VARCHAR(100) NOT NULL,
    canal VARCHAR(50) NOT NULL,
    active BOOLEAN DEFAULT TRUE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(utilisateur_id, type_notification, canal)
);

-- Table des conversations de chatbot
CREATE TABLE IF NOT EXISTS chatbot_conversation (
    id BIGSERIAL PRIMARY KEY,
    utilisateur_id BIGINT NOT NULL,
    session_id VARCHAR(255) NOT NULL,
    contexte JSONB,
    statut VARCHAR(50) DEFAULT 'ACTIVE' CHECK (statut IN ('ACTIVE', 'TERMINEE', 'ESCALADEE')),
    satisfaction INTEGER CHECK (satisfaction BETWEEN 1 AND 5),
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_fin TIMESTAMP,
    UNIQUE(session_id)
);

-- Table des messages de chatbot
CREATE TABLE IF NOT EXISTS chatbot_message (
    id BIGSERIAL PRIMARY KEY,
    chatbot_conversation_id BIGINT NOT NULL,
    contenu TEXT NOT NULL,
    est_utilisateur BOOLEAN NOT NULL,
    intention VARCHAR(255),
    score_confiance DECIMAL(3,2),
    temps_reponse_ms INTEGER,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chatbot_conversation_id) REFERENCES chatbot_conversation(id) ON DELETE CASCADE
);

-- Table de présence des utilisateurs
CREATE TABLE IF NOT EXISTS user_presence (
    id BIGSERIAL PRIMARY KEY,
    utilisateur_id BIGINT NOT NULL,
    statut VARCHAR(50) DEFAULT 'HORS_LIGNE' CHECK (statut IN ('EN_LIGNE', 'ABSENT', 'OCCUPE', 'HORS_LIGNE', 'INVISIBLE')),
    message_statut VARCHAR(255),
    plateforme VARCHAR(50),
    est_mobile BOOLEAN DEFAULT FALSE,
    derniere_activite TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(utilisateur_id, plateforme)
);

-- Index pour optimiser les performances
CREATE INDEX IF NOT EXISTS idx_conversation_createur ON conversation(createur_id);
CREATE INDEX IF NOT EXISTS idx_conversation_type ON conversation(type);
CREATE INDEX IF NOT EXISTS idx_conversation_date_creation ON conversation(date_creation);

CREATE INDEX IF NOT EXISTS idx_participant_conversation ON participant_conversation(conversation_id);
CREATE INDEX IF NOT EXISTS idx_participant_utilisateur ON participant_conversation(utilisateur_id);
CREATE INDEX IF NOT EXISTS idx_participant_role ON participant_conversation(role);

CREATE INDEX IF NOT EXISTS idx_message_conversation ON message(conversation_id);
CREATE INDEX IF NOT EXISTS idx_message_expediteur ON message(expediteur_id);
CREATE INDEX IF NOT EXISTS idx_message_date_creation ON message(date_creation);
CREATE INDEX IF NOT EXISTS idx_message_type ON message(type);
CREATE INDEX IF NOT EXISTS idx_message_parent ON message(message_parent_id);
CREATE INDEX IF NOT EXISTS idx_message_contenu_search ON message USING gin(to_tsvector('french_unaccent', contenu));

CREATE INDEX IF NOT EXISTS idx_reaction_message ON reaction_message(message_id);
CREATE INDEX IF NOT EXISTS idx_reaction_utilisateur ON reaction_message(utilisateur_id);

CREATE INDEX IF NOT EXISTS idx_lecture_message ON lecture_message(message_id);
CREATE INDEX IF NOT EXISTS idx_lecture_utilisateur ON lecture_message(utilisateur_id);

CREATE INDEX IF NOT EXISTS idx_notification_destinataire ON notification(destinataire_id);
CREATE INDEX IF NOT EXISTS idx_notification_type ON notification(type);
CREATE INDEX IF NOT EXISTS idx_notification_statut ON notification(statut);
CREATE INDEX IF NOT EXISTS idx_notification_date_creation ON notification(date_creation);
CREATE INDEX IF NOT EXISTS idx_notification_date_programmee ON notification(date_programmee);

CREATE INDEX IF NOT EXISTS idx_notification_preference_utilisateur ON notification_preference(utilisateur_id);

CREATE INDEX IF NOT EXISTS idx_chatbot_conversation_utilisateur ON chatbot_conversation(utilisateur_id);
CREATE INDEX IF NOT EXISTS idx_chatbot_conversation_session ON chatbot_conversation(session_id);
CREATE INDEX IF NOT EXISTS idx_chatbot_conversation_statut ON chatbot_conversation(statut);

CREATE INDEX IF NOT EXISTS idx_chatbot_message_conversation ON chatbot_message(chatbot_conversation_id);
CREATE INDEX IF NOT EXISTS idx_chatbot_message_date ON chatbot_message(date_creation);

CREATE INDEX IF NOT EXISTS idx_user_presence_utilisateur ON user_presence(utilisateur_id);
CREATE INDEX IF NOT EXISTS idx_user_presence_statut ON user_presence(statut);
CREATE INDEX IF NOT EXISTS idx_user_presence_derniere_activite ON user_presence(derniere_activite);

-- Affichage du statut
SELECT 'Tables créées avec succès dans le schéma communication' AS status;
SELECT 'Index créés pour optimiser les performances' AS index_status;
