# 🚀 Guide de déploiement - Gateway Service

## Vue d'ensemble

Ce guide détaille les procédures de déploiement du **Gateway Service** pour l'écosystème SprintBot. Le Gateway Service sert de point d'entrée unique pour tous les microservices de la plateforme.

## 📋 Prérequis

### Logiciels requis
- **Docker** 20.10+ et **Docker Compose** 2.0+
- **Java** 21+ (pour le développement local)
- **Maven** 3.8+ (pour le build local)
- **Git** pour le contrôle de version

### Services dépendants
- **Discovery Service** (Eureka) - Port 8761
- **Redis** - Port 6379 (pour rate limiting)

### Ressources système minimales
- **RAM** : 1GB minimum, 2GB recommandé
- **CPU** : 1 core minimum, 2 cores recommandé
- **Stockage** : 2GB d'espace libre

## 🔧 Configuration

### 1. Variables d'environnement

Copiez le fichier d'exemple et adaptez les valeurs :

```bash
cp .env.example .env
```

Variables critiques à configurer :

```bash
# Sécurité JWT - OBLIGATOIRE à changer en production
JWT_SECRET_KEY=your-very-long-and-secure-secret-key

# Configuration Redis
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password

# Configuration Eureka
EUREKA_CLIENT_SERVICE_URL=http://discovery-service:8761/eureka/

# Rate limiting
RATE_LIMIT_REQUESTS_PER_SECOND=100
RATE_LIMIT_BURST_CAPACITY=200
```

### 2. Configuration CORS

Adaptez les origines autorisées selon votre environnement :

```yaml
# Dans application.yml
spring:
  cloud:
    gateway:
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOriginPatterns: 
              - "https://your-domain.com"
              - "https://*.your-domain.com"
```

## 🐳 Déploiement avec Docker

### 1. Déploiement rapide

```bash
# Démarrage complet avec dépendances
docker-compose up -d

# Vérification du statut
docker-compose ps
```

### 2. Déploiement étape par étape

```bash
# 1. Démarrer Redis
docker-compose up -d redis

# 2. Attendre que Redis soit prêt
docker-compose logs -f redis

# 3. Démarrer Discovery Service (si pas déjà fait)
docker-compose up -d discovery-service

# 4. Démarrer Gateway Service
docker-compose up -d gateway-service

# 5. Vérifier les logs
docker-compose logs -f gateway-service
```

### 3. Build personnalisé

```bash
# Build de l'image
cd backend
docker build -t sprintbot/gateway-service:custom .

# Utilisation de l'image personnalisée
docker-compose -f docker-compose.yml -f docker-compose.custom.yml up -d
```

## 🔨 Déploiement local (développement)

### 1. Prérequis locaux

```bash
# Démarrer les services dépendants
docker-compose up -d redis discovery-service

# Vérifier la disponibilité
curl http://localhost:8761/actuator/health
curl http://localhost:6379 # Redis ping
```

### 2. Build et exécution

```bash
cd backend

# Build Maven
mvn clean package -DskipTests

# Exécution locale
java -jar target/gateway-service.jar \
  --spring.profiles.active=dev \
  --server.port=8080 \
  --eureka.client.service-url.defaultZone=http://localhost:8761/eureka/
```

### 3. Mode développement avec rechargement

```bash
# Avec Spring Boot DevTools
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

## 🌐 Déploiement en production

### 1. Configuration de sécurité

```bash
# Variables de production
export JWT_SECRET_KEY="$(openssl rand -base64 64)"
export REDIS_PASSWORD="$(openssl rand -base64 32)"
export SPRING_PROFILES_ACTIVE=prod
```

### 2. Configuration HTTPS

```yaml
# application-prod.yml
server:
  ssl:
    enabled: true
    key-store: classpath:keystore.p12
    key-store-password: ${SSL_KEYSTORE_PASSWORD}
    key-store-type: PKCS12
```

### 3. Monitoring et observabilité

```bash
# Déploiement avec monitoring
docker-compose -f docker-compose.yml -f docker-compose.monitoring.yml up -d

# Vérification des métriques
curl http://localhost:8080/actuator/prometheus
```

### 4. Load balancing

```yaml
# docker-compose.prod.yml
gateway-service:
  deploy:
    replicas: 3
    update_config:
      parallelism: 1
      delay: 10s
    restart_policy:
      condition: on-failure
```

## 🔍 Validation du déploiement

### 1. Tests automatiques

```bash
# Validation complète
./validate-gateway.sh --full

# Tests des routes
./test-gateway-routes.sh

# Tests avec authentification
./test-gateway-routes.sh --with-auth
```

### 2. Vérifications manuelles

```bash
# Health check
curl http://localhost:8080/actuator/health

# Info du service
curl http://localhost:8080/actuator/info

# Routes configurées
curl http://localhost:8080/actuator/gateway/routes

# Métriques
curl http://localhost:8080/actuator/metrics
```

### 3. Tests de routage

```bash
# Test route auth (publique)
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"test","password":"test"}'

# Test route protégée (doit retourner 401)
curl http://localhost:8080/api/planning/sessions
```

## 📊 Monitoring et logs

### 1. Logs en temps réel

```bash
# Logs du Gateway
docker-compose logs -f gateway-service

# Logs avec filtrage
docker-compose logs gateway-service | grep ERROR

# Logs de tous les services
docker-compose logs -f
```

### 2. Métriques importantes

```bash
# Métriques HTTP
curl http://localhost:8080/actuator/metrics/http.server.requests

# Circuit breakers
curl http://localhost:8080/actuator/circuitbreakers

# Rate limiters
curl http://localhost:8080/actuator/ratelimiters
```

### 3. Dashboard Grafana (optionnel)

```bash
# Démarrage avec Grafana
docker-compose -f docker-compose.yml -f docker-compose.monitoring.yml up -d

# Accès : http://localhost:3000
# Login : admin/admin
```

## 🔧 Maintenance

### 1. Mise à jour

```bash
# Sauvegarde de la configuration
cp .env .env.backup

# Pull de la nouvelle image
docker-compose pull gateway-service

# Redémarrage avec la nouvelle version
docker-compose up -d gateway-service

# Vérification
./validate-gateway.sh --service
```

### 2. Sauvegarde

```bash
# Sauvegarde de la configuration Redis
docker exec sprintbot-redis redis-cli BGSAVE

# Sauvegarde des logs
docker-compose logs gateway-service > gateway-$(date +%Y%m%d).log
```

### 3. Nettoyage

```bash
# Nettoyage des conteneurs arrêtés
docker-compose down --remove-orphans

# Nettoyage des images inutilisées
docker image prune -f

# Nettoyage des volumes (ATTENTION : perte de données)
docker-compose down -v
```

## 🚨 Dépannage

### 1. Problèmes courants

**Gateway ne démarre pas :**
```bash
# Vérifier les logs
docker-compose logs gateway-service

# Vérifier Eureka
curl http://localhost:8761/actuator/health

# Vérifier Redis
docker-compose exec redis redis-cli ping
```

**Erreurs d'authentification :**
```bash
# Vérifier la configuration JWT
echo $JWT_SECRET_KEY

# Vérifier les logs de sécurité
docker-compose logs gateway-service | grep "Authentication"
```

**Problèmes de routage :**
```bash
# Vérifier les routes
curl http://localhost:8080/actuator/gateway/routes

# Vérifier l'enregistrement Eureka
curl http://localhost:8761/eureka/apps
```

### 2. Mode debug

```bash
# Démarrage en mode debug
LOGGING_LEVEL_GATEWAY=DEBUG docker-compose up -d gateway-service

# Logs détaillés
docker-compose logs -f gateway-service
```

### 3. Tests de connectivité

```bash
# Test de connectivité réseau
docker-compose exec gateway-service nc -zv discovery-service 8761
docker-compose exec gateway-service nc -zv redis 6379

# Test DNS
docker-compose exec gateway-service nslookup discovery-service
```

## 📞 Support

### Contacts
- **Équipe Infrastructure** : <EMAIL>
- **Équipe Sécurité** : <EMAIL>

### Ressources
- [Documentation Spring Cloud Gateway](https://spring.io/projects/spring-cloud-gateway)
- [Guide de sécurité JWT](./docs/jwt-security.md)
- [Monitoring avec Prometheus](./docs/monitoring.md)

---

**🎯 Le Gateway Service est maintenant prêt à servir de point d'entrée unique pour l'écosystème SprintBot !**
