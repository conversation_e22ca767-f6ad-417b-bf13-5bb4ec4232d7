{"name": "sprintbot-finance-frontend", "version": "1.0.0", "description": "Frontend Angular pour le service finance de SprintBot", "author": "SprintBot Team <<EMAIL>>", "license": "MIT", "scripts": {"ng": "ng", "start": "ng serve", "start:dev": "ng serve --configuration development --host 0.0.0.0 --port 4205", "start:prod": "ng serve --configuration production --host 0.0.0.0 --port 4205", "build": "ng build", "build:dev": "ng build --configuration development", "build:prod": "ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test", "test:ci": "ng test --watch=false --browsers=ChromeHeadless", "lint": "ng lint", "e2e": "ng e2e", "analyze": "ng build --stats-json && npx webpack-bundle-analyzer dist/sprintbot-finance-frontend/stats.json"}, "dependencies": {"@angular/animations": "^17.0.0", "@angular/cdk": "^17.0.0", "@angular/common": "^17.0.0", "@angular/compiler": "^17.0.0", "@angular/core": "^17.0.0", "@angular/forms": "^17.0.0", "@angular/material": "^17.0.0", "@angular/platform-browser": "^17.0.0", "@angular/platform-browser-dynamic": "^17.0.0", "@angular/router": "^17.0.0", "@angular/service-worker": "^17.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.0", "chart.js": "^4.4.0", "ng2-charts": "^5.0.0", "date-fns": "^2.30.0", "lodash-es": "^4.17.21", "file-saver": "^2.0.5", "ngx-toastr": "^18.0.0", "ngx-loading": "^16.0.0", "ngx-pagination": "^6.0.3", "ngx-mask": "^16.4.0", "ngx-currency": "^3.0.0", "angular-material-expansion-panel": "^0.7.2"}, "devDependencies": {"@angular-devkit/build-angular": "^17.0.0", "@angular/cli": "^17.0.0", "@angular/compiler-cli": "^17.0.0", "@types/jasmine": "~5.1.0", "@types/lodash-es": "^4.17.12", "@types/file-saver": "^2.0.7", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.50.0", "@angular-eslint/builder": "^17.0.0", "@angular-eslint/eslint-plugin": "^17.0.0", "@angular-eslint/eslint-plugin-template": "^17.0.0", "@angular-eslint/schematics": "^17.0.0", "@angular-eslint/template-parser": "^17.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": ["last 2 Chrome versions", "last 2 Firefox versions", "last 2 Safari versions", "last 2 Edge versions"]}