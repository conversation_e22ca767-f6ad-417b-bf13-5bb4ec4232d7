export interface RendezVousMedical {
  id?: number;
  joueurId: number;
  staffMedicalId: number;
  typeRendezVous: TypeRendezVous;
  dateRendezVous: string;
  heureDebut: string;
  heureFin: string;
  lieu?: string;
  description?: string;
  statut: StatutRendezVous;
  priorite: PrioriteRendezVous;
  rappelEnvoye: boolean;
  dateRappel?: string;
  compteRendu?: string;
  prescriptions?: string;
  prochaineVisitePrevue?: string;
  notes?: string;
  createdAt?: string;
  updatedAt?: string;
}

export enum TypeRendezVous {
  CONSULTATION = 'CONSULTATION',
  SUIVI_BLESSURE = 'SUIVI_BLESSURE',
  BILAN_ANNUEL = 'BILAN_ANNUEL',
  PREVENTION = 'PREVENTION',
  URGENCE = 'URGENCE',
  REEDUCATION = 'REEDUCATION'
}

export enum StatutRendezVous {
  PLANIFIE = 'PLANIFIE',
  CONFIRME = 'CONFIRME',
  EN_COURS = 'EN_COURS',
  TERMINE = 'TERMINE',
  ANNULE = 'ANNULE',
  REPORTE = 'REPORTE'
}

export enum PrioriteRendezVous {
  FAIBLE = 'FAIBLE',
  NORMALE = 'NORMALE',
  ELEVEE = 'ELEVEE',
  URGENTE = 'URGENTE'
}

export interface RendezVousFilters {
  joueurId?: number;
  staffMedicalId?: number;
  typeRendezVous?: TypeRendezVous;
  statut?: StatutRendezVous;
  priorite?: PrioriteRendezVous;
  dateDebut?: string;
  dateFin?: string;
  lieu?: string;
}

export interface RendezVousStats {
  totalRendezVous: number;
  rendezVousParType: { [key: string]: number };
  rendezVousParStatut: { [key: string]: number };
  rendezVousParPriorite: { [key: string]: number };
  planningHebdomadaire: { jour: string; nombre: number }[];
}

export interface CreneauDisponible {
  date: string;
  heureDebut: string;
  heureFin: string;
  staffMedicalId: number;
  disponible: boolean;
}
