#!/bin/bash

# Script de validation pour Gateway Service - SprintBot
# Valide la configuration, le build et le déploiement

set -e

# Rendre le script exécutable
chmod +x "$0" 2>/dev/null || true

# Configuration des couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Compteurs
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# Fonction de logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

log_info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

log_warn() {
    echo -e "${YELLOW}[WARN] $1${NC}"
}

log_error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

log_success() {
    echo -e "${GREEN}[SUCCESS] $1${NC}"
}

# Fonction de test
test_case() {
    local test_name="$1"
    local test_command="$2"
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    echo -e "\n${CYAN}🧪 Test: $test_name${NC}"
    
    if eval "$test_command"; then
        log_success "✅ $test_name - PASSED"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        log_error "❌ $test_name - FAILED"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Affichage du header
echo -e "${PURPLE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                🌐 GATEWAY SERVICE VALIDATION                 ║"
echo "║                     SprintBot Platform                       ║"
echo "║                                                              ║"
echo "║  Validation complète du Gateway Service                     ║"
echo "║  Version: 1.0.0                                             ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}\n"

# Validation de la structure du projet
log "📁 Validation de la structure du projet..."

test_case "Structure des répertoires" "
    [ -d 'backend' ] && 
    [ -d 'backend/src/main/java/com/sprintbot/gateway' ] && 
    [ -d 'backend/src/main/resources' ]
"

test_case "Fichiers de configuration Maven" "
    [ -f 'backend/pom.xml' ]
"

test_case "Classe principale" "
    [ -f 'backend/src/main/java/com/sprintbot/gateway/GatewayServiceApplication.java' ]
"

test_case "Configuration de sécurité" "
    [ -f 'backend/src/main/java/com/sprintbot/gateway/config/SecurityConfig.java' ]
"

test_case "Service JWT" "
    [ -f 'backend/src/main/java/com/sprintbot/gateway/service/JwtService.java' ]
"

test_case "Filtre d'authentification" "
    [ -f 'backend/src/main/java/com/sprintbot/gateway/filter/AuthenticationFilter.java' ]
"

test_case "Configuration Spring" "
    [ -f 'backend/src/main/resources/application.yml' ]
"

test_case "Configuration Docker" "
    [ -f 'backend/Dockerfile' ] && 
    [ -f 'backend/docker-entrypoint.sh' ] && 
    [ -f 'docker-compose.yml' ]
"

test_case "Documentation" "
    [ -f 'README.md' ]
"

# Validation de la configuration Maven
log "🔧 Validation de la configuration Maven..."

test_case "Validation du POM" "
    cd backend && 
    mvn validate -q
"

test_case "Dépendances Spring Cloud Gateway" "
    cd backend && 
    grep -q 'spring-cloud-starter-gateway' pom.xml
"

test_case "Dépendances Eureka Client" "
    cd backend && 
    grep -q 'spring-cloud-starter-netflix-eureka-client' pom.xml
"

test_case "Dépendances Spring Security" "
    cd backend && 
    grep -q 'spring-boot-starter-security' pom.xml
"

test_case "Dépendances JWT" "
    cd backend && 
    grep -q 'jjwt-api' pom.xml
"

test_case "Dépendances Redis" "
    cd backend && 
    grep -q 'spring-boot-starter-data-redis-reactive' pom.xml
"

test_case "Dépendances Resilience4j" "
    cd backend && 
    grep -q 'resilience4j' pom.xml
"

# Validation de la configuration Spring
log "⚙️ Validation de la configuration Spring..."

test_case "Configuration des profils" "
    grep -q 'spring.profiles.active' backend/src/main/resources/application.yml
"

test_case "Configuration Gateway" "
    grep -q 'spring.cloud.gateway' backend/src/main/resources/application.yml
"

test_case "Configuration Eureka" "
    grep -q 'eureka.client' backend/src/main/resources/application.yml
"

test_case "Configuration Redis" "
    grep -q 'spring.data.redis' backend/src/main/resources/application.yml
"

test_case "Configuration JWT" "
    grep -q 'jwt.secret' backend/src/main/resources/application.yml
"

test_case "Configuration Resilience4j" "
    grep -q 'resilience4j.circuitbreaker' backend/src/main/resources/application.yml
"

test_case "Configuration Actuator" "
    grep -q 'management.endpoints' backend/src/main/resources/application.yml
"

# Validation du code Java
log "☕ Validation du code Java..."

test_case "Annotation @SpringBootApplication" "
    grep -q '@SpringBootApplication' backend/src/main/java/com/sprintbot/gateway/GatewayServiceApplication.java
"

test_case "Annotation @EnableDiscoveryClient" "
    grep -q '@EnableDiscoveryClient' backend/src/main/java/com/sprintbot/gateway/GatewayServiceApplication.java
"

test_case "Configuration des routes" "
    grep -q 'RouteLocator' backend/src/main/java/com/sprintbot/gateway/GatewayServiceApplication.java
"

test_case "Configuration CORS" "
    grep -q 'CorsWebFilter' backend/src/main/java/com/sprintbot/gateway/GatewayServiceApplication.java
"

test_case "Configuration Rate Limiter" "
    grep -q 'RedisRateLimiter' backend/src/main/java/com/sprintbot/gateway/GatewayServiceApplication.java
"

test_case "Configuration de sécurité" "
    grep -q '@EnableWebFluxSecurity' backend/src/main/java/com/sprintbot/gateway/config/SecurityConfig.java
"

test_case "Service JWT" "
    grep -q 'validateToken' backend/src/main/java/com/sprintbot/gateway/service/JwtService.java
"

test_case "Filtre d'authentification" "
    grep -q 'WebFilter' backend/src/main/java/com/sprintbot/gateway/filter/AuthenticationFilter.java
"

# Validation de la configuration Docker
log "🐳 Validation de la configuration Docker..."

test_case "Dockerfile multi-stage" "
    grep -q 'FROM.*AS builder' backend/Dockerfile && 
    grep -q 'FROM.*AS runtime' backend/Dockerfile
"

test_case "Script d'entrée Docker" "
    [ -x 'backend/docker-entrypoint.sh' ] || chmod +x backend/docker-entrypoint.sh
"

test_case "Configuration Docker Compose" "
    grep -q 'gateway-service:' docker-compose.yml && 
    grep -q 'redis:' docker-compose.yml
"

test_case "Variables d'environnement" "
    grep -q 'SPRING_PROFILES_ACTIVE' docker-compose.yml && 
    grep -q 'EUREKA_CLIENT_SERVICE_URL' docker-compose.yml
"

test_case "Health checks" "
    grep -q 'healthcheck:' docker-compose.yml
"

test_case "Configuration réseau" "
    grep -q 'sprintbot-network' docker-compose.yml
"

# Tests de compilation (optionnel)
if [ "$1" = "--build" ] || [ "$1" = "--full" ]; then
    log "🔨 Tests de compilation..."
    
    test_case "Compilation Maven" "
        cd backend && 
        mvn clean compile -q
    "
    
    test_case "Packaging Maven" "
        cd backend && 
        mvn package -DskipTests -q
    "
    
    test_case "Vérification du JAR" "
        [ -f 'backend/target/gateway-service.jar' ]
    "
fi

# Tests Docker (optionnel)
if [ "$1" = "--docker" ] || [ "$1" = "--full" ]; then
    log "🐳 Tests Docker..."
    
    test_case "Build de l'image Docker" "
        cd backend && 
        docker build -t sprintbot/gateway-service:test . > /dev/null 2>&1
    "
    
    test_case "Validation de l'image" "
        docker image inspect sprintbot/gateway-service:test > /dev/null 2>&1
    "
    
    # Nettoyage de l'image de test
    docker rmi sprintbot/gateway-service:test > /dev/null 2>&1 || true
fi

# Tests de service (optionnel)
if [ "$1" = "--service" ] || [ "$1" = "--full" ]; then
    log "🚀 Tests de service..."
    
    # Vérifier si les services sont en cours d'exécution
    if docker-compose ps | grep -q "gateway-service.*Up"; then
        test_case "Service Gateway en cours d'exécution" "true"
        
        test_case "Health check Gateway" "
            curl -f http://localhost:8080/actuator/health > /dev/null 2>&1
        "
        
        test_case "Endpoints Actuator" "
            curl -f http://localhost:8080/actuator/info > /dev/null 2>&1
        "
        
        test_case "Routes Gateway" "
            curl -f http://localhost:8080/actuator/gateway/routes > /dev/null 2>&1
        "
    else
        log_warn "⚠️ Services non démarrés - Tests de service ignorés"
        log_info "💡 Utilisez 'docker-compose up -d' pour démarrer les services"
    fi
fi

# Résumé des résultats
echo -e "\n${PURPLE}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${PURPLE}║                        📊 RÉSULTATS                          ║${NC}"
echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════╝${NC}"

echo -e "\n📈 Statistiques des tests:"
echo -e "   ${GREEN}✅ Tests réussis: $TESTS_PASSED${NC}"
echo -e "   ${RED}❌ Tests échoués: $TESTS_FAILED${NC}"
echo -e "   📊 Total: $TESTS_TOTAL"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n${GREEN}🎉 Tous les tests sont passés avec succès!${NC}"
    echo -e "${GREEN}✅ Gateway Service prêt pour le déploiement${NC}"
    exit 0
else
    echo -e "\n${RED}❌ $TESTS_FAILED test(s) ont échoué${NC}"
    echo -e "${YELLOW}⚠️ Veuillez corriger les erreurs avant le déploiement${NC}"
    exit 1
fi
