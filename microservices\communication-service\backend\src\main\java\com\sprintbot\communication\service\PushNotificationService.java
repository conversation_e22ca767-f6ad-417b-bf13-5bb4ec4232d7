package com.sprintbot.communication.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * Service pour l'envoi de notifications push
 */
@Service
public class PushNotificationService {

    private static final Logger logger = LoggerFactory.getLogger(PushNotificationService.class);

    @Value("${app.notification.push.vapid.public-key:}")
    private String vapidPublicKey;

    @Value("${app.notification.push.vapid.private-key:}")
    private String vapidPrivateKey;

    @Value("${app.notification.push.vapid.subject:mailto:<EMAIL>}")
    private String vapidSubject;

    private final RestTemplate restTemplate = new RestTemplate();

    /**
     * Envoie une notification push à un utilisateur
     */
    public boolean envoyerNotification(Long utilisateurId, String titre, String contenu, 
                                     Map<String, Object> donnees) {
        try {
            // Récupérer les endpoints push de l'utilisateur
            // TODO: Appel vers auth-user-service pour obtenir les endpoints push
            String endpoint = obtenirEndpointPushUtilisateur(utilisateurId);
            
            if (endpoint == null || endpoint.isEmpty()) {
                logger.warn("Aucun endpoint push trouvé pour l'utilisateur {}", utilisateurId);
                return false;
            }

            // Créer le payload de notification
            Map<String, Object> payload = creerPayloadNotification(titre, contenu, donnees);

            // Envoyer la notification
            return envoyerNotificationPush(endpoint, payload);

        } catch (Exception e) {
            logger.error("Erreur lors de l'envoi de notification push pour l'utilisateur {}: {}", 
                        utilisateurId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Envoie une notification push à un endpoint spécifique
     */
    private boolean envoyerNotificationPush(String endpoint, Map<String, Object> payload) {
        try {
            // Créer les headers avec authentification VAPID
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("TTL", "86400"); // 24 heures
            
            // TODO: Implémenter l'authentification VAPID complète
            // headers.set("Authorization", "vapid t=" + generateVapidToken() + ", k=" + vapidPublicKey);

            // Créer la requête
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(payload, headers);

            // Envoyer la notification
            ResponseEntity<String> response = restTemplate.exchange(
                    endpoint, 
                    HttpMethod.POST, 
                    request, 
                    String.class
            );

            boolean succes = response.getStatusCode().is2xxSuccessful();
            
            if (succes) {
                logger.debug("Notification push envoyée avec succès à {}", endpoint);
            } else {
                logger.warn("Échec d'envoi notification push à {}: {}", endpoint, response.getStatusCode());
            }

            return succes;

        } catch (Exception e) {
            logger.error("Erreur lors de l'envoi notification push à {}: {}", endpoint, e.getMessage());
            return false;
        }
    }

    /**
     * Crée le payload de notification
     */
    private Map<String, Object> creerPayloadNotification(String titre, String contenu, 
                                                        Map<String, Object> donnees) {
        Map<String, Object> payload = new HashMap<>();
        
        // Notification de base
        Map<String, Object> notification = new HashMap<>();
        notification.put("title", titre);
        notification.put("body", contenu);
        notification.put("icon", "/assets/icons/icon-192x192.png");
        notification.put("badge", "/assets/icons/badge-72x72.png");
        notification.put("vibrate", new int[]{200, 100, 200});
        notification.put("requireInteraction", true);
        
        // Actions possibles
        notification.put("actions", new Object[]{
            Map.of("action", "view", "title", "Voir"),
            Map.of("action", "dismiss", "title", "Ignorer")
        });

        payload.put("notification", notification);

        // Données personnalisées
        if (donnees != null && !donnees.isEmpty()) {
            payload.put("data", donnees);
        }

        return payload;
    }

    /**
     * Enregistre un endpoint push pour un utilisateur
     */
    public boolean enregistrerEndpointPush(Long utilisateurId, String endpoint, String p256dh, String auth) {
        try {
            // TODO: Sauvegarder l'endpoint dans auth-user-service
            logger.info("Endpoint push enregistré pour l'utilisateur {}: {}", utilisateurId, endpoint);
            return true;

        } catch (Exception e) {
            logger.error("Erreur lors de l'enregistrement de l'endpoint push pour l'utilisateur {}: {}", 
                        utilisateurId, e.getMessage());
            return false;
        }
    }

    /**
     * Supprime un endpoint push pour un utilisateur
     */
    public boolean supprimerEndpointPush(Long utilisateurId, String endpoint) {
        try {
            // TODO: Supprimer l'endpoint dans auth-user-service
            logger.info("Endpoint push supprimé pour l'utilisateur {}: {}", utilisateurId, endpoint);
            return true;

        } catch (Exception e) {
            logger.error("Erreur lors de la suppression de l'endpoint push pour l'utilisateur {}: {}", 
                        utilisateurId, e.getMessage());
            return false;
        }
    }

    /**
     * Envoie une notification de test
     */
    public boolean envoyerNotificationTest(Long utilisateurId) {
        Map<String, Object> donnees = Map.of(
                "type", "test",
                "timestamp", System.currentTimeMillis()
        );

        return envoyerNotification(
                utilisateurId,
                "Notification de test",
                "Ceci est une notification de test de SprintBot",
                donnees
        );
    }

    /**
     * Obtient l'endpoint push d'un utilisateur
     */
    private String obtenirEndpointPushUtilisateur(Long utilisateurId) {
        // TODO: Appel REST vers auth-user-service pour obtenir l'endpoint push
        // Pour l'instant, retourner null (pas d'endpoint configuré)
        return null;
    }

    /**
     * Génère un token VAPID pour l'authentification
     */
    private String genererTokenVapid() {
        // TODO: Implémenter la génération de token VAPID
        // Utiliser les clés VAPID pour signer un JWT
        return "";
    }

    /**
     * Valide la configuration VAPID
     */
    public boolean estConfigurationVapidValide() {
        return vapidPublicKey != null && !vapidPublicKey.isEmpty() &&
               vapidPrivateKey != null && !vapidPrivateKey.isEmpty() &&
               vapidSubject != null && !vapidSubject.isEmpty();
    }

    /**
     * Obtient la clé publique VAPID
     */
    public String obtenirClePubliqueVapid() {
        return vapidPublicKey;
    }
}
