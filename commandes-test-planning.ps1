# =============================================================================
# COMMANDES POUR TESTER LE MICROSERVICE PLANNING PERFORMANCE
# Club Olympique de Kelibia
# Copiez et collez ces commandes une par une dans PowerShell
# =============================================================================

Write-Host "=== DÉBUT DU TEST MICROSERVICE PLANNING PERFORMANCE ===" -ForegroundColor Green

# ÉTAPE 1 : Navigation vers le répertoire
Write-Host "ÉTAPE 1 : Navigation vers le répertoire du microservice" -ForegroundColor Yellow
cd "C:\Users\<USER>\Desktop\plateforme intelligente pour gérer une équipe de volley-ball\microservices\planning-performance-service"
Write-Host "Répertoire actuel : $(Get-Location)" -ForegroundColor Cyan

# ÉTAPE 2 : Vérification Docker
Write-Host "`nÉTAPE 2 : Vérification de Docker" -ForegroundColor Yellow
docker --version
docker ps

# ÉTAPE 3 : Nettoyage des conteneurs existants
Write-Host "`nÉTAPE 3 : Nettoyage des conteneurs existants" -ForegroundColor Yellow
docker stop planning-performance-db 2>$null
docker rm planning-performance-db 2>$null
docker stop planning-performance-backend 2>$null
docker rm planning-performance-backend 2>$null
docker stop planning-performance-frontend 2>$null
docker rm planning-performance-frontend 2>$null

# ÉTAPE 4 : Démarrage de PostgreSQL
Write-Host "`nÉTAPE 4 : Démarrage de la base de données PostgreSQL" -ForegroundColor Yellow
docker run -d --name planning-performance-db `
  -e POSTGRES_DB=planning_performance_db `
  -e POSTGRES_USER=planning_user `
  -e POSTGRES_PASSWORD=planning_password `
  -p 5434:5432 `
  postgres:15-alpine

Write-Host "Attente du démarrage de PostgreSQL (20 secondes)..." -ForegroundColor Cyan
Start-Sleep -Seconds 20

# ÉTAPE 5 : Vérification de la base de données
Write-Host "`nÉTAPE 5 : Vérification de la base de données" -ForegroundColor Yellow
docker exec planning-performance-db pg_isready -U planning_user -d planning_performance_db

# ÉTAPE 6 : Construction du backend
Write-Host "`nÉTAPE 6 : Construction et démarrage du backend" -ForegroundColor Yellow
cd backend
Write-Host "Construction de l'image Docker backend..." -ForegroundColor Cyan
docker build -t planning-performance-backend .

Write-Host "Démarrage du conteneur backend..." -ForegroundColor Cyan
docker run -d --name planning-performance-backend `
  -p 8082:8082 `
  -e SPRING_PROFILES_ACTIVE=dev `
  -e SPRING_DATASOURCE_URL="*******************************************************************" `
  -e SPRING_DATASOURCE_USERNAME=planning_user `
  -e SPRING_DATASOURCE_PASSWORD=planning_password `
  planning-performance-backend

Write-Host "Attente du démarrage du backend (30 secondes)..." -ForegroundColor Cyan
Start-Sleep -Seconds 30

# ÉTAPE 7 : Construction du frontend
Write-Host "`nÉTAPE 7 : Construction et démarrage du frontend" -ForegroundColor Yellow
cd ../frontend
Write-Host "Construction de l'image Docker frontend..." -ForegroundColor Cyan
docker build -t planning-performance-frontend .

Write-Host "Démarrage du conteneur frontend..." -ForegroundColor Cyan
docker run -d --name planning-performance-frontend `
  -p 4202:80 `
  planning-performance-frontend

Write-Host "Attente du démarrage du frontend (15 secondes)..." -ForegroundColor Cyan
Start-Sleep -Seconds 15

# ÉTAPE 8 : Vérification des conteneurs
Write-Host "`nÉTAPE 8 : Vérification de l'état des conteneurs" -ForegroundColor Yellow
docker ps

# ÉTAPE 9 : Tests des services
Write-Host "`nÉTAPE 9 : Tests des services" -ForegroundColor Yellow

Write-Host "Test du Backend Health Check..." -ForegroundColor Cyan
try {
    $healthResponse = Invoke-RestMethod -Uri "http://localhost:8082/actuator/health" -TimeoutSec 10
    Write-Host "Backend Health : $($healthResponse.status)" -ForegroundColor Green
    $healthResponse | ConvertTo-Json
} catch {
    Write-Host "Erreur Health Check : $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nTest du Frontend..." -ForegroundColor Cyan
try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:4202" -TimeoutSec 10 -UseBasicParsing
    Write-Host "Frontend Status : $($frontendResponse.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "Erreur Frontend : $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nTest API Entraînements..." -ForegroundColor Cyan
try {
    $entrainementsResponse = Invoke-RestMethod -Uri "http://localhost:8082/api/entrainements" -TimeoutSec 10
    Write-Host "API Entraînements : OK" -ForegroundColor Green
    Write-Host "Nombre d'entraînements : $($entrainementsResponse.Count)" -ForegroundColor Cyan
} catch {
    Write-Host "Erreur API Entraînements : $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nTest API Performances..." -ForegroundColor Cyan
try {
    $performancesResponse = Invoke-RestMethod -Uri "http://localhost:8082/api/performances" -TimeoutSec 10
    Write-Host "API Performances : OK" -ForegroundColor Green
    Write-Host "Nombre de performances : $($performancesResponse.Count)" -ForegroundColor Cyan
} catch {
    Write-Host "Erreur API Performances : $($_.Exception.Message)" -ForegroundColor Red
}

# ÉTAPE 10 : Test de création d'entraînement
Write-Host "`nÉTAPE 10 : Test de création d'entraînement" -ForegroundColor Yellow
try {
    $body = @{
        titre = "Entraînement Test COK"
        description = "Test du microservice Planning Performance - Club Olympique de Kelibia"
        date = "2024-08-06"
        heureDebut = "18:00"
        heureFin = "20:00"
        lieu = "Gymnase Municipal Kelibia"
        type = "TECHNIQUE"
        intensite = 7
    } | ConvertTo-Json

    $createResponse = Invoke-RestMethod -Uri "http://localhost:8082/api/entrainements" `
      -Method POST `
      -Body $body `
      -ContentType "application/json" `
      -TimeoutSec 10

    Write-Host "Création d'entraînement : SUCCÈS" -ForegroundColor Green
    Write-Host "ID de l'entraînement créé : $($createResponse.id)" -ForegroundColor Cyan
    $createResponse | ConvertTo-Json
} catch {
    Write-Host "Erreur création entraînement : $($_.Exception.Message)" -ForegroundColor Red
}

# ÉTAPE 11 : Vérification finale
Write-Host "`nÉTAPE 11 : Vérification finale des entraînements" -ForegroundColor Yellow
try {
    $finalCheck = Invoke-RestMethod -Uri "http://localhost:8082/api/entrainements" -TimeoutSec 10
    Write-Host "Vérification finale : OK" -ForegroundColor Green
    Write-Host "Total entraînements : $($finalCheck.Count)" -ForegroundColor Cyan
    if ($finalCheck.Count -gt 0) {
        Write-Host "Dernier entraînement : $($finalCheck[-1].titre)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "Erreur vérification finale : $($_.Exception.Message)" -ForegroundColor Red
}

# ÉTAPE 12 : Ouverture des interfaces
Write-Host "`nÉTAPE 12 : Ouverture des interfaces web" -ForegroundColor Yellow
Write-Host "Ouverture du frontend Angular..." -ForegroundColor Cyan
Start-Process "http://localhost:4202"

Write-Host "Ouverture du backend health check..." -ForegroundColor Cyan
Start-Process "http://localhost:8082/actuator/health"

# RÉSUMÉ FINAL
Write-Host "`n=== RÉSUMÉ DU TEST ===" -ForegroundColor Green
Write-Host "URLs d'accès :" -ForegroundColor Cyan
Write-Host "  Frontend Angular    : http://localhost:4202" -ForegroundColor White
Write-Host "  Backend Spring Boot : http://localhost:8082" -ForegroundColor White
Write-Host "  API Health Check    : http://localhost:8082/actuator/health" -ForegroundColor White
Write-Host "  API Entraînements   : http://localhost:8082/api/entrainements" -ForegroundColor White
Write-Host "  API Performances    : http://localhost:8082/api/performances" -ForegroundColor White
Write-Host "  Base de données     : localhost:5434" -ForegroundColor White

Write-Host "`nCommandes utiles :" -ForegroundColor Cyan
Write-Host "  docker ps                                    # Voir les conteneurs" -ForegroundColor White
Write-Host "  docker logs planning-performance-backend     # Logs backend" -ForegroundColor White
Write-Host "  docker logs planning-performance-frontend    # Logs frontend" -ForegroundColor White
Write-Host "  docker logs planning-performance-db          # Logs base de données" -ForegroundColor White

Write-Host "`nPour arrêter les services :" -ForegroundColor Yellow
Write-Host "  docker stop planning-performance-frontend planning-performance-backend planning-performance-db" -ForegroundColor White
Write-Host "  docker rm planning-performance-frontend planning-performance-backend planning-performance-db" -ForegroundColor White

Write-Host "`n🎉 TEST DU MICROSERVICE PLANNING PERFORMANCE TERMINÉ !" -ForegroundColor Green
Write-Host "🏐 Club Olympique de Kelibia - Microservice opérationnel !" -ForegroundColor Cyan

# Retour au répertoire racine
cd "C:\Users\<USER>\Desktop\plateforme intelligente pour gérer une équipe de volley-ball"
