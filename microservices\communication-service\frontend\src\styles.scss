/* Styles globaux pour l'application Communication Service */

// Import Angular Material theme
@import '~@angular/material/theming';

// Include the common styles for Angular Material
@include mat-core();

// Définir les palettes de couleurs
$primary-palette: mat-palette($mat-indigo);
$accent-palette: mat-palette($mat-pink, A200, A100, A400);
$warn-palette: mat-palette($mat-red);

// Créer le thème
$app-theme: mat-light-theme((
  color: (
    primary: $primary-palette,
    accent: $accent-palette,
    warn: $warn-palette,
  )
));

// Inclure le thème dans l'application
@include angular-material-theme($app-theme);

/* Reset et styles de base */
* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Roboto', sans-serif;
  background-color: #fafafa;
}

body {
  overflow-x: hidden;
}

/* Typographie */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: 500;
}

p {
  margin: 0 0 16px 0;
  line-height: 1.6;
}

a {
  color: #3f51b5;
  text-decoration: none;
  
  &:hover {
    text-decoration: underline;
  }
}

/* Classes utilitaires */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-8 { margin-top: 8px; }
.mt-16 { margin-top: 16px; }
.mt-24 { margin-top: 24px; }
.mt-32 { margin-top: 32px; }

.mb-8 { margin-bottom: 8px; }
.mb-16 { margin-bottom: 16px; }
.mb-24 { margin-bottom: 24px; }
.mb-32 { margin-bottom: 32px; }

.ml-8 { margin-left: 8px; }
.ml-16 { margin-left: 16px; }
.mr-8 { margin-right: 8px; }
.mr-16 { margin-right: 16px; }

.p-8 { padding: 8px; }
.p-16 { padding: 16px; }
.p-24 { padding: 24px; }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-center { align-items: center; justify-content: center; }
.flex-between { justify-content: space-between; }
.flex-around { justify-content: space-around; }
.flex-wrap { flex-wrap: wrap; }
.flex-1 { flex: 1; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

.cursor-pointer { cursor: pointer; }

/* Styles pour les cartes */
.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin-bottom: 16px;
  
  &.card-hover {
    transition: box-shadow 0.2s ease;
    
    &:hover {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
  }
}

/* Styles pour les messages */
.message-bubble {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 18px;
  margin-bottom: 8px;
  word-wrap: break-word;
  
  &.sent {
    background-color: #3f51b5;
    color: white;
    margin-left: auto;
    border-bottom-right-radius: 4px;
  }
  
  &.received {
    background-color: #e0e0e0;
    color: #333;
    margin-right: auto;
    border-bottom-left-radius: 4px;
  }
  
  &.system {
    background-color: #fff3e0;
    color: #e65100;
    text-align: center;
    font-style: italic;
    margin: 16px auto;
    max-width: 80%;
  }
}

/* Styles pour les notifications */
.notification-item {
  padding: 16px;
  border-left: 4px solid #3f51b5;
  background: white;
  margin-bottom: 8px;
  border-radius: 0 4px 4px 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  &.unread {
    background-color: #f3f4f6;
    border-left-color: #ff5722;
  }
  
  &.success {
    border-left-color: #4caf50;
  }
  
  &.warning {
    border-left-color: #ff9800;
  }
  
  &.error {
    border-left-color: #f44336;
  }
}

/* Styles pour les avatars */
.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #3f51b5;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: 16px;
  
  &.small {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
  
  &.large {
    width: 56px;
    height: 56px;
    font-size: 20px;
  }
}

/* Styles pour les badges de statut */
.status-badge {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
  position: absolute;
  bottom: 0;
  right: 0;
  
  &.online { background-color: #4caf50; }
  &.away { background-color: #ff9800; }
  &.busy { background-color: #f44336; }
  &.offline { background-color: #9e9e9e; }
}

/* Styles pour les fichiers */
.file-preview {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
  object-fit: cover;
  cursor: pointer;
  
  &:hover {
    opacity: 0.8;
  }
}

.file-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 8px;
  margin: 8px 0;
  
  .file-icon {
    margin-right: 12px;
    color: #666;
  }
  
  .file-info {
    flex: 1;
    
    .file-name {
      font-weight: 500;
      margin-bottom: 4px;
    }
    
    .file-size {
      font-size: 12px;
      color: #666;
    }
  }
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

.pulse {
  animation: pulse 1.5s infinite;
}

/* Styles pour les indicateurs de frappe */
.typing-indicator {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  color: #666;
  font-style: italic;
  
  .dots {
    margin-left: 8px;
    
    span {
      display: inline-block;
      width: 4px;
      height: 4px;
      border-radius: 50%;
      background-color: #666;
      margin: 0 1px;
      animation: pulse 1.4s infinite ease-in-out;
      
      &:nth-child(1) { animation-delay: -0.32s; }
      &:nth-child(2) { animation-delay: -0.16s; }
      &:nth-child(3) { animation-delay: 0s; }
    }
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .message-bubble {
    max-width: 85%;
  }
  
  .card {
    padding: 16px;
    margin-bottom: 12px;
  }
  
  .avatar {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .message-bubble {
    max-width: 90%;
    padding: 10px 14px;
  }
  
  .card {
    padding: 12px;
  }
  
  .notification-item {
    padding: 12px;
  }
}

/* Scrollbar personnalisée */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  
  &:hover {
    background: #a8a8a8;
  }
}

/* Styles pour les tooltips personnalisés */
.custom-tooltip {
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  max-width: 200px;
  word-wrap: break-word;
}

/* Styles pour les modales */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  padding: 24px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

/* Classes d'état */
.loading {
  pointer-events: none;
  opacity: 0.6;
}

.error {
  color: #f44336;
}

.success {
  color: #4caf50;
}

.warning {
  color: #ff9800;
}

.info {
  color: #2196f3;
}
