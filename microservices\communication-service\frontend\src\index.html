<!doctype html>
<html lang="fr">
<head>
  <meta charset="utf-8">
  <title>SprintBot - Communication Service</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Service de communication SprintBot - Messagerie, notifications et chatbot intelligent">
  <meta name="keywords" content="SprintBot, communication, messagerie, chat, notifications, volleyball">
  <meta name="author" content="SprintBot Team">
  
  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <link rel="apple-touch-icon" href="assets/icons/icon-192x192.png">
  
  <!-- Manifest pour PWA -->
  <link rel="manifest" href="manifest.json">
  
  <!-- Theme color -->
  <meta name="theme-color" content="#3f51b5">
  
  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.gstatic.com">
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  
  <!-- Preload critical resources -->
  <link rel="preload" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap" as="style">
  
  <!-- Meta tags pour les réseaux sociaux -->
  <meta property="og:title" content="SprintBot - Communication Service">
  <meta property="og:description" content="Service de communication pour la gestion d'équipes de volleyball">
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://sprintbot.com/communication">
  <meta property="og:image" content="assets/images/og-image.png">
  
  <!-- Twitter Card -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="SprintBot - Communication Service">
  <meta name="twitter:description" content="Service de communication pour la gestion d'équipes de volleyball">
  <meta name="twitter:image" content="assets/images/twitter-card.png">
  
  <!-- Configuration pour les notifications push -->
  <script>
    // Configuration VAPID pour les notifications push
    window.vapidPublicKey = 'BEl62iUYgUivxIkv69yViEuiBIa40HI6YrrfuWt7jgSFm7_HrfCee_V6YFgqi3HrR5BYmThA4d1YPKQx0_GYDCw';
  </script>
</head>
<body class="mat-typography">
  <!-- Conteneur principal de l'application -->
  <app-root>
    <!-- Écran de chargement initial -->
    <div id="initial-loader" style="
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #3f51b5;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: white;
      font-family: 'Roboto', sans-serif;
      z-index: 9999;
    ">
      <!-- Logo ou spinner -->
      <div style="
        width: 60px;
        height: 60px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
      "></div>
      
      <h2 style="margin: 0; font-weight: 300; font-size: 24px;">SprintBot</h2>
      <p style="margin: 8px 0 0 0; font-weight: 300; opacity: 0.8;">Communication Service</p>
    </div>
  </app-root>
  
  <!-- Styles pour l'écran de chargement -->
  <style>
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    /* Masquer le loader une fois Angular chargé */
    app-root:not(:empty) + #initial-loader {
      display: none;
    }
  </style>
  
  <!-- Script pour masquer le loader -->
  <script>
    // Masquer le loader après le chargement complet
    window.addEventListener('load', function() {
      setTimeout(function() {
        const loader = document.getElementById('initial-loader');
        if (loader) {
          loader.style.opacity = '0';
          loader.style.transition = 'opacity 0.5s ease';
          setTimeout(function() {
            loader.style.display = 'none';
          }, 500);
        }
      }, 1000);
    });
  </script>
  
  <!-- Service Worker pour PWA -->
  <script>
    if ('serviceWorker' in navigator && location.protocol === 'https:') {
      navigator.serviceWorker.register('/sw.js')
        .then(function(registration) {
          console.log('Service Worker enregistré avec succès:', registration);
        })
        .catch(function(error) {
          console.log('Échec de l\'enregistrement du Service Worker:', error);
        });
    }
  </script>
</body>
</html>
