package com.sprintbot.communication.websocket;

import com.sprintbot.communication.service.UserPresenceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.WebSocketHandlerDecorator;
import org.springframework.web.socket.handler.WebSocketHandlerDecoratorFactory;
import org.springframework.web.socket.messaging.SessionConnectedEvent;
import org.springframework.web.socket.messaging.SessionDisconnectEvent;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.springframework.lang.NonNull;

/**
 * Gestionnaire d'événements WebSocket pour la gestion des connexions/déconnexions
 */
@Component
public class WebSocketEventHandler implements WebSocketHandlerDecoratorFactory {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketEventHandler.class);

    @Autowired
    private UserPresenceService presenceService;

    // Map pour stocker les sessions actives
    private final Map<String, Long> sessionUtilisateur = new ConcurrentHashMap<>();

    @Override
    @NonNull
    public WebSocketHandler decorate(@NonNull WebSocketHandler handler) {
        return new WebSocketHandlerDecorator(handler) {
            @Override
            public void afterConnectionEstablished(@NonNull WebSocketSession session) throws Exception {
                logger.debug("Nouvelle connexion WebSocket établie: {}", session.getId());
                super.afterConnectionEstablished(session);
            }

            @Override
            public void afterConnectionClosed(@NonNull WebSocketSession session, @NonNull CloseStatus closeStatus) throws Exception {
                logger.debug("Connexion WebSocket fermée: {} - Status: {}", session.getId(), closeStatus);
                
                // Nettoyer la session de présence
                Long utilisateurId = sessionUtilisateur.remove(session.getId());
                if (utilisateurId != null) {
                    presenceService.deconnecterParSession(session.getId());
                }
                
                super.afterConnectionClosed(session, closeStatus);
            }
        };
    }

    /**
     * Gère l'événement de connexion STOMP
     */
    @EventListener
    public void handleWebSocketConnectListener(SessionConnectedEvent event) {
        try {
            StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
            String sessionId = headerAccessor.getSessionId();
            
            // Extraire les informations utilisateur des headers
            String utilisateurIdStr = headerAccessor.getFirstNativeHeader("utilisateurId");
            String plateforme = headerAccessor.getFirstNativeHeader("plateforme");
            String estMobileStr = headerAccessor.getFirstNativeHeader("estMobile");

            if (utilisateurIdStr != null) {
                Long utilisateurId = Long.parseLong(utilisateurIdStr);
                boolean estMobile = Boolean.parseBoolean(estMobileStr);
                
                // Stocker la session
                sessionUtilisateur.put(sessionId, utilisateurId);
                
                // Mettre à jour la présence
                presenceService.definirEnLigne(
                        utilisateurId, 
                        sessionId, 
                        plateforme != null ? plateforme : "web", 
                        estMobile
                );

                logger.info("Utilisateur {} connecté via WebSocket - Session: {}", utilisateurId, sessionId);
            }

        } catch (Exception e) {
            logger.error("Erreur lors de la gestion de connexion WebSocket: {}", e.getMessage(), e);
        }
    }

    /**
     * Gère l'événement de déconnexion STOMP
     */
    @EventListener
    public void handleWebSocketDisconnectListener(SessionDisconnectEvent event) {
        try {
            StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
            String sessionId = headerAccessor.getSessionId();

            // Récupérer l'utilisateur associé à cette session
            Long utilisateurId = sessionUtilisateur.remove(sessionId);

            if (utilisateurId != null) {
                // Mettre à jour la présence
                presenceService.definirHorsLigne(utilisateurId);

                logger.info("Utilisateur {} déconnecté de WebSocket - Session: {}", utilisateurId, sessionId);
            }

        } catch (Exception e) {
            logger.error("Erreur lors de la gestion de déconnexion WebSocket: {}", e.getMessage(), e);
        }
    }

    /**
     * Obtient l'utilisateur associé à une session
     */
    public Long getUtilisateurParSession(String sessionId) {
        return sessionUtilisateur.get(sessionId);
    }

    /**
     * Obtient toutes les sessions actives
     */
    public Map<String, Long> getSessionsActives() {
        return new ConcurrentHashMap<>(sessionUtilisateur);
    }

    /**
     * Compte le nombre de sessions actives
     */
    public int getNombreSessionsActives() {
        return sessionUtilisateur.size();
    }

    /**
     * Vérifie si un utilisateur a une session active
     */
    public boolean utilisateurConnecte(Long utilisateurId) {
        return sessionUtilisateur.containsValue(utilisateurId);
    }

    /**
     * Obtient la session d'un utilisateur
     */
    public String getSessionUtilisateur(Long utilisateurId) {
        return sessionUtilisateur.entrySet().stream()
                .filter(entry -> entry.getValue().equals(utilisateurId))
                .map(Map.Entry::getKey)
                .findFirst()
                .orElse(null);
    }
}
