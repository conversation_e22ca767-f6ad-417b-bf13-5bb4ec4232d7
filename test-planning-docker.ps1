Write-Host "=== Test Microservice Planning Performance avec Docker ===" -ForegroundColor Green
Write-Host "Club Olympique de Kelibia - Test Planning Performance Service" -ForegroundColor Cyan
Write-Host ""

Write-Host "1. Verification de Docker..." -ForegroundColor Yellow

try {
    $dockerVersion = docker --version
    Write-Host "   Docker installe : $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "   Docker non trouve. Veuillez installer Docker" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "2. Nettoyage des conteneurs existants..." -ForegroundColor Yellow

# Arreter et supprimer les conteneurs existants
Write-Host "   Arret des conteneurs planning-performance..." -ForegroundColor Cyan
docker stop planning-performance-db 2>$null
docker rm planning-performance-db 2>$null

Write-Host ""
Write-Host "3. Demarrage de la base de donnees PostgreSQL..." -ForegroundColor Yellow

# Aller dans le repertoire du microservice
Set-Location "microservices\planning-performance-service"

# Demarrer la base de donnees
Write-Host "   Demarrage du conteneur PostgreSQL..." -ForegroundColor Cyan
docker-compose up -d planning-performance-db

# Attendre que la base soit prete
Write-Host "   Attente du demarrage de PostgreSQL (20 secondes)..." -ForegroundColor Cyan
Start-Sleep -Seconds 20

# Verifier la connexion a la base
Write-Host "   Verification de la base de donnees..." -ForegroundColor Cyan
try {
    $dbCheck = docker exec planning-performance-db pg_isready -U planning_user -d planning_performance_db 2>$null
    if ($dbCheck -like "*accepting connections*") {
        Write-Host "   Base de donnees PostgreSQL prete!" -ForegroundColor Green
    } else {
        Write-Host "   Base de donnees en cours de demarrage..." -ForegroundColor Yellow
        Start-Sleep -Seconds 10
    }
} catch {
    Write-Host "   Base de donnees en cours de demarrage..." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "4. Construction et demarrage du backend avec Docker..." -ForegroundColor Yellow

# Construire l'image Docker du backend
Write-Host "   Construction de l'image Docker backend..." -ForegroundColor Cyan
Set-Location "backend"
docker build -t planning-performance-backend . 2>$null

if ($LASTEXITCODE -eq 0) {
    Write-Host "   Image backend construite avec succes!" -ForegroundColor Green
    
    # Demarrer le conteneur backend
    Write-Host "   Demarrage du conteneur backend..." -ForegroundColor Cyan
    docker run -d --name planning-performance-backend `
        --network planning-performance-service_planning-performance-network `
        -p 8082:8082 `
        -e SPRING_DATASOURCE_URL="**********************************************************************" `
        -e SPRING_DATASOURCE_USERNAME="planning_user" `
        -e SPRING_DATASOURCE_PASSWORD="planning_password" `
        planning-performance-backend
        
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   Backend demarre avec succes!" -ForegroundColor Green
    } else {
        Write-Host "   Erreur lors du demarrage du backend" -ForegroundColor Red
    }
} else {
    Write-Host "   Erreur lors de la construction de l'image backend" -ForegroundColor Red
}

Set-Location ".."

Write-Host ""
Write-Host "5. Construction et demarrage du frontend avec Docker..." -ForegroundColor Yellow

# Construire l'image Docker du frontend
Write-Host "   Construction de l'image Docker frontend..." -ForegroundColor Cyan
Set-Location "frontend"
docker build -t planning-performance-frontend . 2>$null

if ($LASTEXITCODE -eq 0) {
    Write-Host "   Image frontend construite avec succes!" -ForegroundColor Green
    
    # Demarrer le conteneur frontend
    Write-Host "   Demarrage du conteneur frontend..." -ForegroundColor Cyan
    docker run -d --name planning-performance-frontend `
        -p 4202:80 `
        planning-performance-frontend
        
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   Frontend demarre avec succes!" -ForegroundColor Green
    } else {
        Write-Host "   Erreur lors du demarrage du frontend" -ForegroundColor Red
    }
} else {
    Write-Host "   Erreur lors de la construction de l'image frontend" -ForegroundColor Red
}

# Retourner au repertoire racine
Set-Location "..\..\.."

Write-Host ""
Write-Host "6. Verification des services..." -ForegroundColor Yellow
Write-Host "   Attente du demarrage complet (30 secondes)..." -ForegroundColor Cyan
Start-Sleep -Seconds 30

# Fonction pour verifier un service
function Test-Service {
    param(
        [string]$ServiceName,
        [string]$Url
    )
    
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec 10 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "   $ServiceName : OK" -ForegroundColor Green
            return $true
        } else {
            Write-Host "   $ServiceName : Status $($response.StatusCode)" -ForegroundColor Yellow
            return $false
        }
    } catch {
        Write-Host "   $ServiceName : Non accessible ($($_.Exception.Message))" -ForegroundColor Red
        return $false
    }
}

# Verifier les services
Write-Host "   Verification des services..." -ForegroundColor Cyan
$backendOk = Test-Service -ServiceName "Backend Spring Boot" -Url "http://localhost:8082/actuator/health"
$frontendOk = Test-Service -ServiceName "Frontend Angular" -Url "http://localhost:4202"

# Verifier les conteneurs Docker
Write-Host ""
Write-Host "   Etat des conteneurs Docker :" -ForegroundColor Cyan
$containers = docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | Where-Object { $_ -like "*planning-performance*" }
if ($containers) {
    $containers | ForEach-Object { Write-Host "   $_" -ForegroundColor White }
} else {
    Write-Host "   Aucun conteneur planning-performance en cours d'execution" -ForegroundColor Yellow
}

Write-Host ""
if ($backendOk -or $frontendOk) {
    Write-Host "=== Microservice Planning Performance Demarre ===" -ForegroundColor Green
} else {
    Write-Host "=== Problemes detectes lors du demarrage ===" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "URLs d'acces :" -ForegroundColor Cyan
Write-Host "   Frontend Angular    : http://localhost:4202" -ForegroundColor White
Write-Host "   Backend Spring Boot : http://localhost:8082" -ForegroundColor White
Write-Host "   API Health Check    : http://localhost:8082/actuator/health" -ForegroundColor White
Write-Host "   Base de donnees     : localhost:5434" -ForegroundColor White
Write-Host ""
Write-Host "Test des APIs :" -ForegroundColor Cyan
Write-Host "   curl http://localhost:8082/api/entrainements" -ForegroundColor White
Write-Host "   curl http://localhost:8082/api/performances" -ForegroundColor White
Write-Host "   curl http://localhost:8082/actuator/health" -ForegroundColor White
Write-Host ""
Write-Host "Pour arreter les services :" -ForegroundColor Yellow
Write-Host "   docker stop planning-performance-backend planning-performance-frontend planning-performance-db" -ForegroundColor White
Write-Host "   docker rm planning-performance-backend planning-performance-frontend planning-performance-db" -ForegroundColor White

Read-Host "Appuyez sur Entree pour continuer"
