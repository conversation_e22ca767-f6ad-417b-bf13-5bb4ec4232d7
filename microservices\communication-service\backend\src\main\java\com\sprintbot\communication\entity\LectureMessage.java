package com.sprintbot.communication.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * Entité représentant la lecture d'un message par un utilisateur
 */
@Entity
@Table(name = "lectures_message",
       uniqueConstraints = @UniqueConstraint(columnNames = {"message_id", "utilisateur_id"}))
@EntityListeners(AuditingEntityListener.class)
public class LectureMessage {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "L'ID de l'utilisateur est obligatoire")
    @Column(name = "utilisateur_id", nullable = false)
    private Long utilisateurId;

    @CreatedDate
    @Column(name = "date_lecture", nullable = false)
    private LocalDateTime dateLecture;

    // Relations
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "message_id", nullable = false)
    private Message message;

    // Constructeurs
    public LectureMessage() {}

    public LectureMessage(Message message, Long utilisateurId) {
        this.message = message;
        this.utilisateurId = utilisateurId;
    }

    // Getters et Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUtilisateurId() {
        return utilisateurId;
    }

    public void setUtilisateurId(Long utilisateurId) {
        this.utilisateurId = utilisateurId;
    }

    public LocalDateTime getDateLecture() {
        return dateLecture;
    }

    public void setDateLecture(LocalDateTime dateLecture) {
        this.dateLecture = dateLecture;
    }

    public Message getMessage() {
        return message;
    }

    public void setMessage(Message message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return "LectureMessage{" +
                "id=" + id +
                ", utilisateurId=" + utilisateurId +
                ", dateLecture=" + dateLecture +
                '}';
    }
}
