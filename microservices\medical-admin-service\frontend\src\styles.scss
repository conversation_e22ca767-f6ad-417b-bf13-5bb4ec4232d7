/* Styles globaux pour Medical Admin Frontend */

/* Variables CSS personnalisées */
:root {
  --primary-color: #2196F3;
  --secondary-color: #4CAF50;
  --accent-color: #FF9800;
  --warn-color: #F44336;
  --success-color: #4CAF50;
  --info-color: #2196F3;
  --warning-color: #FF9800;
  --danger-color: #F44336;
  
  --background-color: #f5f5f5;
  --surface-color: #ffffff;
  --text-primary: #212529;
  --text-secondary: #6c757d;
  
  --border-radius: 8px;
  --box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  --transition: all 0.3s ease;
}

/* Reset et styles de base */
* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Roboto', sans-serif;
  background-color: var(--background-color);
  color: var(--text-primary);
}

/* Styles pour les cartes */
.card {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  background-color: var(--surface-color);
}

.card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}

.card-header {
  background-color: var(--primary-color);
  color: white;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  padding: 1rem 1.5rem;
  font-weight: 500;
}

.card-body {
  padding: 1.5rem;
}

/* Styles pour les boutons */
.btn {
  border-radius: var(--border-radius);
  transition: var(--transition);
  font-weight: 500;
  padding: 0.5rem 1rem;
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-success {
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.btn-warning {
  background-color: var(--warning-color);
  border-color: var(--warning-color);
}

.btn-danger {
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.btn-info {
  background-color: var(--info-color);
  border-color: var(--info-color);
}

/* Styles pour les formulaires */
.form-control {
  border-radius: var(--border-radius);
  border: 1px solid #dee2e6;
  transition: var(--transition);
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(33, 150, 243, 0.25);
}

.form-label {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

/* Styles pour les badges */
.badge {
  border-radius: var(--border-radius);
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.375rem 0.75rem;
}

.badge-status-actif { background-color: var(--success-color); }
.badge-status-en-traitement { background-color: var(--warning-color); }
.badge-status-gueri { background-color: var(--info-color); }
.badge-status-suivi { background-color: var(--secondary-color); }

.badge-priorite-faible { background-color: #6c757d; }
.badge-priorite-normale { background-color: var(--info-color); }
.badge-priorite-elevee { background-color: var(--warning-color); }
.badge-priorite-urgente { background-color: var(--danger-color); }

/* Styles pour les tableaux */
.table {
  background-color: var(--surface-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.table thead th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: var(--text-primary);
}

.table tbody tr:hover {
  background-color: #f8f9fa;
}

/* Styles pour la navigation */
.navbar {
  box-shadow: var(--box-shadow);
  background-color: var(--surface-color) !important;
}

.navbar-brand {
  font-weight: 600;
  color: var(--primary-color) !important;
}

.nav-link {
  color: var(--text-primary) !important;
  transition: var(--transition);
}

.nav-link:hover {
  color: var(--primary-color) !important;
}

/* Styles pour les alertes */
.alert {
  border-radius: var(--border-radius);
  border: none;
  padding: 1rem 1.5rem;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
}

.alert-warning {
  background-color: #fff3cd;
  color: #856404;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
}

.alert-info {
  background-color: #d1ecf1;
  color: #0c5460;
}

/* Styles pour les modales */
.modal-content {
  border-radius: var(--border-radius);
  border: none;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
  background-color: var(--primary-color);
  color: white;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-footer {
  border-top: 1px solid #dee2e6;
  padding: 1rem 1.5rem;
}

/* Styles pour les icônes */
.icon {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 0.5rem;
}

.icon-lg {
  width: 2rem;
  height: 2rem;
}

/* Styles pour les statistiques */
.stat-card {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  text-align: center;
  box-shadow: var(--box-shadow);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.875rem;
  opacity: 0.9;
}

/* Styles pour les listes */
.list-group-item {
  border: 1px solid #dee2e6;
  transition: var(--transition);
}

.list-group-item:hover {
  background-color: #f8f9fa;
}

.list-group-item.active {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

/* Styles pour les spinners de chargement */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

.spinner-border {
  color: var(--primary-color);
}

/* Styles pour les breadcrumbs */
.breadcrumb {
  background-color: transparent;
  padding: 0.75rem 0;
  margin-bottom: 1rem;
}

.breadcrumb-item a {
  color: var(--primary-color);
  text-decoration: none;
}

.breadcrumb-item.active {
  color: var(--text-secondary);
}

/* Styles pour les tooltips */
.tooltip {
  font-size: 0.875rem;
}

/* Styles responsifs */
@media (max-width: 768px) {
  .card-body {
    padding: 1rem;
  }
  
  .stat-number {
    font-size: 2rem;
  }
  
  .table-responsive {
    font-size: 0.875rem;
  }
}

/* Styles pour les animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Styles pour les états de validation */
.is-valid {
  border-color: var(--success-color);
}

.is-invalid {
  border-color: var(--danger-color);
}

.valid-feedback {
  color: var(--success-color);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.invalid-feedback {
  color: var(--danger-color);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Styles pour les calendriers */
.calendar-day {
  border: 1px solid #dee2e6;
  padding: 0.5rem;
  text-align: center;
  cursor: pointer;
  transition: var(--transition);
}

.calendar-day:hover {
  background-color: #f8f9fa;
}

.calendar-day.selected {
  background-color: var(--primary-color);
  color: white;
}

.calendar-day.has-appointment {
  background-color: var(--warning-color);
  color: white;
}

/* Styles pour les graphiques */
.chart-container {
  position: relative;
  height: 300px;
  margin: 1rem 0;
}

/* Utilitaires */
.text-muted {
  color: var(--text-secondary) !important;
}

.bg-light {
  background-color: #f8f9fa !important;
}

.border-radius {
  border-radius: var(--border-radius) !important;
}

.shadow {
  box-shadow: var(--box-shadow) !important;
}

.transition {
  transition: var(--transition) !important;
}
