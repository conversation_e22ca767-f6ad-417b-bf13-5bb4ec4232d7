import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { NavbarComponent } from '@shared/components/navbar/navbar.component';
import { SidebarComponent } from '@shared/components/sidebar/sidebar.component';
import { LoadingSpinnerComponent } from '@shared/components/loading-spinner/loading-spinner.component';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    NavbarComponent,
    SidebarComponent,
    LoadingSpinnerComponent
  ],
  template: `
    <div class="app-container">
      <!-- Navbar -->
      <app-navbar></app-navbar>
      
      <!-- Main content area -->
      <div class="main-content">
        <!-- Sidebar -->
        <app-sidebar class="sidebar"></app-sidebar>
        
        <!-- Page content -->
        <main class="content">
          <router-outlet></router-outlet>
        </main>
      </div>
      
      <!-- Loading spinner global -->
      <app-loading-spinner></app-loading-spinner>
    </div>
  `,
  styles: [`
    .app-container {
      display: flex;
      flex-direction: column;
      height: 100vh;
    }
    
    .main-content {
      display: flex;
      flex: 1;
      overflow: hidden;
    }
    
    .sidebar {
      width: 250px;
      flex-shrink: 0;
      background-color: #f8f9fa;
      border-right: 1px solid #dee2e6;
    }
    
    .content {
      flex: 1;
      padding: 1.5rem;
      overflow-y: auto;
      background-color: #f5f5f5;
    }
    
    @media (max-width: 768px) {
      .main-content {
        flex-direction: column;
      }
      
      .sidebar {
        width: 100%;
        height: auto;
      }
      
      .content {
        padding: 1rem;
      }
    }
  `]
})
export class AppComponent {
  title = 'Medical Admin Service';
}
