// Configuration pour l'environnement de développement
export const environment = {
  production: false,
  apiUrl: 'http://localhost:8080/communication-service',
  wsUrl: 'ws://localhost:8080/communication-service/ws',
  
  // Configuration WebSocket
  websocket: {
    reconnectInterval: 5000,
    maxReconnectAttempts: 10,
    heartbeatInterval: 30000
  },
  
  // Configuration des notifications
  notifications: {
    enablePush: true,
    enableEmail: true,
    enableSms: false,
    defaultDuration: 5000
  },
  
  // Configuration du chatbot
  chatbot: {
    enabled: true,
    maxMessageLength: 1000,
    typingIndicatorDelay: 1000,
    suggestionCount: 3
  },
  
  // Configuration des fichiers
  fileUpload: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'video/mp4',
      'video/webm',
      'audio/mp3',
      'audio/wav',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ]
  },
  
  // Configuration de la présence utilisateur
  presence: {
    heartbeatInterval: 30000,
    activityThreshold: 60000,
    offlineThreshold: 300000
  },
  
  // Configuration de l'interface
  ui: {
    theme: 'light',
    language: 'fr',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    messagesPerPage: 50,
    conversationsPerPage: 20,
    notificationsPerPage: 25
  },
  
  // Configuration du cache
  cache: {
    conversationsTtl: 300000, // 5 minutes
    messagesTtl: 600000, // 10 minutes
    usersTtl: 900000 // 15 minutes
  },
  
  // Configuration de sécurité
  security: {
    jwtTokenKey: 'sprintbot_token',
    refreshTokenKey: 'sprintbot_refresh_token',
    sessionTimeout: 3600000 // 1 heure
  },
  
  // Configuration des logs
  logging: {
    level: 'debug',
    enableConsole: true,
    enableRemote: false
  },
  
  // Configuration des fonctionnalités
  features: {
    enableVoiceMessages: true,
    enableVideoMessages: true,
    enableFileSharing: true,
    enableMessageReactions: true,
    enableMessageReplies: true,
    enableMessageForwarding: true,
    enableMessageSearch: true,
    enableConversationArchive: true,
    enableUserBlocking: false,
    enableMessageEncryption: false
  }
};
