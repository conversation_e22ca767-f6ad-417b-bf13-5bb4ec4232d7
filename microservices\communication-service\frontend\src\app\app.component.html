<!-- Layout principal de l'application -->
<div class="app-container">
  <!-- Barr<PERSON> d'outils principale -->
  <mat-toolbar color="primary" class="app-toolbar">
    <button mat-icon-button (click)="sidenav.toggle()" class="menu-button">
      <mat-icon>menu</mat-icon>
    </button>
    
    <span class="app-title">{{ title }}</span>
    
    <span class="spacer"></span>
    
    <!-- Indicateur de connexion -->
    <button mat-icon-button [matTooltip]="isConnected ? 'Connecté' : 'Déconnecté'">
      <mat-icon [color]="getConnectionStatusColor()">{{ getConnectionStatusIcon() }}</mat-icon>
    </button>
    
    <!-- Notifications -->
    <button mat-icon-button (click)="onNotificationClick()" matTooltip="Notifications">
      <mat-icon matBadge="{{ notificationsCount }}" matBadgeColor="warn" 
                [matBadgeHidden]="notificationsCount === 0">
        notifications
      </mat-icon>
    </button>
    
    <!-- Menu utilisateur -->
    <button mat-icon-button [matMenuTriggerFor]="userMenu" matTooltip="Profil utilisateur">
      <mat-icon>account_circle</mat-icon>
    </button>
    
    <!-- Menu déroulant utilisateur -->
    <mat-menu #userMenu="matMenu">
      <div class="user-info">
        <div class="user-name">{{ currentUser?.nom }}</div>
        <div class="user-email">{{ currentUser?.email }}</div>
      </div>
      <mat-divider></mat-divider>
      
      <button mat-menu-item (click)="changeUserStatus('EN_LIGNE')">
        <mat-icon color="primary">circle</mat-icon>
        <span>En ligne</span>
      </button>
      
      <button mat-menu-item (click)="changeUserStatus('ABSENT')">
        <mat-icon color="warn">schedule</mat-icon>
        <span>Absent</span>
      </button>
      
      <button mat-menu-item (click)="changeUserStatus('OCCUPE')">
        <mat-icon color="accent">do_not_disturb</mat-icon>
        <span>Occupé</span>
      </button>
      
      <button mat-menu-item (click)="changeUserStatus('INVISIBLE')">
        <mat-icon>visibility_off</mat-icon>
        <span>Invisible</span>
      </button>
      
      <mat-divider></mat-divider>
      
      <button mat-menu-item (click)="onSettingsClick()">
        <mat-icon>settings</mat-icon>
        <span>Paramètres</span>
      </button>
    </mat-menu>
  </mat-toolbar>

  <!-- Container principal avec sidenav -->
  <mat-sidenav-container class="app-sidenav-container">
    <!-- Navigation latérale -->
    <mat-sidenav #sidenav mode="side" opened class="app-sidenav">
      <mat-nav-list>
        <h3 matSubheader>Navigation</h3>
        
        <a mat-list-item 
           *ngFor="let item of navigationItems" 
           [routerLink]="item.route"
           routerLinkActive="active-nav-item"
           class="nav-item">
          <mat-icon matListIcon>{{ item.icon }}</mat-icon>
          <span matLine>{{ item.label }}</span>
          <span matLine class="nav-badge" *ngIf="item.badge > 0">
            <mat-chip color="warn" selected>{{ item.badge }}</mat-chip>
          </span>
        </a>
        
        <mat-divider></mat-divider>
        
        <!-- Section d'aide -->
        <h3 matSubheader>Aide</h3>
        
        <a mat-list-item routerLink="/chatbot" class="nav-item">
          <mat-icon matListIcon>help</mat-icon>
          <span matLine>Assistant IA</span>
        </a>
        
        <a mat-list-item href="#" class="nav-item">
          <mat-icon matListIcon>info</mat-icon>
          <span matLine>À propos</span>
        </a>
      </mat-nav-list>
    </mat-sidenav>

    <!-- Contenu principal -->
    <mat-sidenav-content class="app-content">
      <!-- Zone de contenu des routes -->
      <div class="content-wrapper">
        <router-outlet></router-outlet>
      </div>
      
      <!-- Indicateur de chargement global -->
      <div class="loading-overlay" *ngIf="!isConnected">
        <mat-spinner diameter="50"></mat-spinner>
        <p>Connexion en cours...</p>
      </div>
    </mat-sidenav-content>
  </mat-sidenav-container>
</div>

<!-- Snackbar pour les notifications toast -->
<div class="snackbar-container"></div>
