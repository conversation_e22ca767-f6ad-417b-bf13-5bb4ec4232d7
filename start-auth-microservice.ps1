Write-Host "=== DEMARRAGE MICROSERVICE AUTH ===" -ForegroundColor Green
Write-Host "Club Olympique de Kelibia" -ForegroundColor Cyan
Write-Host ""

# Aller dans le répertoire du microservice auth
Set-Location "microservices\auth-user-service"

Write-Host "1. Vérification de l'environnement..." -ForegroundColor Yellow

# Vérifier si Docker est disponible
try {
    $dockerVersion = docker --version
    Write-Host "✓ Docker disponible: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker non disponible" -ForegroundColor Red
    Write-Host "Veuillez installer Docker Desktop" -ForegroundColor White
    Read-Host "Appuyez sur Entrée pour continuer"
    exit 1
}

Write-Host ""
Write-Host "2. Arrêt des conteneurs existants..." -ForegroundColor Yellow
docker-compose down --remove-orphans

Write-Host ""
Write-Host "3. Construction et démarrage des services..." -ForegroundColor Yellow
Write-Host "   - Base de données PostgreSQL (port 5433)" -ForegroundColor Cyan
Write-Host "   - Backend Spring Boot (port 8085)" -ForegroundColor Cyan

# Démarrer seulement la base de données et le backend
docker-compose up -d auth-user-db auth-user-service

Write-Host ""
Write-Host "4. Attente du démarrage des services..." -ForegroundColor Yellow
Write-Host "   Cela peut prendre 2-3 minutes..." -ForegroundColor Gray

# Attendre 30 secondes
Start-Sleep -Seconds 30

Write-Host ""
Write-Host "5. Vérification des services..." -ForegroundColor Yellow

# Vérifier les conteneurs
$containers = docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
Write-Host "Conteneurs actifs :" -ForegroundColor Cyan
Write-Host $containers -ForegroundColor White

Write-Host ""
Write-Host "6. Test de connectivité..." -ForegroundColor Yellow

# Tester la base de données
try {
    $dbTest = docker exec auth-user-db pg_isready -U auth_user -d auth_user_db
    if ($dbTest -like "*accepting connections*") {
        Write-Host "✓ Base de données opérationnelle" -ForegroundColor Green
    } else {
        Write-Host "⚠ Base de données en cours de démarrage" -ForegroundColor Yellow
    }
} catch {
    Write-Host "✗ Problème avec la base de données" -ForegroundColor Red
}

# Tester le backend (attendre un peu plus)
Write-Host "   Test du backend Spring Boot..." -ForegroundColor Cyan
Start-Sleep -Seconds 30

try {
    $response = Invoke-WebRequest -Uri "http://localhost:8085/actuator/health" -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Backend Spring Boot opérationnel" -ForegroundColor Green
        $healthData = $response.Content | ConvertFrom-Json
        Write-Host "   Status: $($healthData.status)" -ForegroundColor Gray
    }
} catch {
    Write-Host "⚠ Backend encore en cours de démarrage" -ForegroundColor Yellow
    Write-Host "   Attendez quelques minutes de plus" -ForegroundColor Gray
}

Write-Host ""
Write-Host "URLS DISPONIBLES :" -ForegroundColor Yellow
Write-Host "==================" -ForegroundColor Yellow
Write-Host "Backend API        : http://localhost:8085" -ForegroundColor White
Write-Host "Health Check       : http://localhost:8085/actuator/health" -ForegroundColor White
Write-Host "Base de données    : localhost:5433 (auth_user/auth_user_password)" -ForegroundColor White
Write-Host "Frontend Angular   : http://localhost:55219 (déjà actif)" -ForegroundColor White

Write-Host ""
Write-Host "COMMANDES UTILES :" -ForegroundColor Yellow
Write-Host "==================" -ForegroundColor Yellow
Write-Host "Voir les logs      : docker-compose logs -f auth-user-service" -ForegroundColor Gray
Write-Host "Arrêter les services: docker-compose down" -ForegroundColor Gray
Write-Host "Redémarrer         : docker-compose restart auth-user-service" -ForegroundColor Gray

Write-Host ""
if ($response -and $response.StatusCode -eq 200) {
    Write-Host "🎉 MICROSERVICE AUTH OPÉRATIONNEL !" -ForegroundColor Green
    Write-Host "Vous pouvez maintenant utiliser l'API d'authentification." -ForegroundColor Cyan
} else {
    Write-Host "⏳ MICROSERVICE AUTH EN COURS DE DÉMARRAGE" -ForegroundColor Yellow
    Write-Host "Attendez 2-3 minutes puis testez http://localhost:8085/actuator/health" -ForegroundColor White
}

Write-Host ""
Write-Host "=== CLUB OLYMPIQUE DE KELIBIA ===" -ForegroundColor Green
Write-Host "Microservice Auth configuré !" -ForegroundColor White

# Retourner au répertoire racine
Set-Location "..\..\"

Read-Host "Appuyez sur Entrée pour continuer"
