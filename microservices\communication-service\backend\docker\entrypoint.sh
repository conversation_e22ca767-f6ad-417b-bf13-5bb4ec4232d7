#!/bin/bash

# Script d'entrée pour le conteneur du service de communication
# Auteur: SprintBot Team

set -e

echo "🚀 Démarrage du service de communication SprintBot..."

# Fonction de logging
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

# Fonction d'attente pour les services dépendants
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    local max_attempts=30
    local attempt=1

    log "⏳ Attente de $service_name ($host:$port)..."

    while [ $attempt -le $max_attempts ]; do
        if nc -z "$host" "$port" 2>/dev/null; then
            log "✅ $service_name est disponible"
            return 0
        fi
        
        log "⏳ Tentative $attempt/$max_attempts - $service_name non disponible, attente..."
        sleep 2
        attempt=$((attempt + 1))
    done

    log "❌ Impossible de se connecter à $service_name après $max_attempts tentatives"
    return 1
}

# Attendre les services dépendants
if [ "${WAIT_FOR_DEPENDENCIES:-true}" = "true" ]; then
    # Attendre PostgreSQL
    if [ -n "$DB_HOST" ] && [ -n "$DB_PORT" ]; then
        wait_for_service "$DB_HOST" "$DB_PORT" "PostgreSQL"
    fi

    # Attendre Redis
    if [ -n "$REDIS_HOST" ] && [ -n "$REDIS_PORT" ]; then
        wait_for_service "$REDIS_HOST" "$REDIS_PORT" "Redis"
    fi

    # Attendre le service d'authentification
    if [ -n "$AUTH_SERVICE_HOST" ] && [ -n "$AUTH_SERVICE_PORT" ]; then
        wait_for_service "$AUTH_SERVICE_HOST" "$AUTH_SERVICE_PORT" "Service d'authentification"
    fi
fi

# Configuration des variables d'environnement par défaut
export SPRING_PROFILES_ACTIVE=${SPRING_PROFILES_ACTIVE:-docker}
export SERVER_PORT=${SERVER_PORT:-8084}

# Configuration de la base de données
export DB_HOST=${DB_HOST:-postgres}
export DB_PORT=${DB_PORT:-5432}
export DB_NAME=${DB_NAME:-sprintbot_communication}
export DB_USERNAME=${DB_USERNAME:-sprintbot}
export DB_PASSWORD=${DB_PASSWORD:-sprintbot123}

# Configuration Redis
export REDIS_HOST=${REDIS_HOST:-redis}
export REDIS_PORT=${REDIS_PORT:-6379}
export REDIS_PASSWORD=${REDIS_PASSWORD:-}

# Configuration JWT
export JWT_SECRET=${JWT_SECRET:-sprintbot-communication-secret-key-2024}
export JWT_EXPIRATION=${JWT_EXPIRATION:-86400}

# Configuration OpenAI
export OPENAI_API_KEY=${OPENAI_API_KEY:-}
export OPENAI_MODEL=${OPENAI_MODEL:-gpt-3.5-turbo}

# Configuration des notifications
export VAPID_PUBLIC_KEY=${VAPID_PUBLIC_KEY:-}
export VAPID_PRIVATE_KEY=${VAPID_PRIVATE_KEY:-}
export VAPID_SUBJECT=${VAPID_SUBJECT:-mailto:<EMAIL>}

# Configuration SMTP
export SMTP_HOST=${SMTP_HOST:-}
export SMTP_PORT=${SMTP_PORT:-587}
export SMTP_USERNAME=${SMTP_USERNAME:-}
export SMTP_PASSWORD=${SMTP_PASSWORD:-}

# Configuration CORS
export CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS:-http://localhost:4200,http://localhost:4204}

# Options JVM
JAVA_OPTS="${JAVA_OPTS} -Djava.security.egd=file:/dev/./urandom"
JAVA_OPTS="${JAVA_OPTS} -Dspring.profiles.active=${SPRING_PROFILES_ACTIVE}"
JAVA_OPTS="${JAVA_OPTS} -Dserver.port=${SERVER_PORT}"

# Configuration pour le monitoring
if [ "${ENABLE_MONITORING:-false}" = "true" ]; then
    JAVA_OPTS="${JAVA_OPTS} -Dmanagement.endpoints.web.exposure.include=health,info,metrics,prometheus"
    JAVA_OPTS="${JAVA_OPTS} -Dmanagement.endpoint.health.show-details=always"
fi

# Configuration pour le debug
if [ "${ENABLE_DEBUG:-false}" = "true" ]; then
    JAVA_OPTS="${JAVA_OPTS} -Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=*:5005"
    log "🐛 Mode debug activé sur le port 5005"
fi

# Affichage de la configuration
log "📋 Configuration du service:"
log "   - Profil Spring: $SPRING_PROFILES_ACTIVE"
log "   - Port: $SERVER_PORT"
log "   - Base de données: $DB_HOST:$DB_PORT/$DB_NAME"
log "   - Redis: $REDIS_HOST:$REDIS_PORT"
log "   - Options JVM: $JAVA_OPTS"

# Vérification de la santé avant démarrage
if [ "${HEALTH_CHECK_ENABLED:-true}" = "true" ]; then
    log "🏥 Vérifications de santé activées"
fi

# Démarrage de l'application
log "🎯 Démarrage de l'application Spring Boot..."

exec java $JAVA_OPTS -jar app.jar
