Write-Host "=== Test Simple du Microservice Planning Performance ===" -ForegroundColor Green
Write-Host "Club Olympique de Kelibia" -ForegroundColor Cyan
Write-Host ""

Write-Host "1. Verification de la structure du microservice..." -ForegroundColor Yellow

$planningPath = "microservices\planning-performance-service"

if (Test-Path $planningPath) {
    Write-Host "   Microservice Planning Performance trouve!" -ForegroundColor Green
    
    # Verifier les composants
    $backendPath = "$planningPath\backend"
    $frontendPath = "$planningPath\frontend"
    $dockerComposePath = "$planningPath\docker-compose.yml"
    
    if (Test-Path $backendPath) {
        Write-Host "   Backend Spring Boot : OK" -ForegroundColor Green
    } else {
        Write-Host "   Backend Spring Boot : MANQUANT" -ForegroundColor Red
    }
    
    if (Test-Path $frontendPath) {
        Write-Host "   Frontend Angular : OK" -ForegroundColor Green
    } else {
        Write-Host "   Frontend Angular : MANQUANT" -ForegroundColor Red
    }
    
    if (Test-Path $dockerComposePath) {
        Write-Host "   Docker Compose : OK" -ForegroundColor Green
    } else {
        Write-Host "   Docker Compose : MANQUANT" -ForegroundColor Red
    }
} else {
    Write-Host "   Microservice Planning Performance non trouve!" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "2. Verification de la configuration..." -ForegroundColor Yellow

# Verifier application.yml
$appConfigPath = "$planningPath\backend\src\main\resources\application.yml"
if (Test-Path $appConfigPath) {
    Write-Host "   Configuration Spring Boot : OK" -ForegroundColor Green
    
    # Lire la configuration
    $config = Get-Content $appConfigPath -Raw
    if ($config -like "*port: 8082*") {
        Write-Host "   Port backend configure : 8082" -ForegroundColor Green
    }
    if ($config -like "*planning_performance_db*") {
        Write-Host "   Base de donnees configuree : planning_performance_db" -ForegroundColor Green
    }
} else {
    Write-Host "   Configuration Spring Boot : MANQUANTE" -ForegroundColor Red
}

# Verifier package.json
$packageJsonPath = "$planningPath\frontend\package.json"
if (Test-Path $packageJsonPath) {
    Write-Host "   Configuration Angular : OK" -ForegroundColor Green
} else {
    Write-Host "   Configuration Angular : MANQUANTE" -ForegroundColor Red
}

Write-Host ""
Write-Host "3. Instructions de test manuel..." -ForegroundColor Yellow

Write-Host ""
Write-Host "OPTION 1 - Test avec Docker (Recommande) :" -ForegroundColor Cyan
Write-Host "   1. Ouvrir un terminal PowerShell" -ForegroundColor White
Write-Host "   2. cd microservices\planning-performance-service" -ForegroundColor White
Write-Host "   3. docker-compose up -d planning-performance-db" -ForegroundColor White
Write-Host "   4. Attendre 15 secondes" -ForegroundColor White
Write-Host "   5. cd backend && docker build -t planning-backend ." -ForegroundColor White
Write-Host "   6. docker run -d --name planning-backend -p 8082:8082 planning-backend" -ForegroundColor White
Write-Host "   7. cd ../frontend && docker build -t planning-frontend ." -ForegroundColor White
Write-Host "   8. docker run -d --name planning-frontend -p 4202:80 planning-frontend" -ForegroundColor White

Write-Host ""
Write-Host "OPTION 2 - Test en developpement local :" -ForegroundColor Cyan
Write-Host "   1. Installer PostgreSQL ou utiliser Docker :" -ForegroundColor White
Write-Host "      docker run -d --name planning-db -p 5434:5432 -e POSTGRES_DB=planning_performance_db -e POSTGRES_USER=planning_user -e POSTGRES_PASSWORD=planning_password postgres:15" -ForegroundColor White
Write-Host "   2. Backend (nouveau terminal) :" -ForegroundColor White
Write-Host "      cd microservices\planning-performance-service\backend" -ForegroundColor White
Write-Host "      mvn spring-boot:run" -ForegroundColor White
Write-Host "   3. Frontend (nouveau terminal) :" -ForegroundColor White
Write-Host "      cd microservices\planning-performance-service\frontend" -ForegroundColor White
Write-Host "      npm install && ng serve --port 4202" -ForegroundColor White

Write-Host ""
Write-Host "4. Tests a effectuer apres demarrage..." -ForegroundColor Yellow

Write-Host ""
Write-Host "URLs de test :" -ForegroundColor Cyan
Write-Host "   Frontend Angular    : http://localhost:4202" -ForegroundColor White
Write-Host "   Backend Health      : http://localhost:8082/actuator/health" -ForegroundColor White
Write-Host "   API Entrainements   : http://localhost:8082/api/entrainements" -ForegroundColor White
Write-Host "   API Performances    : http://localhost:8082/api/performances" -ForegroundColor White

Write-Host ""
Write-Host "Commandes de test API :" -ForegroundColor Cyan
Write-Host "   curl http://localhost:8082/actuator/health" -ForegroundColor White
Write-Host "   curl http://localhost:8082/api/entrainements" -ForegroundColor White
Write-Host "   curl http://localhost:8082/api/performances" -ForegroundColor White
Write-Host "   curl http://localhost:8082/api/statistiques" -ForegroundColor White

Write-Host ""
Write-Host "Test de creation d'entrainement :" -ForegroundColor Cyan
Write-Host '   curl -X POST http://localhost:8082/api/entrainements -H "Content-Type: application/json" -d "{"titre":"Test","date":"2024-08-06","heureDebut":"18:00","heureFin":"20:00","lieu":"Gymnase","type":"TECHNIQUE","intensite":7}"' -ForegroundColor White

Write-Host ""
Write-Host "5. Verification des resultats attendus..." -ForegroundColor Yellow

Write-Host ""
Write-Host "Resultats attendus :" -ForegroundColor Cyan
Write-Host "   Backend Health Check : {\"status\":\"UP\"}" -ForegroundColor White
Write-Host "   API Entrainements    : Liste JSON (peut etre vide)" -ForegroundColor White
Write-Host "   Frontend Angular     : Interface utilisateur" -ForegroundColor White
Write-Host "   Creation entrainement: Status 201 Created" -ForegroundColor White

Write-Host ""
Write-Host "En cas de probleme :" -ForegroundColor Yellow
Write-Host "   - Verifier que les ports 4202, 8082, 5434 sont libres" -ForegroundColor White
Write-Host "   - Consulter les logs Docker : docker logs [container-name]" -ForegroundColor White
Write-Host "   - Redemarrer Docker Desktop si necessaire" -ForegroundColor White
Write-Host "   - Utiliser le developpement local si Docker pose probleme" -ForegroundColor White

Write-Host ""
Write-Host "=== Microservice Planning Performance Pret pour Test ===" -ForegroundColor Green
Write-Host "Suivez les instructions ci-dessus pour tester le microservice." -ForegroundColor Cyan

Read-Host "Appuyez sur Entree pour continuer"
