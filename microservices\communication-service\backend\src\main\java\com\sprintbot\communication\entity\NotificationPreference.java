package com.sprintbot.communication.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * Entité représentant les préférences de notification d'un utilisateur
 */
@Entity
@Table(name = "notification_preferences",
       uniqueConstraints = @UniqueConstraint(columnNames = {"utilisateur_id", "type_notification", "canal"}))
@EntityListeners(AuditingEntityListener.class)
public class NotificationPreference {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "L'ID de l'utilisateur est obligatoire")
    @Column(name = "utilisateur_id", nullable = false)
    private Long utilisateurId;

    @NotNull(message = "Le type de notification est obligatoire")
    @Enumerated(EnumType.STRING)
    @Column(name = "type_notification", nullable = false, length = 20)
    private TypeNotification typeNotification;

    @NotNull(message = "Le canal est obligatoire")
    @Enumerated(EnumType.STRING)
    @Column(name = "canal", nullable = false, length = 20)
    private CanalNotification canal;

    @NotNull(message = "Le statut d'activation est obligatoire")
    @Column(name = "active", nullable = false)
    private Boolean active = true;

    @Column(name = "heure_debut_silencieux")
    private LocalTime heureDebutSilencieux; // ex: 22:00

    @Column(name = "heure_fin_silencieux")
    private LocalTime heureFinSilencieux; // ex: 08:00

    @Column(name = "jours_silencieux")
    private String joursSilencieux; // JSON array: ["SATURDAY", "SUNDAY"]

    @Column(name = "frequence_max_par_heure")
    private Integer frequenceMaxParHeure; // Limite de notifications par heure

    @Column(name = "grouper_notifications")
    private Boolean grouperNotifications = false;

    @Column(name = "delai_groupement") // en minutes
    private Integer delaiGroupement = 5;

    @Column(name = "son_personnalise")
    private String sonPersonnalise;

    @Column(name = "vibration_activee")
    private Boolean vibrationActivee = true;

    @Column(name = "priorite_minimum")
    @Enumerated(EnumType.STRING)
    private PrioriteNotification prioriteMinimum = PrioriteNotification.FAIBLE;

    @Column(name = "mots_cles_filtre")
    private String motsClesFiltre; // JSON array de mots-clés

    @Column(name = "expediteurs_bloques")
    private String expediteursBloques; // JSON array d'IDs d'utilisateurs

    @Column(name = "template_personnalise")
    private String templatePersonnalise;

    @CreatedDate
    @Column(name = "date_creation", nullable = false)
    private LocalDateTime dateCreation;

    @LastModifiedDate
    @Column(name = "date_modification")
    private LocalDateTime dateModification;

    // Constructeurs
    public NotificationPreference() {}

    public NotificationPreference(Long utilisateurId, TypeNotification typeNotification, 
                                CanalNotification canal, Boolean active) {
        this.utilisateurId = utilisateurId;
        this.typeNotification = typeNotification;
        this.canal = canal;
        this.active = active;
    }

    // Méthodes métier
    public void activer() {
        this.active = true;
    }

    public void desactiver() {
        this.active = false;
    }

    public void configurerPeriodeSilencieuse(LocalTime debut, LocalTime fin) {
        this.heureDebutSilencieux = debut;
        this.heureFinSilencieux = fin;
    }

    public void configurerJoursSilencieux(String jours) {
        this.joursSilencieux = jours;
    }

    public void limiterFrequence(Integer maxParHeure) {
        this.frequenceMaxParHeure = maxParHeure;
    }

    public void activerGroupement(Integer delaiMinutes) {
        this.grouperNotifications = true;
        this.delaiGroupement = delaiMinutes;
    }

    public void desactiverGroupement() {
        this.grouperNotifications = false;
        this.delaiGroupement = null;
    }

    public boolean estActif() {
        return this.active;
    }

    public boolean estDansPeriodeSilencieuse() {
        if (this.heureDebutSilencieux == null || this.heureFinSilencieux == null) {
            return false;
        }
        
        LocalTime maintenant = LocalTime.now();
        
        // Période normale (ex: 22:00 - 08:00 du lendemain)
        if (this.heureDebutSilencieux.isAfter(this.heureFinSilencieux)) {
            return maintenant.isAfter(this.heureDebutSilencieux) || 
                   maintenant.isBefore(this.heureFinSilencieux);
        }
        // Période dans la même journée (ex: 12:00 - 14:00)
        else {
            return maintenant.isAfter(this.heureDebutSilencieux) && 
                   maintenant.isBefore(this.heureFinSilencieux);
        }
    }

    public boolean doitGrouperNotifications() {
        return this.grouperNotifications;
    }

    public boolean respectePrioriteMinimum(PrioriteNotification priorite) {
        if (this.prioriteMinimum == null) return true;
        
        // Ordre de priorité: FAIBLE < NORMALE < ELEVEE < URGENTE
        return priorite.ordinal() >= this.prioriteMinimum.ordinal();
    }

    // Getters et Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUtilisateurId() {
        return utilisateurId;
    }

    public void setUtilisateurId(Long utilisateurId) {
        this.utilisateurId = utilisateurId;
    }

    public TypeNotification getTypeNotification() {
        return typeNotification;
    }

    public void setTypeNotification(TypeNotification typeNotification) {
        this.typeNotification = typeNotification;
    }

    public CanalNotification getCanal() {
        return canal;
    }

    public void setCanal(CanalNotification canal) {
        this.canal = canal;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public LocalTime getHeureDebutSilencieux() {
        return heureDebutSilencieux;
    }

    public void setHeureDebutSilencieux(LocalTime heureDebutSilencieux) {
        this.heureDebutSilencieux = heureDebutSilencieux;
    }

    public LocalTime getHeureFinSilencieux() {
        return heureFinSilencieux;
    }

    public void setHeureFinSilencieux(LocalTime heureFinSilencieux) {
        this.heureFinSilencieux = heureFinSilencieux;
    }

    public String getJoursSilencieux() {
        return joursSilencieux;
    }

    public void setJoursSilencieux(String joursSilencieux) {
        this.joursSilencieux = joursSilencieux;
    }

    public Integer getFrequenceMaxParHeure() {
        return frequenceMaxParHeure;
    }

    public void setFrequenceMaxParHeure(Integer frequenceMaxParHeure) {
        this.frequenceMaxParHeure = frequenceMaxParHeure;
    }

    public Boolean getGrouperNotifications() {
        return grouperNotifications;
    }

    public void setGrouperNotifications(Boolean grouperNotifications) {
        this.grouperNotifications = grouperNotifications;
    }

    public Integer getDelaiGroupement() {
        return delaiGroupement;
    }

    public void setDelaiGroupement(Integer delaiGroupement) {
        this.delaiGroupement = delaiGroupement;
    }

    public String getSonPersonnalise() {
        return sonPersonnalise;
    }

    public void setSonPersonnalise(String sonPersonnalise) {
        this.sonPersonnalise = sonPersonnalise;
    }

    public Boolean getVibrationActivee() {
        return vibrationActivee;
    }

    public void setVibrationActivee(Boolean vibrationActivee) {
        this.vibrationActivee = vibrationActivee;
    }

    public PrioriteNotification getPrioriteMinimum() {
        return prioriteMinimum;
    }

    public void setPrioriteMinimum(PrioriteNotification prioriteMinimum) {
        this.prioriteMinimum = prioriteMinimum;
    }

    public String getMotsClesFiltre() {
        return motsClesFiltre;
    }

    public void setMotsClesFiltre(String motsClesFiltre) {
        this.motsClesFiltre = motsClesFiltre;
    }

    public String getExpediteursBloques() {
        return expediteursBloques;
    }

    public void setExpediteursBloques(String expediteursBloques) {
        this.expediteursBloques = expediteursBloques;
    }

    public String getTemplatePersonnalise() {
        return templatePersonnalise;
    }

    public void setTemplatePersonnalise(String templatePersonnalise) {
        this.templatePersonnalise = templatePersonnalise;
    }

    public LocalDateTime getDateCreation() {
        return dateCreation;
    }

    public void setDateCreation(LocalDateTime dateCreation) {
        this.dateCreation = dateCreation;
    }

    public LocalDateTime getDateModification() {
        return dateModification;
    }

    public void setDateModification(LocalDateTime dateModification) {
        this.dateModification = dateModification;
    }

    @Override
    public String toString() {
        return "NotificationPreference{" +
                "id=" + id +
                ", utilisateurId=" + utilisateurId +
                ", typeNotification=" + typeNotification +
                ", canal=" + canal +
                ", active=" + active +
                ", prioriteMinimum=" + prioriteMinimum +
                '}';
    }
}
