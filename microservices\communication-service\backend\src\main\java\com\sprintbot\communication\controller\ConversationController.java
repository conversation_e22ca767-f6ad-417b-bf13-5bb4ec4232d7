package com.sprintbot.communication.controller;

import com.sprintbot.communication.entity.Conversation;
import com.sprintbot.communication.entity.RoleParticipant;
import com.sprintbot.communication.entity.TypeConversation;
import com.sprintbot.communication.service.ConversationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * Contrôleur REST pour la gestion des conversations
 */
@RestController
@RequestMapping("/api/conversations")
@CrossOrigin(origins = {"http://localhost:4200", "http://localhost:4204"})
public class ConversationController {

    private static final Logger logger = LoggerFactory.getLogger(ConversationController.class);

    @Autowired
    private ConversationService conversationService;

    /**
     * Crée une nouvelle conversation
     */
    @PostMapping
    public ResponseEntity<?> creerConversation(@Valid @RequestBody CreerConversationRequest request) {
        try {
            Conversation conversation = conversationService.creerConversation(
                    request.nom(),
                    request.type(),
                    request.createurId(),
                    request.description(),
                    request.participantIds()
            );

            return ResponseEntity.status(HttpStatus.CREATED).body(conversation);

        } catch (Exception e) {
            logger.error("Erreur lors de la création de conversation: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Obtient ou crée une conversation privée
     */
    @PostMapping("/privee")
    public ResponseEntity<?> obtenirConversationPrivee(@RequestBody ConversationPriveeRequest request) {
        try {
            Conversation conversation = conversationService.obtenirConversationPrivee(
                    request.utilisateur1Id(), request.utilisateur2Id());

            return ResponseEntity.ok(conversation);

        } catch (Exception e) {
            logger.error("Erreur lors de l'obtention de conversation privée: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Obtient les conversations d'un utilisateur
     */
    @GetMapping("/utilisateur/{utilisateurId}")
    public ResponseEntity<?> obtenirConversationsUtilisateur(
            @PathVariable Long utilisateurId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Conversation> conversations = conversationService
                    .obtenirConversationsUtilisateur(utilisateurId, pageable);

            return ResponseEntity.ok(conversations);

        } catch (Exception e) {
            logger.error("Erreur lors de l'obtention des conversations: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Recherche des conversations
     */
    @GetMapping("/rechercher")
    public ResponseEntity<?> rechercherConversations(
            @RequestParam Long utilisateurId,
            @RequestParam String recherche) {
        try {
            List<Conversation> conversations = conversationService
                    .rechercherConversations(utilisateurId, recherche);

            return ResponseEntity.ok(conversations);

        } catch (Exception e) {
            logger.error("Erreur lors de la recherche de conversations: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Ajoute un participant à une conversation
     */
    @PostMapping("/{conversationId}/participants")
    public ResponseEntity<?> ajouterParticipant(
            @PathVariable Long conversationId,
            @RequestBody AjouterParticipantRequest request) {
        try {
            conversationService.ajouterParticipant(
                    conversationId, 
                    request.utilisateurId(), 
                    request.role()
            );

            return ResponseEntity.ok(Map.of("message", "Participant ajouté avec succès"));

        } catch (Exception e) {
            logger.error("Erreur lors de l'ajout de participant: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Supprime un participant d'une conversation
     */
    @DeleteMapping("/{conversationId}/participants/{utilisateurId}")
    public ResponseEntity<?> supprimerParticipant(
            @PathVariable Long conversationId,
            @PathVariable Long utilisateurId,
            @RequestParam Long demandeurId) {
        try {
            conversationService.supprimerParticipant(conversationId, utilisateurId, demandeurId);

            return ResponseEntity.ok(Map.of("message", "Participant supprimé avec succès"));

        } catch (Exception e) {
            logger.error("Erreur lors de la suppression de participant: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Archive une conversation
     */
    @PutMapping("/{conversationId}/archiver")
    public ResponseEntity<?> archiverConversation(
            @PathVariable Long conversationId,
            @RequestParam Long utilisateurId) {
        try {
            conversationService.archiverConversation(conversationId, utilisateurId);

            return ResponseEntity.ok(Map.of("message", "Conversation archivée avec succès"));

        } catch (Exception e) {
            logger.error("Erreur lors de l'archivage de conversation: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Épingle/désépingle une conversation
     */
    @PutMapping("/{conversationId}/epingler")
    public ResponseEntity<?> toggleEpingleConversation(
            @PathVariable Long conversationId,
            @RequestParam Long utilisateurId) {
        try {
            conversationService.toggleEpingleConversation(conversationId, utilisateurId);

            return ResponseEntity.ok(Map.of("message", "Statut épinglé mis à jour"));

        } catch (Exception e) {
            logger.error("Erreur lors de l'épinglage de conversation: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Met à jour les paramètres de notification
     */
    @PutMapping("/{conversationId}/notifications")
    public ResponseEntity<?> mettreAJourNotifications(
            @PathVariable Long conversationId,
            @RequestParam Long utilisateurId,
            @RequestParam boolean activer) {
        try {
            conversationService.mettreAJourNotifications(conversationId, utilisateurId, activer);

            return ResponseEntity.ok(Map.of("message", "Notifications mises à jour"));

        } catch (Exception e) {
            logger.error("Erreur lors de la mise à jour des notifications: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Marque une conversation comme lue
     */
    @PutMapping("/{conversationId}/marquer-lue")
    public ResponseEntity<?> marquerCommeLue(
            @PathVariable Long conversationId,
            @RequestParam Long utilisateurId) {
        try {
            conversationService.marquerCommeLue(conversationId, utilisateurId);

            return ResponseEntity.ok(Map.of("message", "Conversation marquée comme lue"));

        } catch (Exception e) {
            logger.error("Erreur lors du marquage comme lue: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Obtient les conversations avec messages non lus
     */
    @GetMapping("/non-lues/{utilisateurId}")
    public ResponseEntity<?> obtenirConversationsAvecMessagesNonLus(@PathVariable Long utilisateurId) {
        try {
            List<Conversation> conversations = conversationService
                    .obtenirConversationsAvecMessagesNonLus(utilisateurId);

            return ResponseEntity.ok(conversations);

        } catch (Exception e) {
            logger.error("Erreur lors de l'obtention des conversations non lues: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    /**
     * Obtient les statistiques d'une conversation
     */
    @GetMapping("/{conversationId}/statistiques")
    public ResponseEntity<?> obtenirStatistiquesConversation(@PathVariable Long conversationId) {
        try {
            Object[] stats = conversationService.obtenirStatistiquesConversation(conversationId);

            Map<String, Object> statistiques = Map.of(
                    "nombreParticipants", stats[0],
                    "nombreMessages", stats[1],
                    "derniereActivite", stats[2]
            );

            return ResponseEntity.ok(statistiques);

        } catch (Exception e) {
            logger.error("Erreur lors de l'obtention des statistiques: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("erreur", e.getMessage()));
        }
    }

    // Records pour les requêtes
    public record CreerConversationRequest(
            String nom,
            TypeConversation type,
            Long createurId,
            String description,
            List<Long> participantIds
    ) {}

    public record ConversationPriveeRequest(
            Long utilisateur1Id,
            Long utilisateur2Id
    ) {}

    public record AjouterParticipantRequest(
            Long utilisateurId,
            RoleParticipant role
    ) {}
}
