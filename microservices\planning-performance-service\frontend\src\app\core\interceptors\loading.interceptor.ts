import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent } from '@angular/common/http';
import { Observable } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { LoadingService } from '../services/loading.service';

@Injectable()
export class LoadingInterceptor implements HttpInterceptor {

  constructor(private loadingService: LoadingService) {}

  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // Démarrer le chargement
    this.loadingService.startLoading();

    return next.handle(request).pipe(
      finalize(() => {
        // Arrêter le chargement quand la requête est terminée (succès ou erreur)
        this.loadingService.stopLoading();
      })
    );
  }
}
