Write-Host "=== DEMARRAGE DU PROJET COMPLET ===" -ForegroundColor Green
Write-Host "Club Olympique de Kelibia" -ForegroundColor Cyan
Write-Host ""

Write-Host "1. DEMARRAGE DISCOVERY SERVICE" -ForegroundColor Yellow
Write-Host "===============================" -ForegroundColor Yellow

$discoveryPath = "microservices\discovery-service\backend"
Set-Location $discoveryPath
Write-Host "Demarrage Discovery Service (port 8761)..." -ForegroundColor Cyan
Start-Process powershell -ArgumentList "-Command", "mvn spring-boot:run" -WindowStyle Normal
Write-Host "Discovery Service demarre en arriere-plan" -ForegroundColor Green

Start-Sleep -Seconds 10

Write-Host ""
Write-Host "2. DEMARRAGE GATEWAY SERVICE" -ForegroundColor Yellow
Write-Host "=============================" -ForegroundColor Yellow

Set-Location "..\..\gateway-service\backend"
Write-Host "Demarrage Gateway Service (port 8080)..." -ForegroundColor Cyan
Start-Process powershell -ArgumentList "-Command", "mvn spring-boot:run" -WindowStyle Normal
Write-Host "Gateway Service demarre en arriere-plan" -ForegroundColor Green

Start-Sleep -Seconds 10

Write-Host ""
Write-Host "3. DEMARRAGE AUTH USER SERVICE" -ForegroundColor Yellow
Write-Host "===============================" -ForegroundColor Yellow

Set-Location "..\..\auth-user-service\backend"
Write-Host "Demarrage Auth User Service (port 8081)..." -ForegroundColor Cyan
Start-Process powershell -ArgumentList "-Command", "mvn spring-boot:run" -WindowStyle Normal
Write-Host "Auth User Service demarre en arriere-plan" -ForegroundColor Green

Start-Sleep -Seconds 10

Write-Host ""
Write-Host "4. DEMARRAGE PLANNING PERFORMANCE SERVICE" -ForegroundColor Yellow
Write-Host "==========================================" -ForegroundColor Yellow

Set-Location "..\..\planning-performance-service\backend"
Write-Host "Demarrage Planning Performance Service (port 8082)..." -ForegroundColor Cyan
Start-Process powershell -ArgumentList "-Command", "mvn spring-boot:run" -WindowStyle Normal
Write-Host "Planning Performance Service demarre en arriere-plan" -ForegroundColor Green

Start-Sleep -Seconds 15

Write-Host ""
Write-Host "5. DEMARRAGE FRONTEND ANGULAR" -ForegroundColor Yellow
Write-Host "==============================" -ForegroundColor Yellow

Set-Location "..\frontend"
Write-Host "Demarrage Frontend Angular (port 4201)..." -ForegroundColor Cyan
Start-Process powershell -ArgumentList "-Command", "npm start" -WindowStyle Normal
Write-Host "Frontend Angular demarre en arriere-plan" -ForegroundColor Green

Write-Host ""
Write-Host "6. ATTENTE DU DEMARRAGE COMPLET" -ForegroundColor Yellow
Write-Host "================================" -ForegroundColor Yellow

Write-Host "Attente de 60 secondes pour le demarrage complet..." -ForegroundColor Cyan
Start-Sleep -Seconds 60

Write-Host ""
Write-Host "7. VERIFICATION DES SERVICES" -ForegroundColor Yellow
Write-Host "=============================" -ForegroundColor Yellow

$services = @(
    @{ Name = "Discovery Service"; Url = "http://localhost:8761" },
    @{ Name = "Gateway Service"; Url = "http://localhost:8080/actuator/health" },
    @{ Name = "Auth User Service"; Url = "http://localhost:8081/actuator/health" },
    @{ Name = "Planning Performance Service"; Url = "http://localhost:8082/actuator/health" },
    @{ Name = "Frontend Angular"; Url = "http://localhost:4201" }
)

$servicesUp = 0
foreach ($service in $services) {
    Write-Host "Test de $($service.Name)..." -ForegroundColor Cyan
    
    try {
        $response = Invoke-WebRequest -Uri $service.Url -UseBasicParsing -TimeoutSec 5
        if ($response.StatusCode -eq 200) {
            Write-Host "OK - $($service.Name) operationnel" -ForegroundColor Green
            $servicesUp++
        }
    } catch {
        Write-Host "ERREUR - $($service.Name) non accessible" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "RESULTAT : $servicesUp/5 services operationnels" -ForegroundColor Yellow

Write-Host ""
Write-Host "=== URLS DISPONIBLES ===" -ForegroundColor Green
Write-Host ""
Write-Host "Frontend Principal : http://localhost:4201" -ForegroundColor White
Write-Host "Discovery Console  : http://localhost:8761" -ForegroundColor White
Write-Host "Gateway API        : http://localhost:8080" -ForegroundColor White
Write-Host "Auth API           : http://localhost:8081" -ForegroundColor White
Write-Host "Planning API       : http://localhost:8082" -ForegroundColor White
Write-Host ""

if ($servicesUp -ge 4) {
    Write-Host "SUCCES ! Le projet est operationnel !" -ForegroundColor Green
    Write-Host "Vous pouvez maintenant acceder au frontend : http://localhost:4201" -ForegroundColor Cyan
} else {
    Write-Host "ATTENTION : Certains services ne sont pas encore demarres." -ForegroundColor Yellow
    Write-Host "Attendez quelques minutes et verifiez les URLs ci-dessus." -ForegroundColor White
}

Write-Host ""
Write-Host "Club Olympique de Kelibia - Projet demarre !" -ForegroundColor Yellow

# Retourner au repertoire racine
Set-Location "..\..\..\.."

Read-Host "Appuyez sur Entree pour continuer"
