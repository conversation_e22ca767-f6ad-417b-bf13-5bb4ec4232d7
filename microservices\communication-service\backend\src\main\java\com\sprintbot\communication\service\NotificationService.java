package com.sprintbot.communication.service;

import com.sprintbot.communication.entity.*;
import com.sprintbot.communication.repository.NotificationRepository;
import com.sprintbot.communication.repository.NotificationPreferenceRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Service pour la gestion des notifications
 */
@Service
@Transactional
public class NotificationService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationService.class);

    @Autowired
    private NotificationRepository notificationRepository;

    @Autowired
    private NotificationPreferenceRepository preferenceRepository;

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @Autowired
    private JavaMailSender mailSender;

    @Autowired
    private PushNotificationService pushNotificationService;

    @Value("${app.notification.email.from:<EMAIL>}")
    private String emailFrom;

    @Value("${app.notification.max-retry:3}")
    private int maxRetry;

    /**
     * Crée une notification
     */
    public Notification creerNotification(Long destinataireId, TypeNotification type, 
                                        CanalNotification canal, String titre, String contenu) {
        return creerNotification(destinataireId, type, canal, titre, contenu, 
                               PrioriteNotification.NORMALE, null, null);
    }

    /**
     * Crée une notification avec tous les paramètres
     */
    public Notification creerNotification(Long destinataireId, TypeNotification type, 
                                        CanalNotification canal, String titre, String contenu,
                                        PrioriteNotification priorite, LocalDateTime dateProgrammee,
                                        Map<String, Object> donnees) {
        
        // Vérifier les préférences utilisateur
        if (!utilisateurAccepteNotification(destinataireId, type, canal)) {
            logger.debug("Notification ignorée selon les préférences utilisateur {} pour type {} canal {}", 
                        destinataireId, type, canal);
            return null;
        }

        Notification notification = new Notification(destinataireId, type, canal, titre, contenu);
        notification.setPriorite(priorite);
        notification.setDateProgrammee(dateProgrammee);
        
        if (donnees != null) {
            notification.setDonnees(donnees);
        }

        notification = notificationRepository.save(notification);

        // Envoyer immédiatement si pas programmée
        if (dateProgrammee == null || dateProgrammee.isBefore(LocalDateTime.now())) {
            envoyerNotification(notification);
        }

        logger.info("Notification créée: ID {} pour utilisateur {} type {} canal {}", 
                   notification.getId(), destinataireId, type, canal);

        return notification;
    }

    /**
     * Envoie une notification
     */
    @Async
    public void envoyerNotification(Notification notification) {
        try {
            notification.marquerEnCours();
            notificationRepository.save(notification);

            boolean succes = false;
            
            switch (notification.getCanal()) {
                case PUSH:
                    succes = envoyerNotificationPush(notification);
                    break;
                case EMAIL:
                    succes = envoyerNotificationEmail(notification);
                    break;
                case SMS:
                    succes = envoyerNotificationSMS(notification);
                    break;
                case INTERNE:
                    succes = envoyerNotificationInterne(notification);
                    break;
                case WEBHOOK:
                    succes = envoyerNotificationWebhook(notification);
                    break;
            }

            if (succes) {
                notification.marquerEnvoye();
                logger.info("Notification {} envoyée avec succès", notification.getId());
            } else {
                notification.marquerErreur("Échec d'envoi");
                logger.error("Échec d'envoi de la notification {}", notification.getId());
            }

        } catch (Exception e) {
            notification.marquerErreur(e.getMessage());
            logger.error("Erreur lors de l'envoi de la notification {}: {}", 
                        notification.getId(), e.getMessage(), e);
        } finally {
            notificationRepository.save(notification);
        }
    }

    /**
     * Envoie une notification push
     */
    private boolean envoyerNotificationPush(Notification notification) {
        try {
            // Convertir les données String en Map si nécessaire
            java.util.Map<String, Object> donneesMap = new java.util.HashMap<>();
            if (notification.getDonnees() != null && !notification.getDonnees().isEmpty()) {
                donneesMap.put("data", notification.getDonnees());
            }

            return pushNotificationService.envoyerNotification(
                    notification.getDestinataireId(),
                    notification.getTitre(),
                    notification.getContenu(),
                    donneesMap
            );
        } catch (Exception e) {
            logger.error("Erreur notification push: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Envoie une notification email
     */
    private boolean envoyerNotificationEmail(Notification notification) {
        try {
            // Récupérer l'email de l'utilisateur (appel vers auth-user-service)
            String emailDestinataire = obtenirEmailUtilisateur(notification.getDestinataireId());
            if (emailDestinataire == null) {
                logger.warn("Email non trouvé pour l'utilisateur {}", notification.getDestinataireId());
                return false;
            }

            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(emailFrom);
            message.setTo(emailDestinataire);
            message.setSubject(notification.getTitre());
            message.setText(notification.getContenu());

            mailSender.send(message);
            return true;

        } catch (Exception e) {
            logger.error("Erreur notification email: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Envoie une notification SMS
     */
    private boolean envoyerNotificationSMS(Notification notification) {
        try {
            // Récupérer le téléphone de l'utilisateur (appel vers auth-user-service)
            String telephoneDestinataire = obtenirTelephoneUtilisateur(notification.getDestinataireId());
            if (telephoneDestinataire == null) {
                logger.warn("Téléphone non trouvé pour l'utilisateur {}", notification.getDestinataireId());
                return false;
            }

            // Intégration avec service SMS (Twilio, etc.)
            // TODO: Implémenter l'envoi SMS
            logger.info("SMS envoyé à {} pour notification {}", telephoneDestinataire, notification.getId());
            return true;

        } catch (Exception e) {
            logger.error("Erreur notification SMS: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Envoie une notification interne (WebSocket)
     */
    private boolean envoyerNotificationInterne(Notification notification) {
        try {
            String destination = "/topic/user/" + notification.getDestinataireId() + "/notifications";
            messagingTemplate.convertAndSend(destination, notification);
            return true;

        } catch (Exception e) {
            logger.error("Erreur notification interne: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Marque une notification comme lue
     */
    public void marquerCommeLue(Long notificationId, Long utilisateurId) {
        Optional<Notification> notificationOpt = notificationRepository.findById(notificationId);
        if (notificationOpt.isEmpty()) {
            throw new RuntimeException("Notification non trouvée: " + notificationId);
        }

        Notification notification = notificationOpt.get();

        // Vérifier que l'utilisateur est le destinataire
        if (!notification.getDestinataireId().equals(utilisateurId)) {
            throw new RuntimeException("Permission refusée pour marquer cette notification comme lue");
        }

        notification.marquerLue();
        notificationRepository.save(notification);

        logger.debug("Notification {} marquée comme lue par l'utilisateur {}", notificationId, utilisateurId);
    }

    /**
     * Obtient les notifications d'un utilisateur
     */
    @Transactional(readOnly = true)
    public Page<Notification> obtenirNotificationsUtilisateur(Long utilisateurId, Pageable pageable) {
        return notificationRepository.findByDestinataireIdOrderByDateCreationDesc(utilisateurId, pageable);
    }

    /**
     * Obtient les notifications non lues d'un utilisateur
     */
    @Transactional(readOnly = true)
    public List<Notification> obtenirNotificationsNonLues(Long utilisateurId) {
        return notificationRepository.findByDestinataireIdAndStatutNotOrderByDateCreationDesc(
                utilisateurId, StatutNotification.LU);
    }

    /**
     * Compte les notifications non lues
     */
    @Transactional(readOnly = true)
    public Long compterNotificationsNonLues(Long utilisateurId) {
        List<StatutNotification> statutsNonLus = List.of(
                StatutNotification.EN_ATTENTE,
                StatutNotification.ENVOYE
        );
        return notificationRepository.countByDestinataireIdAndStatutIn(utilisateurId, statutsNonLus);
    }

    /**
     * Supprime une notification
     */
    public void supprimerNotification(Long notificationId, Long utilisateurId) {
        Optional<Notification> notificationOpt = notificationRepository.findById(notificationId);
        if (notificationOpt.isEmpty()) {
            throw new RuntimeException("Notification non trouvée: " + notificationId);
        }

        Notification notification = notificationOpt.get();

        // Vérifier que l'utilisateur est le destinataire
        if (!notification.getDestinataireId().equals(utilisateurId)) {
            throw new RuntimeException("Permission refusée pour supprimer cette notification");
        }

        notificationRepository.delete(notification);
        logger.info("Notification {} supprimée par l'utilisateur {}", notificationId, utilisateurId);
    }

    /**
     * Traite les notifications programmées
     */
    @Transactional
    public void traiterNotificationsProgrammees() {
        List<Notification> notifications = notificationRepository
                .findNotificationsAEnvoyer(LocalDateTime.now());

        for (Notification notification : notifications) {
            envoyerNotification(notification);
        }

        logger.info("Traitement de {} notifications programmées", notifications.size());
    }

    /**
     * Réessaie les notifications en erreur
     */
    @Transactional
    public void reessayerNotificationsEnErreur() {
        List<Notification> notifications = notificationRepository.findNotificationsAReessayer();

        for (Notification notification : notifications) {
            if (notification.getNombreTentatives() < maxRetry) {
                envoyerNotification(notification);
            }
        }

        logger.info("Réessai de {} notifications en erreur", notifications.size());
    }

    /**
     * Marque les notifications expirées
     */
    @Transactional
    public void marquerNotificationsExpirees() {
        int nombreExpirees = notificationRepository.marquerNotificationsCommeExpirees(LocalDateTime.now());
        logger.info("{} notifications marquées comme expirées", nombreExpirees);
    }

    // Méthodes spécifiques pour les différents types de notifications

    public void notifierNouveauMessage(Message message, Long destinataireId) {
        String titre = "Nouveau message";
        String contenu = "Nouveau message de " + message.getExpediteurId() + 
                        " dans " + message.getConversation().getNom();
        
        creerNotification(destinataireId, TypeNotification.NOUVEAU_MESSAGE, 
                         CanalNotification.PUSH, titre, contenu);
    }

    public void notifierNouvelleConversation(Long conversationId, Long destinataireId) {
        String titre = "Nouvelle conversation";
        String contenu = "Vous avez été ajouté à une nouvelle conversation";
        
        creerNotification(destinataireId, TypeNotification.NOUVELLE_CONVERSATION, 
                         CanalNotification.INTERNE, titre, contenu);
    }

    public void notifierAjoutConversation(Long conversationId, Long destinataireId) {
        String titre = "Ajout à une conversation";
        String contenu = "Vous avez été ajouté à une conversation";
        
        creerNotification(destinataireId, TypeNotification.AJOUT_CONVERSATION, 
                         CanalNotification.INTERNE, titre, contenu);
    }

    public void notifierSuppressionConversation(Long conversationId, Long destinataireId) {
        String titre = "Suppression de conversation";
        String contenu = "Vous avez été retiré d'une conversation";
        
        creerNotification(destinataireId, TypeNotification.SUPPRESSION_CONVERSATION, 
                         CanalNotification.INTERNE, titre, contenu);
    }

    /**
     * Envoie une notification via webhook
     */
    private boolean envoyerNotificationWebhook(Notification notification) {
        try {
            logger.info("Envoi de notification webhook pour notification {}", notification.getId());

            // TODO: Implémenter l'envoi webhook
            // - Récupérer l'URL webhook de l'utilisateur
            // - Construire le payload JSON
            // - Envoyer la requête HTTP POST

            logger.info("Notification webhook envoyée pour notification {}", notification.getId());
            return true;

        } catch (Exception e) {
            logger.error("Erreur lors de l'envoi de la notification webhook pour notification {}: {}",
                        notification.getId(), e.getMessage());
            return false;
        }
    }

    /**
     * Vérifie si un utilisateur accepte un type de notification sur un canal
     */
    private boolean utilisateurAccepteNotification(Long utilisateurId, TypeNotification type,
                                                  CanalNotification canal) {
        Optional<NotificationPreference> preference = preferenceRepository
                .findByUtilisateurIdAndTypeNotificationAndCanal(utilisateurId, type, canal);
        
        return preference.map(NotificationPreference::getActive).orElse(true);
    }

    /**
     * Obtient l'email d'un utilisateur (appel vers auth-user-service)
     */
    private String obtenirEmailUtilisateur(Long utilisateurId) {
        // TODO: Appel REST vers auth-user-service
        return "user" + utilisateurId + "@example.com";
    }

    /**
     * Obtient le téléphone d'un utilisateur (appel vers auth-user-service)
     */
    private String obtenirTelephoneUtilisateur(Long utilisateurId) {
        // TODO: Appel REST vers auth-user-service
        return "+33123456789";
    }
}
