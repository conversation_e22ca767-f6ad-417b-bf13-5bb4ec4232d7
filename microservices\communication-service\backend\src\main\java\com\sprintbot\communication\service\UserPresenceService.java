package com.sprintbot.communication.service;

import com.sprintbot.communication.entity.StatutPresence;
import com.sprintbot.communication.entity.UserPresence;
import com.sprintbot.communication.repository.UserPresenceRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service pour la gestion de la présence des utilisateurs
 */
@Service
@Transactional
public class UserPresenceService {

    private static final Logger logger = LoggerFactory.getLogger(UserPresenceService.class);

    @Autowired
    private UserPresenceRepository presenceRepository;

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    private static final int TIMEOUT_INACTIVITE_MINUTES = 15;
    private static final int TIMEOUT_HORS_LIGNE_MINUTES = 30;

    /**
     * Met à jour le statut de présence d'un utilisateur
     */
    public UserPresence mettreAJourPresence(Long utilisateurId, StatutPresence statut, 
                                          String sessionId, String plateforme, boolean estMobile) {
        Optional<UserPresence> presenceOpt = presenceRepository.findByUtilisateurId(utilisateurId);
        
        UserPresence presence;
        if (presenceOpt.isPresent()) {
            presence = presenceOpt.get();
            presence.mettreAJourStatut(statut);
        } else {
            presence = new UserPresence(utilisateurId, statut);
        }

        presence.setSessionId(sessionId);
        presence.setPlateforme(plateforme);
        presence.setEstMobile(estMobile);
        presence.setDerniereActivite(LocalDateTime.now());

        presence = presenceRepository.save(presence);

        // Notifier le changement de statut en temps réel
        notifierChangementPresence(presence);

        logger.debug("Présence mise à jour pour l'utilisateur {}: {}", utilisateurId, statut);
        return presence;
    }

    /**
     * Met à jour l'activité d'un utilisateur
     */
    public void mettreAJourActivite(Long utilisateurId) {
        Optional<UserPresence> presenceOpt = presenceRepository.findByUtilisateurId(utilisateurId);
        
        if (presenceOpt.isPresent()) {
            UserPresence presence = presenceOpt.get();
            
            // Si l'utilisateur était absent, le remettre en ligne
            if (presence.getStatut() == StatutPresence.ABSENT) {
                presence.mettreAJourStatut(StatutPresence.EN_LIGNE);
                notifierChangementPresence(presence);
            }
            
            presence.setDerniereActivite(LocalDateTime.now());
            presenceRepository.save(presence);
        }
    }

    /**
     * Définit un utilisateur comme en ligne
     */
    public UserPresence definirEnLigne(Long utilisateurId, String sessionId, 
                                     String plateforme, boolean estMobile) {
        return mettreAJourPresence(utilisateurId, StatutPresence.EN_LIGNE, 
                                 sessionId, plateforme, estMobile);
    }

    /**
     * Définit un utilisateur comme hors ligne
     */
    public void definirHorsLigne(Long utilisateurId) {
        Optional<UserPresence> presenceOpt = presenceRepository.findByUtilisateurId(utilisateurId);
        
        if (presenceOpt.isPresent()) {
            UserPresence presence = presenceOpt.get();
            presence.mettreAJourStatut(StatutPresence.HORS_LIGNE);
            presence.setSessionId(null);
            presence.setDerniereActivite(LocalDateTime.now());
            
            presenceRepository.save(presence);
            notifierChangementPresence(presence);
            
            logger.debug("Utilisateur {} défini comme hors ligne", utilisateurId);
        }
    }

    /**
     * Définit un utilisateur comme absent
     */
    public void definirAbsent(Long utilisateurId) {
        Optional<UserPresence> presenceOpt = presenceRepository.findByUtilisateurId(utilisateurId);
        
        if (presenceOpt.isPresent()) {
            UserPresence presence = presenceOpt.get();
            presence.mettreAJourStatut(StatutPresence.ABSENT);
            presence.setDerniereActivite(LocalDateTime.now());
            
            presenceRepository.save(presence);
            notifierChangementPresence(presence);
            
            logger.debug("Utilisateur {} défini comme absent", utilisateurId);
        }
    }

    /**
     * Définit un utilisateur comme occupé
     */
    public void definirOccupe(Long utilisateurId) {
        Optional<UserPresence> presenceOpt = presenceRepository.findByUtilisateurId(utilisateurId);
        
        if (presenceOpt.isPresent()) {
            UserPresence presence = presenceOpt.get();
            presence.mettreAJourStatut(StatutPresence.OCCUPE);
            presence.setDerniereActivite(LocalDateTime.now());
            
            presenceRepository.save(presence);
            notifierChangementPresence(presence);
            
            logger.debug("Utilisateur {} défini comme occupé", utilisateurId);
        }
    }

    /**
     * Active/désactive le mode invisible
     */
    public void toggleModeInvisible(Long utilisateurId) {
        Optional<UserPresence> presenceOpt = presenceRepository.findByUtilisateurId(utilisateurId);
        
        if (presenceOpt.isPresent()) {
            UserPresence presence = presenceOpt.get();
            presence.setInvisible(!presence.getInvisible());
            presenceRepository.save(presence);
            
            // Notifier seulement si devient visible
            if (!presence.getInvisible()) {
                notifierChangementPresence(presence);
            }
            
            logger.debug("Mode invisible {} pour l'utilisateur {}", 
                        presence.getInvisible() ? "activé" : "désactivé", utilisateurId);
        }
    }

    /**
     * Définit un message de statut personnalisé
     */
    public void definirMessageStatut(Long utilisateurId, String message) {
        Optional<UserPresence> presenceOpt = presenceRepository.findByUtilisateurId(utilisateurId);
        
        if (presenceOpt.isPresent()) {
            UserPresence presence = presenceOpt.get();
            presence.setMessageStatut(message);
            presence.setDerniereActivite(LocalDateTime.now());
            
            presenceRepository.save(presence);
            notifierChangementPresence(presence);
            
            logger.debug("Message de statut défini pour l'utilisateur {}: {}", utilisateurId, message);
        }
    }

    /**
     * Obtient la présence d'un utilisateur
     */
    @Transactional(readOnly = true)
    public Optional<UserPresence> obtenirPresence(Long utilisateurId) {
        return presenceRepository.findByUtilisateurId(utilisateurId);
    }

    /**
     * Obtient les utilisateurs en ligne
     */
    @Transactional(readOnly = true)
    public List<UserPresence> obtenirUtilisateursEnLigne() {
        List<StatutPresence> statutsEnLigne = List.of(
                StatutPresence.EN_LIGNE, 
                StatutPresence.ABSENT, 
                StatutPresence.OCCUPE
        );
        return presenceRepository.findByStatutInAndInvisibleFalseOrderByDerniereActiviteDesc(statutsEnLigne);
    }

    /**
     * Obtient les utilisateurs actifs récemment
     */
    @Transactional(readOnly = true)
    public List<UserPresence> obtenirUtilisateursActifsRecemment(int minutes) {
        LocalDateTime depuis = LocalDateTime.now().minusMinutes(minutes);
        return presenceRepository.findUtilisateursActifsRecemment(depuis);
    }

    /**
     * Compte les utilisateurs en ligne
     */
    @Transactional(readOnly = true)
    public Long compterUtilisateursEnLigne() {
        return presenceRepository.countUtilisateursEnLigne();
    }

    /**
     * Obtient les statistiques de présence
     */
    @Transactional(readOnly = true)
    public List<Object[]> obtenirStatistiquesPresence() {
        return presenceRepository.getStatistiquesParStatut();
    }

    /**
     * Déconnecte un utilisateur par session ID
     */
    public void deconnecterParSession(String sessionId) {
        Optional<UserPresence> presenceOpt = presenceRepository.findBySessionId(sessionId);
        
        if (presenceOpt.isPresent()) {
            UserPresence presence = presenceOpt.get();
            definirHorsLigne(presence.getUtilisateurId());
            
            logger.debug("Utilisateur {} déconnecté par session {}", 
                        presence.getUtilisateurId(), sessionId);
        }
    }

    /**
     * Nettoie automatiquement les utilisateurs inactifs
     */
    @Scheduled(fixedRate = 300000) // Toutes les 5 minutes
    @Transactional
    public void nettoyerUtilisateursInactifs() {
        LocalDateTime seuilAbsent = LocalDateTime.now().minusMinutes(TIMEOUT_INACTIVITE_MINUTES);
        LocalDateTime seuilHorsLigne = LocalDateTime.now().minusMinutes(TIMEOUT_HORS_LIGNE_MINUTES);

        // Marquer comme absent les utilisateurs inactifs
        List<UserPresence> utilisateursInactifs = presenceRepository
                .findUtilisateursInactifs(seuilAbsent);

        for (UserPresence presence : utilisateursInactifs) {
            if (presence.getStatut() == StatutPresence.EN_LIGNE) {
                presence.mettreAJourStatut(StatutPresence.ABSENT);
                presenceRepository.save(presence);
                notifierChangementPresence(presence);
            }
        }

        // Marquer comme hors ligne les utilisateurs très inactifs
        int nombreHorsLigne = presenceRepository.nettoyerSessionsExpirees(seuilHorsLigne);

        if (nombreHorsLigne > 0) {
            logger.info("Nettoyage automatique: {} utilisateurs marqués comme hors ligne", nombreHorsLigne);
        }
    }

    /**
     * Notifie un changement de présence en temps réel
     */
    private void notifierChangementPresence(UserPresence presence) {
        if (presence.getInvisible()) {
            return; // Ne pas notifier si invisible
        }

        // Notifier tous les utilisateurs connectés
        String destination = "/topic/presence";
        java.util.Map<String, Object> presenceData = new java.util.HashMap<>();
        presenceData.put("utilisateurId", presence.getUtilisateurId());
        presenceData.put("statut", presence.getStatut());
        presenceData.put("messageStatut", presence.getMessageStatut());
        presenceData.put("derniereActivite", presence.getDerniereActivite());
        presenceData.put("estMobile", presence.getEstMobile());
        presenceData.put("plateforme", presence.getPlateforme());

        messagingTemplate.convertAndSend(destination, presenceData);
    }

    /**
     * Obtient les utilisateurs par plateforme
     */
    @Transactional(readOnly = true)
    public List<UserPresence> obtenirUtilisateursParPlateforme(String plateforme) {
        return presenceRepository.findByPlateformeAndStatutOrderByDerniereActiviteDesc(
                plateforme, StatutPresence.EN_LIGNE);
    }

    /**
     * Obtient les utilisateurs mobiles en ligne
     */
    @Transactional(readOnly = true)
    public List<UserPresence> obtenirUtilisateursMobilesEnLigne() {
        return presenceRepository.findByEstMobileTrueAndStatutOrderByDerniereActiviteDesc(
                StatutPresence.EN_LIGNE);
    }
}
