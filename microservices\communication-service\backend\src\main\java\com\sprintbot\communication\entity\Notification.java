package com.sprintbot.communication.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * Entité représentant une notification système
 */
@Entity
@Table(name = "notifications")
@EntityListeners(AuditingEntityListener.class)
public class Notification {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "L'ID du destinataire est obligatoire")
    @Column(name = "destinataire_id", nullable = false)
    private Long destinataireId;

    @NotBlank(message = "Le titre est obligatoire")
    @Size(max = 200, message = "Le titre ne peut pas dépasser 200 caractères")
    @Column(name = "titre", nullable = false, length = 200)
    private String titre;

    @NotBlank(message = "Le contenu est obligatoire")
    @Size(max = 1000, message = "Le contenu ne peut pas dépasser 1000 caractères")
    @Column(name = "contenu", nullable = false, length = 1000)
    private String contenu;

    @NotNull(message = "Le type de notification est obligatoire")
    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, length = 20)
    private TypeNotification type;

    @NotNull(message = "Le canal est obligatoire")
    @Enumerated(EnumType.STRING)
    @Column(name = "canal", nullable = false, length = 20)
    private CanalNotification canal;

    @NotNull(message = "Le statut est obligatoire")
    @Enumerated(EnumType.STRING)
    @Column(name = "statut", nullable = false, length = 20)
    private StatutNotification statut = StatutNotification.EN_ATTENTE;

    @NotNull(message = "La priorité est obligatoire")
    @Enumerated(EnumType.STRING)
    @Column(name = "priorite", nullable = false, length = 20)
    private PrioriteNotification priorite = PrioriteNotification.NORMALE;

    @Column(name = "icone")
    private String icone;

    @Column(name = "couleur")
    private String couleur;

    @Column(name = "url_action")
    private String urlAction;

    @Column(name = "donnees_supplementaires", columnDefinition = "TEXT")
    private String donneesSupplementaires; // JSON

    @Column(name = "date_programmee")
    private LocalDateTime dateProgrammee;

    @Column(name = "date_envoi")
    private LocalDateTime dateEnvoi;

    @Column(name = "date_lecture")
    private LocalDateTime dateLecture;

    @Column(name = "date_expiration")
    private LocalDateTime dateExpiration;

    @Column(name = "nombre_tentatives")
    private Integer nombreTentatives = 0;

    @Column(name = "derniere_erreur")
    private String derniereErreur;

    @Column(name = "template_id")
    private String templateId;

    @Column(name = "groupe_notification")
    private String groupeNotification;

    @CreatedDate
    @Column(name = "date_creation", nullable = false)
    private LocalDateTime dateCreation;

    @LastModifiedDate
    @Column(name = "date_modification")
    private LocalDateTime dateModification;

    // Constructeurs
    public Notification() {}

    public Notification(Long destinataireId, String titre, String contenu,
                       TypeNotification type, CanalNotification canal) {
        this.destinataireId = destinataireId;
        this.titre = titre;
        this.contenu = contenu;
        this.type = type;
        this.canal = canal;
        this.statut = StatutNotification.EN_ATTENTE;
        this.priorite = PrioriteNotification.NORMALE;
    }

    public Notification(Long destinataireId, TypeNotification type, CanalNotification canal,
                       String titre, String contenu) {
        this.destinataireId = destinataireId;
        this.type = type;
        this.canal = canal;
        this.titre = titre;
        this.contenu = contenu;
        this.statut = StatutNotification.EN_ATTENTE;
        this.priorite = PrioriteNotification.NORMALE;
    }

    // Méthodes métier
    public void marquerCommeEnvoyee() {
        this.statut = StatutNotification.ENVOYE;
        this.dateEnvoi = LocalDateTime.now();
    }

    public void marquerCommeLue() {
        this.statut = StatutNotification.LU;
        this.dateLecture = LocalDateTime.now();
    }

    public void marquerCommeErreur(String erreur) {
        this.statut = StatutNotification.ERREUR;
        this.derniereErreur = erreur;
        this.nombreTentatives++;
    }

    public void programmer(LocalDateTime dateEnvoi) {
        this.dateProgrammee = dateEnvoi;
        this.statut = StatutNotification.PROGRAMMEE;
    }

    public void annuler() {
        this.statut = StatutNotification.ANNULEE;
    }

    public boolean estLue() {
        return this.statut == StatutNotification.LU;
    }

    public boolean estEnvoyee() {
        return this.statut == StatutNotification.ENVOYE;
    }

    public boolean estProgrammee() {
        return this.statut == StatutNotification.PROGRAMMEE;
    }

    public boolean estExpiree() {
        return this.dateExpiration != null && 
               LocalDateTime.now().isAfter(this.dateExpiration);
    }

    public boolean peutEtreRenvoyee() {
        return this.statut == StatutNotification.ERREUR && 
               this.nombreTentatives < 3;
    }

    public boolean estUrgente() {
        return this.priorite == PrioriteNotification.URGENTE;
    }

    // Méthodes alias pour compatibilité
    public void marquerEnvoye() {
        this.marquerCommeEnvoyee();
    }

    public void marquerLue() {
        this.marquerCommeLue();
    }

    public void marquerErreur(String erreur) {
        this.marquerCommeErreur(erreur);
    }

    public void marquerEnCours() {
        this.statut = StatutNotification.EN_ATTENTE;
    }

    public void setDonnees(String donnees) {
        this.donneesSupplementaires = donnees;
    }

    public void setDonnees(java.util.Map<String, Object> donnees) {
        if (donnees != null) {
            // Conversion simple en JSON pour les données
            this.donneesSupplementaires = donnees.toString();
        }
    }

    public String getDonnees() {
        return this.donneesSupplementaires;
    }

    // Getters et Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDestinataireId() {
        return destinataireId;
    }

    public void setDestinataireId(Long destinataireId) {
        this.destinataireId = destinataireId;
    }

    public String getTitre() {
        return titre;
    }

    public void setTitre(String titre) {
        this.titre = titre;
    }

    public String getContenu() {
        return contenu;
    }

    public void setContenu(String contenu) {
        this.contenu = contenu;
    }

    public TypeNotification getType() {
        return type;
    }

    public void setType(TypeNotification type) {
        this.type = type;
    }

    public CanalNotification getCanal() {
        return canal;
    }

    public void setCanal(CanalNotification canal) {
        this.canal = canal;
    }

    public StatutNotification getStatut() {
        return statut;
    }

    public void setStatut(StatutNotification statut) {
        this.statut = statut;
    }

    public PrioriteNotification getPriorite() {
        return priorite;
    }

    public void setPriorite(PrioriteNotification priorite) {
        this.priorite = priorite;
    }

    public String getIcone() {
        return icone;
    }

    public void setIcone(String icone) {
        this.icone = icone;
    }

    public String getCouleur() {
        return couleur;
    }

    public void setCouleur(String couleur) {
        this.couleur = couleur;
    }

    public String getUrlAction() {
        return urlAction;
    }

    public void setUrlAction(String urlAction) {
        this.urlAction = urlAction;
    }

    public String getDonneesSupplementaires() {
        return donneesSupplementaires;
    }

    public void setDonneesSupplementaires(String donneesSupplementaires) {
        this.donneesSupplementaires = donneesSupplementaires;
    }

    public LocalDateTime getDateProgrammee() {
        return dateProgrammee;
    }

    public void setDateProgrammee(LocalDateTime dateProgrammee) {
        this.dateProgrammee = dateProgrammee;
    }

    public LocalDateTime getDateEnvoi() {
        return dateEnvoi;
    }

    public void setDateEnvoi(LocalDateTime dateEnvoi) {
        this.dateEnvoi = dateEnvoi;
    }

    public LocalDateTime getDateLecture() {
        return dateLecture;
    }

    public void setDateLecture(LocalDateTime dateLecture) {
        this.dateLecture = dateLecture;
    }

    public LocalDateTime getDateExpiration() {
        return dateExpiration;
    }

    public void setDateExpiration(LocalDateTime dateExpiration) {
        this.dateExpiration = dateExpiration;
    }

    public Integer getNombreTentatives() {
        return nombreTentatives;
    }

    public void setNombreTentatives(Integer nombreTentatives) {
        this.nombreTentatives = nombreTentatives;
    }

    public String getDerniereErreur() {
        return derniereErreur;
    }

    public void setDerniereErreur(String derniereErreur) {
        this.derniereErreur = derniereErreur;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public String getGroupeNotification() {
        return groupeNotification;
    }

    public void setGroupeNotification(String groupeNotification) {
        this.groupeNotification = groupeNotification;
    }

    public LocalDateTime getDateCreation() {
        return dateCreation;
    }

    public void setDateCreation(LocalDateTime dateCreation) {
        this.dateCreation = dateCreation;
    }

    public LocalDateTime getDateModification() {
        return dateModification;
    }

    public void setDateModification(LocalDateTime dateModification) {
        this.dateModification = dateModification;
    }

    @Override
    public String toString() {
        return "Notification{" +
                "id=" + id +
                ", destinataireId=" + destinataireId +
                ", titre='" + titre + '\'' +
                ", type=" + type +
                ", canal=" + canal +
                ", statut=" + statut +
                ", priorite=" + priorite +
                ", dateCreation=" + dateCreation +
                '}';
    }
}
