version: '3.8'

services:
  # Base de données de production
  finance-prod-db:
    image: postgres:15-alpine
    container_name: finance-prod-db
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-finance_prod_db}
      POSTGRES_USER: ${POSTGRES_USER:-finance_prod_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - finance_prod_db_data:/var/lib/postgresql/data
      - ./backup:/backup
      - ./backend/src/main/resources/db/migration:/docker-entrypoint-initdb.d
    ports:
      - "${DB_PORT:-5432}:5432"
    networks:
      - finance-prod-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-finance_prod_user} -d ${POSTGRES_DB:-finance_prod_db}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Redis de production
  finance-prod-redis:
    image: redis:7-alpine
    container_name: finance-prod-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - finance_prod_redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - finance-prod-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

  # Backend de production
  finance-prod-backend:
    image: sprintbot/finance-backend:${VERSION:-latest}
    container_name: finance-prod-backend
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_DATASOURCE_URL: **************************************/${POSTGRES_DB:-finance_prod_db}?currentSchema=finance
      SPRING_DATASOURCE_USERNAME: ${POSTGRES_USER:-finance_prod_user}
      SPRING_DATASOURCE_PASSWORD: ${POSTGRES_PASSWORD}
      SPRING_JPA_HIBERNATE_DDL_AUTO: validate
      SPRING_FLYWAY_ENABLED: true
      SPRING_FLYWAY_BASELINE_ON_MIGRATE: false
      REDIS_HOST: finance-prod-redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
      JWT_EXPIRATION: ${JWT_EXPIRATION:-86400}
      SMTP_HOST: ${SMTP_HOST}
      SMTP_PORT: ${SMTP_PORT:-587}
      SMTP_USERNAME: ${SMTP_USERNAME}
      SMTP_PASSWORD: ${SMTP_PASSWORD}
      CORS_ALLOWED_ORIGINS: ${CORS_ALLOWED_ORIGINS:-https://finance.sprintbot.com}
      JAVA_OPTS: "-Xmx1G -Xms512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/app/logs/"
      # Monitoring et logging
      MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: health,metrics,info
      MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS: when_authorized
      LOGGING_LEVEL_COM_SPRINTBOT_FINANCE: INFO
      LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_SECURITY: WARN
      LOGGING_FILE_NAME: /app/logs/finance-service.log
      LOGGING_PATTERN_FILE: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
      # Sécurité
      SERVER_SSL_ENABLED: ${SSL_ENABLED:-false}
      SERVER_SSL_KEY_STORE: ${SSL_KEYSTORE:-}
      SERVER_SSL_KEY_STORE_PASSWORD: ${SSL_KEYSTORE_PASSWORD:-}
      # Performance
      SPRING_DATASOURCE_HIKARI_MAXIMUM_POOL_SIZE: 20
      SPRING_DATASOURCE_HIKARI_MINIMUM_IDLE: 5
      SPRING_DATASOURCE_HIKARI_CONNECTION_TIMEOUT: 30000
      SPRING_DATASOURCE_HIKARI_IDLE_TIMEOUT: 600000
      SPRING_DATASOURCE_HIKARI_MAX_LIFETIME: 1800000
    ports:
      - "${BACKEND_PORT:-8085}:8085"
    depends_on:
      finance-prod-db:
        condition: service_healthy
      finance-prod-redis:
        condition: service_healthy
    networks:
      - finance-prod-network
    volumes:
      - finance_prod_logs:/app/logs
      - finance_prod_reports:/app/reports
      - finance_prod_uploads:/app/uploads
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8085/actuator/health || exit 1"]
      interval: 30s
      timeout: 15s
      retries: 3
      start_period: 120s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1.5G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Frontend de production
  finance-prod-frontend:
    image: sprintbot/finance-frontend:${VERSION:-latest}
    container_name: finance-prod-frontend
    environment:
      API_URL: ${API_URL:-https://api.sprintbot.com/finance}
      NODE_ENV: production
    ports:
      - "${FRONTEND_PORT:-80}:8080"
    depends_on:
      finance-prod-backend:
        condition: service_healthy
    networks:
      - finance-prod-network
    volumes:
      - finance_prod_nginx_logs:/var/log/nginx
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

  # Reverse proxy (optionnel)
  finance-nginx:
    image: nginx:alpine
    container_name: finance-nginx
    ports:
      - "${NGINX_HTTP_PORT:-80}:80"
      - "${NGINX_HTTPS_PORT:-443}:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - finance_prod_nginx_logs:/var/log/nginx
    depends_on:
      - finance-prod-frontend
      - finance-prod-backend
    networks:
      - finance-prod-network
    restart: unless-stopped

  # Monitoring avec Prometheus
  finance-prometheus:
    image: prom/prometheus:latest
    container_name: finance-prometheus
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - finance_prometheus_data:/prometheus
    networks:
      - finance-prod-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=15d'
      - '--web.enable-lifecycle'
    restart: unless-stopped

  # Grafana pour les dashboards
  finance-grafana:
    image: grafana/grafana:latest
    container_name: finance-grafana
    ports:
      - "${GRAFANA_PORT:-3000}:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin}
      GF_INSTALL_PLUGINS: grafana-piechart-panel
    volumes:
      - finance_grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - finance-prod-network
    depends_on:
      - finance-prometheus
    restart: unless-stopped

  # Service de sauvegarde automatique
  finance-backup:
    image: postgres:15-alpine
    container_name: finance-backup
    environment:
      PGPASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - ./backup:/backup
      - ./scripts/backup.sh:/backup.sh
    networks:
      - finance-prod-network
    depends_on:
      - finance-prod-db
    command: >
      sh -c "
        echo 'Service de sauvegarde démarré...' &&
        while true; do
          echo 'Début de la sauvegarde...' &&
          pg_dump -h finance-prod-db -U ${POSTGRES_USER:-finance_prod_user} -d ${POSTGRES_DB:-finance_prod_db} > /backup/finance_backup_$$(date +%Y%m%d_%H%M%S).sql &&
          echo 'Sauvegarde terminée.' &&
          find /backup -name '*.sql' -mtime +7 -delete &&
          sleep 86400
        done
      "
    restart: unless-stopped

volumes:
  finance_prod_db_data:
    driver: local
  finance_prod_redis_data:
    driver: local
  finance_prod_logs:
    driver: local
  finance_prod_reports:
    driver: local
  finance_prod_uploads:
    driver: local
  finance_prod_nginx_logs:
    driver: local
  finance_prometheus_data:
    driver: local
  finance_grafana_data:
    driver: local

networks:
  finance-prod-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Configuration des secrets (utiliser Docker Secrets en production)
secrets:
  postgres_password:
    external: true
  jwt_secret:
    external: true
  redis_password:
    external: true
