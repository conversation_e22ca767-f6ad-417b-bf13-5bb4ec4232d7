Write-Host "=== DEMARRAGE MICROSERVICE AUTH ===" -ForegroundColor Green
Write-Host "Club Olympique de Kelibia" -ForegroundColor Cyan
Write-Host ""

# Aller dans le répertoire du microservice auth
Set-Location "microservices\auth-user-service"

Write-Host "1. Arrêt des conteneurs existants..." -ForegroundColor Yellow
docker-compose down --remove-orphans

Write-Host ""
Write-Host "2. Démarrage des services..." -ForegroundColor Yellow
Write-Host "   - Base de données PostgreSQL (port 5433)" -ForegroundColor Cyan
Write-Host "   - Backend Spring Boot (port 8085)" -ForegroundColor Cyan

# Démarrer seulement la base de données et le backend
docker-compose up -d auth-user-db auth-user-service

Write-Host ""
Write-Host "3. Attente du démarrage..." -ForegroundColor Yellow
Write-Host "   Cela peut prendre 2-3 minutes..." -ForegroundColor Gray
Start-Sleep -Seconds 45

Write-Host ""
Write-Host "4. Vérification des conteneurs..." -ForegroundColor Yellow
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

Write-Host ""
Write-Host "5. Test de connectivité..." -ForegroundColor Yellow

# Test simple du backend
Write-Host "   Test du backend..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8085/actuator/health" -UseBasicParsing -TimeoutSec 10
    Write-Host "✓ Backend opérationnel - Status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "⚠ Backend encore en cours de démarrage" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "URLS DISPONIBLES :" -ForegroundColor Yellow
Write-Host "Backend API        : http://localhost:8085" -ForegroundColor White
Write-Host "Health Check       : http://localhost:8085/actuator/health" -ForegroundColor White
Write-Host "Frontend Angular   : http://localhost:55219" -ForegroundColor White

Write-Host ""
Write-Host "🎉 MICROSERVICE AUTH DÉMARRÉ !" -ForegroundColor Green
Write-Host "Attendez 2-3 minutes pour le démarrage complet." -ForegroundColor Cyan

# Retourner au répertoire racine
Set-Location "..\..\"

Read-Host "Appuyez sur Entrée pour continuer"
