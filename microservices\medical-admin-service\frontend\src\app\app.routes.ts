import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full'
  },
  {
    path: 'dashboard',
    loadComponent: () => import('@features/dashboard/dashboard.component').then(m => m.DashboardComponent)
  },
  {
    path: 'donnees-sante',
    loadChildren: () => import('@features/donnees-sante/donnees-sante.routes').then(m => m.DONNEES_SANTE_ROUTES)
  },
  {
    path: 'rendez-vous',
    loadChildren: () => import('@features/rendez-vous/rendez-vous.routes').then(m => m.RENDEZ_VOUS_ROUTES)
  },
  {
    path: 'demandes-administratives',
    loadChildren: () => import('@features/demandes-administratives/demandes-administratives.routes').then(m => m.DEMANDES_ADMINISTRATIVES_ROUTES)
  },
  {
    path: 'rapports',
    loadComponent: () => import('@features/rapports/rapports.component').then(m => m.RapportsComponent)
  },
  {
    path: '**',
    loadComponent: () => import('@shared/components/not-found/not-found.component').then(m => m.NotFoundComponent)
  }
];
