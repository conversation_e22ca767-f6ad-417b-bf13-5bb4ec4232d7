Write-Host "=== TEST FINAL DU PROJET COMPLET ===" -ForegroundColor Green
Write-Host "Club Olympique de Kelibia" -ForegroundColor Cyan
Write-Host ""

# Attendre que les services soient prêts
Write-Host "Attente de 30 secondes pour que tous les services soient prêts..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

Write-Host ""
Write-Host "VERIFICATION RAPIDE DES SERVICES :" -ForegroundColor Yellow
Write-Host "==================================" -ForegroundColor Yellow

$services = @(
    @{ Name = "Discovery Service"; Url = "http://localhost:8761" },
    @{ Name = "Gateway Service"; Url = "http://localhost:8080/actuator/health" },
    @{ Name = "Auth User Service"; Url = "http://localhost:8081/actuator/health" },
    @{ Name = "Planning Performance Service"; Url = "http://localhost:8082/actuator/health" },
    @{ Name = "Frontend Angular"; Url = "http://localhost:4201" }
)

$servicesUp = 0
foreach ($service in $services) {
    Write-Host "Test de $($service.Name)..." -ForegroundColor Cyan
    
    try {
        $response = Invoke-WebRequest -Uri $service.Url -UseBasicParsing -TimeoutSec 5
        if ($response.StatusCode -eq 200) {
            Write-Host "✓ OK - $($service.Name) operationnel" -ForegroundColor Green
            $servicesUp++
        }
    } catch {
        Write-Host "✗ ERREUR - $($service.Name) non accessible" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "RESULTAT : $servicesUp/5 services operationnels" -ForegroundColor Yellow

if ($servicesUp -ge 4) {
    Write-Host ""
    Write-Host "🎉 EXCELLENT ! Le projet est opérationnel !" -ForegroundColor Green
    Write-Host ""
    Write-Host "URLS DISPONIBLES :" -ForegroundColor Cyan
    Write-Host "==================" -ForegroundColor Cyan
    Write-Host "🏐 Frontend Principal : http://localhost:4201" -ForegroundColor White
    Write-Host "🔍 Discovery Console  : http://localhost:8761" -ForegroundColor White
    Write-Host "🚪 Gateway API        : http://localhost:8080" -ForegroundColor White
    Write-Host "🔐 Auth API           : http://localhost:8081" -ForegroundColor White
    Write-Host "📋 Planning API       : http://localhost:8082" -ForegroundColor White
    Write-Host ""
    Write-Host "COMPTE ADMINISTRATEUR PAR DEFAUT :" -ForegroundColor Yellow
    Write-Host "===================================" -ForegroundColor Yellow
    Write-Host "Username : admin" -ForegroundColor White
    Write-Host "Password : admin123" -ForegroundColor White
    Write-Host ""
    Write-Host "🚀 VOUS POUVEZ MAINTENANT UTILISER LA PLATEFORME !" -ForegroundColor Green
    Write-Host "Allez sur http://localhost:4201 et connectez-vous avec admin/admin123" -ForegroundColor Cyan
    
} elseif ($servicesUp -ge 2) {
    Write-Host ""
    Write-Host "⚠️ PARTIELLEMENT OPERATIONNEL" -ForegroundColor Yellow
    Write-Host "Certains services fonctionnent. Attendez quelques minutes de plus." -ForegroundColor White
    Write-Host "Puis testez les URLs suivantes :" -ForegroundColor White
    Write-Host "- Frontend : http://localhost:4201" -ForegroundColor Gray
    Write-Host "- Discovery : http://localhost:8761" -ForegroundColor Gray
    
} else {
    Write-Host ""
    Write-Host "❌ PROBLEME DE DEMARRAGE" -ForegroundColor Red
    Write-Host "Peu de services sont opérationnels." -ForegroundColor White
    Write-Host "Vérifiez que Java 21+ et Node.js 20+ sont installés." -ForegroundColor White
    Write-Host "Relancez le script run-project.ps1 si nécessaire." -ForegroundColor White
}

Write-Host ""
Write-Host "PROCESSUS ACTIFS :" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan

try {
    $javaProcesses = Get-Process -Name "java" -ErrorAction SilentlyContinue
    if ($javaProcesses) {
        Write-Host "☕ Processus Java : $($javaProcesses.Count)" -ForegroundColor Green
    } else {
        Write-Host "❌ Aucun processus Java" -ForegroundColor Red
    }
} catch {
    Write-Host "⚠️ Impossible de vérifier Java" -ForegroundColor Yellow
}

try {
    $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
    if ($nodeProcesses) {
        Write-Host "🟢 Processus Node.js : $($nodeProcesses.Count)" -ForegroundColor Green
    } else {
        Write-Host "❌ Aucun processus Node.js" -ForegroundColor Red
    }
} catch {
    Write-Host "⚠️ Impossible de vérifier Node.js" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== CLUB OLYMPIQUE DE KELIBIA ===" -ForegroundColor Yellow
Write-Host "Plateforme Intelligente de Gestion d'Équipe de Volley-Ball" -ForegroundColor White
Write-Host "Projet démarré avec succès ! 🏐" -ForegroundColor Green
Write-Host ""

Read-Host "Appuyez sur Entrée pour terminer"
