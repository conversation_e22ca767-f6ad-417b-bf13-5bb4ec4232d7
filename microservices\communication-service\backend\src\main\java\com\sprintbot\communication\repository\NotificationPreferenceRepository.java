package com.sprintbot.communication.repository;

import com.sprintbot.communication.entity.CanalNotification;
import com.sprintbot.communication.entity.NotificationPreference;
import com.sprintbot.communication.entity.TypeNotification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository pour l'entité NotificationPreference
 */
@Repository
public interface NotificationPreferenceRepository extends JpaRepository<NotificationPreference, Long> {

    /**
     * Trouve les préférences d'un utilisateur
     */
    List<NotificationPreference> findByUtilisateurIdOrderByTypeNotificationAsc(Long utilisateurId);

    /**
     * Trouve une préférence spécifique
     */
    Optional<NotificationPreference> findByUtilisateurIdAndTypeNotificationAndCanal(
            Long utilisateurId, TypeNotification typeNotification, CanalNotification canal);

    /**
     * Trouve les préférences actives d'un utilisateur
     */
    List<NotificationPreference> findByUtilisateurIdAndActiveTrueOrderByTypeNotificationAsc(
            Long utilisateurId);

    /**
     * Trouve les préférences par type de notification
     */
    List<NotificationPreference> findByUtilisateurIdAndTypeNotificationOrderByCanalAsc(
            Long utilisateurId, TypeNotification typeNotification);

    /**
     * Trouve les préférences par canal
     */
    List<NotificationPreference> findByUtilisateurIdAndCanalOrderByTypeNotificationAsc(
            Long utilisateurId, CanalNotification canal);

    /**
     * Trouve les préférences avec groupement activé
     */
    List<NotificationPreference> findByUtilisateurIdAndGrouperNotificationsTrueOrderByTypeNotificationAsc(
            Long utilisateurId);

    /**
     * Vérifie si un utilisateur a des préférences configurées
     */
    boolean existsByUtilisateurId(Long utilisateurId);

    /**
     * Compte les préférences actives par canal
     */
    @Query("SELECT p.canal, COUNT(p) FROM NotificationPreference p " +
           "WHERE p.utilisateurId = :utilisateurId " +
           "AND p.active = true " +
           "GROUP BY p.canal")
    List<Object[]> countPreferencesActivesParCanal(@Param("utilisateurId") Long utilisateurId);

    /**
     * Trouve les utilisateurs avec préférences pour un type et canal
     */
    @Query("SELECT p.utilisateurId FROM NotificationPreference p " +
           "WHERE p.typeNotification = :type " +
           "AND p.canal = :canal " +
           "AND p.active = true")
    List<Long> findUtilisateursAvecPreference(
            @Param("type") TypeNotification type,
            @Param("canal") CanalNotification canal);

    /**
     * Supprime toutes les préférences d'un utilisateur
     */
    void deleteByUtilisateurId(Long utilisateurId);

    /**
     * Statistiques des préférences par type
     */
    @Query("SELECT p.typeNotification, COUNT(p), " +
           "SUM(CASE WHEN p.active = true THEN 1 ELSE 0 END) as actives " +
           "FROM NotificationPreference p " +
           "WHERE p.utilisateurId = :utilisateurId " +
           "GROUP BY p.typeNotification")
    List<Object[]> getStatistiquesParType(@Param("utilisateurId") Long utilisateurId);
}
